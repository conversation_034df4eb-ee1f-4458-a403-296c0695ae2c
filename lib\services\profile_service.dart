import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/profile_update_request.dart';
import '../models/profile_update_response.dart';

/// Service for handling user profile operations
class ProfileService {
  // Base URL for the API
  static const String baseUrl = 'https://api2.eeil.online/api/v1';
  
  // API endpoints
  static const String updateProfileEndpoint = '/user/user/update';
  
  // Keys for storing data in SharedPreferences
  static const String tokenKey = 'auth_token';
  static const String userDataKey = 'user_data';
  
  // HTTP client for making requests
  final http.Client _client = http.Client();
  
  // Singleton pattern
  static final ProfileService _instance = ProfileService._internal();
  factory ProfileService() => _instance;
  ProfileService._internal();
  
  /// Update user profile with name and email
  Future<ProfileUpdateResponse> updateProfile({
    required String name,
    required String email,
    String? userId,
  }) async {
    try {
      // Log the request
      debugPrint('\n=== UPDATING PROFILE ===');
      debugPrint('Name: $name');
      debugPrint('Email: $email');
      debugPrint('User ID: $userId');
      
      // Get the token
      final token = await getToken();
      if (token == null || token.isEmpty) {
        return ProfileUpdateResponse(
          success: false,
          message: 'Authentication token not found. Please log in again.',
        );
      }
      
      // Create the request data
      final request = ProfileUpdateRequest(
        id: userId,
        name: name,
        email: email,
      );
      
      // Set headers with token
      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': 'Bearer $token',
        'Connection': 'close', // Force connection close to avoid socket reuse issues
      };
      
      // Build the URL
      final uri = Uri.parse('$baseUrl$updateProfileEndpoint');
      
      // Log the request details
      debugPrint('URL: $uri');
      debugPrint('Headers: $headers');
      debugPrint('Request data: ${request.toJson()}');
      
      // Make the request with retry
      http.Response? response;
      try {
        response = await _client
            .post(
              uri,
              headers: headers,
              body: json.encode(request.toJson()),
            )
            .timeout(const Duration(seconds: 15));
      } catch (e) {
        debugPrint('First attempt failed: $e');
        // Retry once
        response = await _client
            .post(
              uri,
              headers: headers,
              body: json.encode(request.toJson()),
            )
            .timeout(const Duration(seconds: 15));
      }
      
      // Log the response
      debugPrint('Response status: ${response.statusCode}');
      debugPrint('Response body: ${response.body}');
      
      // Parse the response
      if (response.statusCode >= 200 && response.statusCode < 300) {
        // Success response
        if (response.body.isEmpty) {
          return ProfileUpdateResponse(
            success: true,
            message: 'Profile updated successfully',
          );
        }
        
        try {
          final Map<String, dynamic> responseData = json.decode(response.body);
          
          // Update user data in local storage if successful
          if (responseData['success'] == true) {
            await _updateLocalUserData(name, email);
          }
          
          return ProfileUpdateResponse.fromJson(responseData);
        } catch (e) {
          debugPrint('Error parsing response: $e');
          return ProfileUpdateResponse(
            success: false,
            message: 'Failed to parse response: $e',
          );
        }
      } else {
        // Error response
        debugPrint('API Error: ${response.statusCode} - ${response.body}');
        
        try {
          final Map<String, dynamic> errorData = json.decode(response.body);
          return ProfileUpdateResponse(
            success: false,
            message: errorData['message'] ?? 'Server error: ${response.statusCode}',
            data: errorData,
          );
        } catch (e) {
          return ProfileUpdateResponse(
            success: false,
            message: 'Server error: ${response.statusCode}',
          );
        }
      }
    } catch (e) {
      // Handle errors
      debugPrint('Error updating profile: $e');
      
      return ProfileUpdateResponse(
        success: false,
        message: 'Failed to update profile: $e',
      );
    }
  }
  
  /// Update local user data with new name and email
  Future<void> _updateLocalUserData(String name, String email) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userDataString = prefs.getString(userDataKey);
      
      if (userDataString != null) {
        final userData = json.decode(userDataString) as Map<String, dynamic>;
        
        // Update the user data
        userData['name'] = name;
        userData['email'] = email;
        
        // Save the updated user data
        await prefs.setString(userDataKey, json.encode(userData));
        debugPrint('Local user data updated successfully');
      } else {
        debugPrint('No local user data found to update');
      }
    } catch (e) {
      debugPrint('Error updating local user data: $e');
    }
  }
  
  /// Get the stored auth token
  Future<String?> getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(tokenKey);
  }
  
  /// Get the stored user data
  Future<Map<String, dynamic>?> getUserData() async {
    final prefs = await SharedPreferences.getInstance();
    final userDataString = prefs.getString(userDataKey);
    
    if (userDataString != null) {
      try {
        return json.decode(userDataString) as Map<String, dynamic>;
      } catch (e) {
        debugPrint('Error parsing user data: $e');
      }
    }
    
    return null;
  }
  
  /// Dispose resources
  void dispose() {
    _client.close();
  }
}
