import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../utils/app_themes.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../services/notification_history_service.dart';
import '../../../models/notification_history_item.dart';

class NotificationPage extends StatefulWidget {
  const NotificationPage({super.key});

  @override
  State<NotificationPage> createState() => _NotificationPageState();
}

class _NotificationPageState extends State<NotificationPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final NotificationHistoryService _historyService =
      NotificationHistoryService();

  final List<String> _categories = [
    'All',
    'Unread',
    'Charging',
    'Payments',
    'Offers'
  ];

  // PRODUCTION READY: Only real user notifications are displayed
  // Sample/mock notifications are automatically filtered out and cleaned up
  List<NotificationHistoryItem> _notifications = [];
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _categories.length, vsync: this);
    _initializeNotificationHistory();
  }

  /// Initialize notification history service and load data
  Future<void> _initializeNotificationHistory() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      // Initialize the history service
      await _historyService.initialize();

      // Load existing notifications
      await _loadNotifications();

      // Clean up any existing sample/mock notifications from previous app versions
      await _historyService.cleanupSampleNotifications();

      // Reload notifications after cleanup
      await _loadNotifications();

      // Listen for real-time updates
      _historyService.notificationStream.listen((notifications) {
        if (mounted) {
          setState(() {
            _notifications = notifications;
          });
        }
      });

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Failed to load notifications: $e';
      });
      debugPrint('❌ Error initializing notification history: $e');
    }
  }

  /// Load notifications from storage
  Future<void> _loadNotifications() async {
    try {
      final notifications = await _historyService.getAllNotifications();
      setState(() {
        _notifications = notifications;
      });
      debugPrint('✅ Loaded ${notifications.length} notifications');
    } catch (e) {
      debugPrint('❌ Error loading notifications: $e');
      setState(() {
        _errorMessage = 'Failed to load notifications';
      });
    }
  }

  /// Refresh notifications
  Future<void> _refreshNotifications() async {
    await _loadNotifications();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  // Helper methods for notification display
  String _formatTime(DateTime time, {bool detailed = false}) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inDays > 365) {
      final years = (difference.inDays / 365).floor();
      return years == 1 ? '1 year ago' : '$years years ago';
    } else if (difference.inDays > 30) {
      final months = (difference.inDays / 30).floor();
      return months == 1 ? '1 month ago' : '$months months ago';
    } else if (difference.inDays > 0) {
      return difference.inDays == 1
          ? 'Yesterday'
          : '${difference.inDays} days ago';
    } else if (difference.inHours > 0) {
      return difference.inHours == 1
          ? '1 hour ago'
          : '${difference.inHours} hours ago';
    } else if (difference.inMinutes > 0) {
      return difference.inMinutes == 1
          ? '1 minute ago'
          : '${difference.inMinutes} minutes ago';
    } else {
      return 'Just now';
    }
  }

  // Icon and color helpers
  IconData _getIconForType(String type) {
    switch (type) {
      case 'charging':
        return Icons.battery_charging_full;
      case 'payment':
        return Icons.payment;
      case 'station':
        return Icons.ev_station;
      case 'maintenance':
        return Icons.build;
      case 'offer':
        return Icons.local_offer;
      default:
        return Icons.notifications;
    }
  }

  Color _getIconBackgroundColor(String type) {
    switch (type) {
      case 'charging':
        return Colors.green;
      case 'payment':
        return Colors.blue;
      case 'station':
        return Colors.purple;
      case 'maintenance':
        return Colors.orange;
      case 'offer':
        return Colors.pink;
      default:
        return AppThemes.primaryColor;
    }
  }

  // Show confirmation dialog for clearing all notifications
  void _showClearConfirmation() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
        title: Text(
          'Clear all notifications?',
          style: TextStyle(
            color: isDarkMode ? Colors.white : Colors.black87,
          ),
        ),
        content: Text(
          'This action cannot be undone.',
          style: TextStyle(
            color: isDarkMode ? Colors.white70 : Colors.black54,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.redAccent,
              foregroundColor: Colors.white,
            ),
            onPressed: () async {
              await _historyService.clearAllNotifications();
              if (mounted) {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: const Text('All notifications cleared'),
                    behavior: SnackBarBehavior.floating,
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10)),
                  ),
                );
              }
            },
            child: const Text('Clear All'),
          ),
        ],
      ),
    );
  }

  // Show options menu bottom sheet
  void _showOptionsMenu() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    HapticFeedback.lightImpact();

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Container(
          decoration: BoxDecoration(
            color: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                margin: const EdgeInsets.only(top: 8),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              ListTile(
                leading: Icon(
                  Icons.delete_outline,
                  color:
                      isDarkMode ? Colors.redAccent.shade100 : Colors.redAccent,
                ),
                title: Text(
                  'Clear all notifications',
                  style: TextStyle(
                    color: isDarkMode ? Colors.white : Colors.black87,
                  ),
                ),
                onTap: () {
                  Navigator.pop(context);
                  _showClearConfirmation();
                },
              ),
              ListTile(
                leading: Icon(
                  Icons.settings_outlined,
                  color: isDarkMode ? Colors.white70 : Colors.black87,
                ),
                title: Text(
                  'Notification settings',
                  style: TextStyle(
                    color: isDarkMode ? Colors.white : Colors.black87,
                  ),
                ),
                onTap: () {
                  Navigator.pop(context);
                  // Navigate to notification settings
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: const Text('Notification settings coming soon'),
                      behavior: SnackBarBehavior.floating,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10)),
                    ),
                  );
                },
              ),
              const SizedBox(height: 16),
            ],
          ),
        );
      },
    );
  }

  // Show notification details in modal bottom sheet
  void _showNotificationDetails(NotificationHistoryItem notification) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // Add haptic feedback for detail view
    HapticFeedback.mediumImpact();

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) {
        return Container(
          decoration: BoxDecoration(
            color: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          ),
          padding: const EdgeInsets.all(24),
          // Allow up to 80% of screen height
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.8,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Pull indicator
              Center(
                child: Container(
                  margin: const EdgeInsets.only(bottom: 16),
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),
              // Header with icon and title
              Row(
                children: [
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: _getIconBackgroundColor(
                              notification.type.categoryFilter)
                          .withAlpha(25),
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: Icon(
                        _getIconForType(notification.type.categoryFilter),
                        color: _getIconBackgroundColor(
                            notification.type.categoryFilter),
                        size: 24,
                      ),
                    ),
                  )
                      .animate()
                      .scale(duration: const Duration(milliseconds: 200)),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Text(
                      notification.title,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: isDarkMode ? Colors.white : Colors.black87,
                      ),
                    )
                        .animate()
                        .fadeIn(duration: const Duration(milliseconds: 300))
                        .move(
                            begin: const Offset(20, 0),
                            end: const Offset(0, 0)),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              // Timestamp with subtle animation
              Text(
                _formatTime(notification.timestamp, detailed: true),
                style: TextStyle(
                  fontSize: 14,
                  color: isDarkMode ? Colors.white60 : Colors.grey.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ).animate().fadeIn(
                  delay: const Duration(milliseconds: 100),
                  duration: const Duration(milliseconds: 300)),
              const SizedBox(height: 24),
              // Message with fade in and slide up animation
              Flexible(
                child: SingleChildScrollView(
                  child: Text(
                    notification.body,
                    style: TextStyle(
                      fontSize: 16,
                      color: isDarkMode ? Colors.white70 : Colors.black87,
                      height: 1.5,
                    ),
                  )
                      .animate()
                      .fadeIn(
                          delay: const Duration(milliseconds: 200),
                          duration: const Duration(milliseconds: 400))
                      .move(
                          delay: const Duration(milliseconds: 200),
                          begin: const Offset(0, 20),
                          end: const Offset(0, 0)),
                ),
              ),
              const SizedBox(height: 32),
              // Action buttons with animations
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  TextButton.icon(
                    onPressed: () {
                      // Remove this notification
                      HapticFeedback.mediumImpact();
                      setState(() {
                        _notifications.remove(notification);
                      });
                      Navigator.pop(context);
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: const Text('Notification deleted'),
                          behavior: SnackBarBehavior.floating,
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10)),
                          duration: const Duration(seconds: 2),
                        ),
                      );
                    },
                    icon: Icon(
                      Icons.delete_outline,
                      color: isDarkMode
                          ? Colors.redAccent.shade100
                          : Colors.redAccent,
                    ),
                    label: Text(
                      'Delete',
                      style: TextStyle(
                        color: isDarkMode
                            ? Colors.redAccent.shade100
                            : Colors.redAccent,
                      ),
                    ),
                  ).animate().fadeIn(
                      delay: const Duration(milliseconds: 300),
                      duration: const Duration(milliseconds: 300)),
                  ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppThemes.primaryColor,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 24, vertical: 12),
                    ),
                    child: const Text('Close'),
                  ).animate().fadeIn(
                      delay: const Duration(milliseconds: 300),
                      duration: const Duration(milliseconds: 300)),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // Show loading state
    if (_isLoading) {
      return Scaffold(
        backgroundColor:
            isDarkMode ? const Color(0xFF121212) : const Color(0xFFF5F7FA),
        appBar: AppBar(
          title: const Text('Notifications'),
          backgroundColor: Colors.transparent,
          elevation: 0,
        ),
        body: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    // Show error state
    if (_errorMessage != null) {
      return Scaffold(
        backgroundColor:
            isDarkMode ? const Color(0xFF121212) : const Color(0xFFF5F7FA),
        appBar: AppBar(
          title: const Text('Notifications'),
          backgroundColor: Colors.transparent,
          elevation: 0,
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.grey.shade400,
              ),
              const SizedBox(height: 16),
              Text(
                _errorMessage!,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey.shade600,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: _initializeNotificationHistory,
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor:
          isDarkMode ? const Color(0xFF121212) : const Color(0xFFF5F7FA),
      appBar: AppBar(
        title: Text(
          'Notifications',
          style:
              const TextStyle(fontWeight: FontWeight.bold, letterSpacing: 0.5),
        )
            .animate()
            .fadeIn(duration: const Duration(milliseconds: 300))
            .slideY(begin: -0.2, end: 0),
        backgroundColor: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
        elevation: 0,
        scrolledUnderElevation: 2,
        shadowColor: Colors.black38,
        actions: [
          IconButton(
            icon: const Icon(Icons.check_circle_outline),
            onPressed: () async {
              // Mark all as read
              HapticFeedback.mediumImpact();
              await _historyService.markAllAsRead();
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: const Text('All notifications marked as read'),
                    behavior: SnackBarBehavior.floating,
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10)),
                    duration: const Duration(seconds: 2),
                  ),
                );
              }
            },
            tooltip: 'Mark all as read',
          ),
          IconButton(
            icon: const Icon(Icons.more_vert),
            onPressed: () {
              _showOptionsMenu();
            },
          ),
          const SizedBox(width: 8),
        ],
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          labelColor: AppThemes.primaryColor,
          unselectedLabelColor:
              isDarkMode ? Colors.white70 : Colors.grey.shade700,
          indicatorColor: AppThemes.primaryColor,
          indicatorSize: TabBarIndicatorSize.label,
          dividerColor: Colors.transparent,
          tabAlignment: TabAlignment.start,
          tabs: _categories.map((category) => Tab(text: category)).toList(),
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: _categories.map((category) {
          // Get filtered notifications based on the category
          final List<NotificationHistoryItem> categoryNotifications;
          if (category == 'All') {
            categoryNotifications = _notifications;
          } else if (category == 'Unread') {
            categoryNotifications =
                _notifications.where((n) => !n.isRead).toList();
          } else {
            NotificationType typeFilter;
            switch (category.toLowerCase()) {
              case 'payments':
                typeFilter = NotificationType.payment;
                break;
              case 'charging':
                typeFilter = NotificationType.charging;
                break;
              case 'offers':
                typeFilter = NotificationType.offer;
                break;
              default:
                typeFilter = NotificationType.general;
            }
            categoryNotifications =
                _notifications.where((n) => n.type == typeFilter).toList();
          }

          // Display empty state if no notifications
          if (categoryNotifications.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.notifications_off_outlined,
                    size: 80,
                    color: Colors.grey.shade400,
                  )
                      .animate()
                      .scale(
                          begin: const Offset(0.8, 0.8),
                          end: const Offset(1.0, 1.0),
                          duration: const Duration(milliseconds: 300))
                      .fadeIn(duration: const Duration(milliseconds: 400)),
                  const SizedBox(height: 16),
                  Text(
                    category == 'All'
                        ? 'No notifications yet'
                        : 'No ${category.toLowerCase()} notifications',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: isDarkMode ? Colors.white70 : Colors.grey.shade700,
                    ),
                  )
                      .animate()
                      .fadeIn(
                          delay: const Duration(milliseconds: 200),
                          duration: const Duration(milliseconds: 400))
                      .move(
                          delay: const Duration(milliseconds: 200),
                          begin: const Offset(0, 20),
                          end: const Offset(0, 0)),
                  const SizedBox(height: 8),
                  Text(
                    category == 'All'
                        ? "You'll see notifications about your charging sessions, payments, and more here."
                        : 'Check back later for ${category.toLowerCase()} notifications.',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 14,
                      color: isDarkMode ? Colors.white60 : Colors.grey.shade600,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  )
                      .animate()
                      .fadeIn(
                          delay: const Duration(milliseconds: 350),
                          duration: const Duration(milliseconds: 400))
                      .move(
                          delay: const Duration(milliseconds: 350),
                          begin: const Offset(0, 20),
                          end: const Offset(0, 0)),
                ],
              ),
            );
          }

          // Display notifications list
          return RefreshIndicator(
            onRefresh: _refreshNotifications,
            child: ListView.builder(
              physics: const BouncingScrollPhysics(),
              padding: const EdgeInsets.all(16),
              itemCount: categoryNotifications.length,
              itemBuilder: (context, index) {
                final notification = categoryNotifications[index];
                final bool isRead = notification.isRead;
                final String type = notification.type.categoryFilter;

                return Padding(
                  padding: const EdgeInsets.only(bottom: 12),
                  child: Container(
                    decoration: BoxDecoration(
                      color:
                          isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withAlpha(13),
                          blurRadius: 10,
                          offset: const Offset(0, 2),
                        ),
                      ],
                      border: isRead
                          ? null
                          : Border.all(
                              color: AppThemes.primaryColor.withAlpha(128),
                              width: 1.5,
                            ),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          onTap: () async {
                            // Haptic feedback
                            HapticFeedback.lightImpact();
                            // Mark as read when tapped
                            if (!notification.isRead) {
                              await _historyService.markAsRead(notification.id);
                            }
                            // Show notification details
                            _showNotificationDetails(notification);
                          },
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Notification icon
                                Container(
                                  width: 48,
                                  height: 48,
                                  decoration: BoxDecoration(
                                    color: _getIconBackgroundColor(type)
                                        .withAlpha(25),
                                    shape: BoxShape.circle,
                                  ),
                                  child: Center(
                                    child: Icon(
                                      _getIconForType(type),
                                      color: _getIconBackgroundColor(type),
                                      size: 24,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 16),
                                // Notification content
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          Expanded(
                                            child: Text(
                                              notification.title,
                                              style: TextStyle(
                                                fontSize: 16,
                                                fontWeight: FontWeight.bold,
                                                color: isDarkMode
                                                    ? Colors.white
                                                    : Colors.black87,
                                              ),
                                            ),
                                          ),
                                          const SizedBox(width: 8),
                                          Text(
                                            notification.formattedTime,
                                            style: TextStyle(
                                              fontSize: 12,
                                              color: isDarkMode
                                                  ? Colors.white60
                                                  : Colors.grey.shade600,
                                            ),
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        notification.body,
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                        style: TextStyle(
                                          fontSize: 14,
                                          color: isDarkMode
                                              ? Colors.white70
                                              : Colors.grey.shade700,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                )
                    .animate(delay: Duration(milliseconds: 50 * index))
                    .fadeIn(duration: const Duration(milliseconds: 300))
                    .move(
                        begin: const Offset(0, 20),
                        end: const Offset(0, 0),
                        curve: Curves.easeOutQuad);
              },
            ),
          );
        }).toList(),
      ),
    );
  }
}
