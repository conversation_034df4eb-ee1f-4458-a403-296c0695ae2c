import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:ecoplug/services/persistent_charging_service.dart';

/// Deep Link Service
/// 
/// Handles deep linking from notification taps to restore charging session screen
/// and maintain seamless user experience across app states.
/// 
/// Key Features:
/// - <PERSON>les notification tap deep links
/// - Restores charging session screen state
/// - Manages navigation to charging session
/// - Integrates with persistent charging service
class DeepLinkService {
  static final DeepLinkService _instance = DeepLinkService._internal();
  factory DeepLinkService() => _instance;
  DeepLinkService._internal();

  // Method channel for deep link communication
  static const MethodChannel _channel = MethodChannel('deep_link');

  // Service state
  bool _isInitialized = false;
  
  // Callbacks
  Function()? _onOpenChargingSession;
  Function(Map<String, dynamic>)? _onDeepLinkReceived;

  // Services
  final PersistentChargingService _persistentChargingService = PersistentChargingService();

  /// Initialize the deep link service
  Future<void> initialize({
    Function()? onOpenChargingSession,
    Function(Map<String, dynamic>)? onDeepLinkReceived,
  }) async {
    if (_isInitialized) return;

    try {
      debugPrint('🔗 ===== INITIALIZING DEEP LINK SERVICE =====');

      // Store callbacks
      _onOpenChargingSession = onOpenChargingSession;
      _onDeepLinkReceived = onDeepLinkReceived;

      // Set up method channel handler
      _channel.setMethodCallHandler(_handleMethodCall);

      _isInitialized = true;
      debugPrint('✅ Deep link service initialized successfully');

    } catch (e) {
      debugPrint('❌ Error initializing deep link service: $e');
      rethrow;
    }
  }

  /// Handle method calls from native Android
  Future<void> _handleMethodCall(MethodCall call) async {
    try {
      debugPrint('🔗 Received deep link method call: ${call.method}');
      debugPrint('🔗 Arguments: ${call.arguments}');

      switch (call.method) {
        case 'openChargingSession':
          await _handleOpenChargingSession(call.arguments);
          break;
        default:
          debugPrint('⚠️ Unknown deep link method: ${call.method}');
      }

    } catch (e) {
      debugPrint('❌ Error handling deep link method call: $e');
    }
  }

  /// Handle open charging session deep link
  Future<void> _handleOpenChargingSession(Map<String, dynamic>? arguments) async {
    try {
      debugPrint('🔗 ===== HANDLING OPEN CHARGING SESSION DEEP LINK =====');

      final source = arguments?['source'] as String? ?? 'unknown';
      final timestamp = arguments?['timestamp'] as int? ?? 0;

      debugPrint('🔗 Source: $source');
      debugPrint('🔗 Timestamp: $timestamp');

      // Check if there's an active charging session
      final sessionData = _persistentChargingService.getSessionRestorationData();
      
      if (sessionData != null) {
        debugPrint('🔗 Active charging session found, restoring...');
        debugPrint('🔗 Session data: $sessionData');

        // Notify callback with session data
        _onDeepLinkReceived?.call({
          'action': 'openChargingSession',
          'source': source,
          'timestamp': timestamp,
          'sessionData': sessionData,
        });

        // Trigger charging session screen navigation
        _onOpenChargingSession?.call();

      } else {
        debugPrint('🔗 No active charging session found');
        
        // Still notify callback for potential navigation
        _onDeepLinkReceived?.call({
          'action': 'openChargingSession',
          'source': source,
          'timestamp': timestamp,
          'sessionData': null,
        });
      }

      debugPrint('✅ Open charging session deep link handled successfully');

    } catch (e) {
      debugPrint('❌ Error handling open charging session deep link: $e');
    }
  }

  /// Check if service is initialized
  bool get isInitialized => _isInitialized;

  /// Dispose the service
  void dispose() {
    debugPrint('🔗 ===== DISPOSING DEEP LINK SERVICE =====');

    _isInitialized = false;
    _onOpenChargingSession = null;
    _onDeepLinkReceived = null;

    debugPrint('✅ Deep link service disposed');
  }

  /// Manually trigger charging session navigation (for testing)
  Future<void> triggerChargingSessionNavigation() async {
    try {
      debugPrint('🔗 Manually triggering charging session navigation');

      await _handleOpenChargingSession({
        'source': 'manual_trigger',
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      });

    } catch (e) {
      debugPrint('❌ Error triggering charging session navigation: $e');
    }
  }

  /// Get current session restoration data
  Map<String, dynamic>? getCurrentSessionData() {
    return _persistentChargingService.getSessionRestorationData();
  }

  /// Check if there's an active charging session
  bool hasActiveChargingSession() {
    return _persistentChargingService.isChargingSessionActive;
  }
}
