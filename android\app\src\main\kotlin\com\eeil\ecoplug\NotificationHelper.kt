package com.eeil.ecoplug

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat

/**
 * Helper class for building notifications consistently across the app
 * Handles common notification building logic to avoid code duplication
 */
class NotificationHelper(private val context: Context) {

    companion object {
        private const val TAG = "NotificationHelper"
        const val CHARGING_CHANNEL_ID = "charging_session_custom"
        const val FCM_CHANNEL_ID = "ecoplug_fcm"
        private const val CHARGING_CHANNEL_NAME = "Charging Session (Custom)"
        private const val FCM_CHANNEL_NAME = "EcoPlug FCM Notifications"
        private const val CHARGING_CHANNEL_DESCRIPTION = "Custom pin bar style charging notifications"
        private const val FCM_CHANNEL_DESCRIPTION = "Firebase Cloud Messaging notifications from EcoPlug"
    }

    private val notificationManager = NotificationManagerCompat.from(context)

    init {
        createNotificationChannels()
    }

    /**
     * Create all notification channels required by the app
     */
    private fun createNotificationChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            Log.d(TAG, "Creating notification channels for Android 8.0+")

            // Create both channels using shared utility method
            createNotificationChannel(CHARGING_CHANNEL_ID, CHARGING_CHANNEL_NAME, CHARGING_CHANNEL_DESCRIPTION, isChargingChannel = true)
            createNotificationChannel(FCM_CHANNEL_ID, FCM_CHANNEL_NAME, FCM_CHANNEL_DESCRIPTION, isChargingChannel = false)

            Log.d(TAG, "Notification channels created successfully")
        }
    }

    /**
     * Shared utility method to create notification channels with identical settings
     * Guards against IMPORTANCE_NONE and prompts user to re-enable if disabled
     */
    fun createNotificationChannel(
        channelId: String,
        channelName: String,
        channelDescription: String,
        isChargingChannel: Boolean = false
    ) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            Log.d(TAG, "Creating notification channel: $channelId")

            val channel = NotificationChannel(
                channelId,
                channelName,
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = channelDescription
                setShowBadge(true)
                enableLights(true)
                lightColor = context.getColor(R.color.charging_green)
                enableVibration(if (isChargingChannel) false else true) // Charging channels are silent
                lockscreenVisibility = Notification.VISIBILITY_PUBLIC
                setBypassDnd(false) // Respect Do Not Disturb
                setSound(null, null) // Silent notifications as specified
            }

            notificationManager.createNotificationChannel(channel)
            Log.d(TAG, "✅ Notification channel created: $channelId")

            // Check if channel was disabled by user (IMPORTANCE_NONE)
            checkChannelImportance(channelId, channelName)
        }
    }

    /**
     * Checks if notification channel has IMPORTANCE_NONE and provides user guidance
     */
    private fun checkChannelImportance(channelId: String, channelName: String) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            try {
                val channel = notificationManager.getNotificationChannel(channelId)
                if (channel != null && channel.importance == NotificationManager.IMPORTANCE_NONE) {
                    Log.w(TAG, "⚠️ Notification channel '$channelName' is disabled (IMPORTANCE_NONE)")
                    Log.w(TAG, "💡 User guidance: Go to Settings > Apps > EcoPlug > Notifications")
                    Log.w(TAG, "   Then enable '$channelName' and set importance to 'High'")
                    promptUserToEnableNotifications(channelName)
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error checking channel importance for $channelId: ${e.message}")
            }
        }
    }

    /**
     * Provides user guidance for re-enabling disabled notifications
     */
    private fun promptUserToEnableNotifications(channelName: String) {
        Log.w(TAG, "🔔 === NOTIFICATION CHANNEL DISABLED ====")
        Log.w(TAG, "📱 The '$channelName' channel is disabled.")
        Log.w(TAG, "💡 To receive important notifications:")
        Log.w(TAG, "")
        Log.w(TAG, "   1. Open device Settings")
        Log.w(TAG, "   2. Go to Apps → EcoPlug → Notifications")
        Log.w(TAG, "   3. Find '$channelName'")
        Log.w(TAG, "   4. Enable the channel")
        Log.w(TAG, "   5. Set importance to 'High' for best experience")
        Log.w(TAG, "")
        Log.w(TAG, "🔋 This ensures you get real-time updates!")
        Log.w(TAG, "=======================================")
    }

    /**
     * Build a basic notification with EcoPlug branding
     * @param channelId The notification channel ID to use
     * @param title The notification title
     * @param message The notification message
     * @param useChargingChannel Whether to respect the charging session channel or create default FCM channel
     * @return Built notification
     */
    fun buildNotification(
        channelId: String? = null,
        title: String,
        message: String,
        useChargingChannel: Boolean = false
    ): Notification {

        // Determine which channel to use
        val finalChannelId = when {
            useChargingChannel && channelExists(CHARGING_CHANNEL_ID) -> CHARGING_CHANNEL_ID
            !channelId.isNullOrEmpty() && channelExists(channelId) -> channelId
            channelExists(FCM_CHANNEL_ID) -> FCM_CHANNEL_ID
            else -> {
                Log.w(TAG, "Using default FCM channel as fallback")
                FCM_CHANNEL_ID
            }
        }

        Log.d(TAG, "Building notification with channel: $finalChannelId")

        // Create pending intent for notification tap
        val intent = Intent(context, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        }
        val pendingIntent = PendingIntent.getActivity(
            context, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        // Build the notification
        val builder = NotificationCompat.Builder(context, finalChannelId)
            .setSmallIcon(R.drawable.ic_launcher) // Use official app launcher icon for better recognition
            .setLargeIcon(android.graphics.BitmapFactory.decodeResource(context.resources, R.drawable.ic_launcher)) // Large icon for notification content
            .setContentTitle(title)
            .setContentText(message)
            .setContentIntent(pendingIntent)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setCategory(NotificationCompat.CATEGORY_MESSAGE)
            .setAutoCancel(true)
            .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
            .setShowWhen(true)
            .setWhen(System.currentTimeMillis())
            .setColor(context.getColor(R.color.charging_green))

        // Set large icon with null-safe guard
        try {
            val largeBitmap = android.graphics.BitmapFactory.decodeResource(
                context.resources,
                R.mipmap.ic_ecoplug_logo
            )
            if (largeBitmap != null) {
                builder.setLargeIcon(largeBitmap)
            }
        } catch (e: Exception) {
            Log.w(TAG, "Failed to load large icon for notification: ${e.message}")
        }

        return builder.build()
    }

    /**
     * Check if a notification channel exists
     */
    private fun channelExists(channelId: String): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = notificationManager.getNotificationChannel(channelId)
            channel != null && channel.importance != NotificationManager.IMPORTANCE_NONE
        } else {
            true // Pre-O doesn't use channels
        }
    }

    /**
     * Show a notification
     */
    fun showNotification(notificationId: Int, notification: Notification) {
        try {
            // Check if notifications are enabled
            if (!notificationManager.areNotificationsEnabled()) {
                Log.w(TAG, "Notifications are disabled by user")
                return
            }

            notificationManager.notify(notificationId, notification)
            Log.d(TAG, "Notification shown with ID: $notificationId")

        } catch (e: Exception) {
            Log.e(TAG, "Error showing notification: ${e.message}", e)
        }
    }
}
