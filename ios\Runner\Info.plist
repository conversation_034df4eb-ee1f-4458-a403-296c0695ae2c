<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Elp</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>elp</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>io.flutter.embedded_views_preview</key>
	<true/>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>This app needs access to location to find charging stations nearby.</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>This app needs access to location to find charging stations when in the background.</string>
	<key>com.google.android.geo.API_KEY</key>
	<string>AIzaSyBCU1EjZJ8ZRmWpYxaK55nAKKcehh-cTwM</string>

	<!-- Notification permissions -->
	<key>NSUserNotificationAlertStyle</key>
	<string>alert</string>
	<key>UIBackgroundModes</key>
	<array>
		<string>background-processing</string>
		<string>background-fetch</string>
		<string>remote-notification</string>
	</array>

	<!-- PhonePe SDK Configuration -->
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>ppemerchantsdkv1</string>
		<string>ppemerchantsdkv2</string>
		<string>ppemerchantsdkv3</string>
		<string>paytmmp</string>
		<string>gpay</string>
		<!-- Cashfree SDK UPI App Schemes -->
		<string>phonepe</string>
		<string>tez</string>
		<string>bhim</string>
		<string>credpay</string>
	</array>

	<!-- URL Scheme for PhonePe callback -->
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLName</key>
			<string>ecoplugapp</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>ecoplugapp</string>
			</array>
		</dict>
	</array>
</dict>
</plist>
