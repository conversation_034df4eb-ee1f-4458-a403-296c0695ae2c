import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:http/http.dart' as http;

import '../config/google_maps_config.dart';
import '../models/route_model.dart';
import '../models/route_alternative.dart';

/// Service for Google Directions API integration
class DirectionsService {
  static const String _baseUrl = 'https://maps.googleapis.com/maps/api';

  /// Calculate route between two points using Google Directions API
  Future<RouteModel?> getDirections({
    required LatLng origin,
    required LatLng destination,
    String travelMode = 'driving',
    bool avoidTolls = false,
    bool avoidHighways = false,
    bool avoidFerries = false,
  }) async {
    debugPrint('🌐 DIRECTIONS API: === STARTING DIRECTIONS REQUEST ===');
    debugPrint('🌐 DIRECTIONS API: Origin: $origin');
    debugPrint('🌐 DIRECTIONS API: Destination: $destination');
    debugPrint('🌐 DIRECTIONS API: Travel mode: $travelMode');

    try {
      final url = Uri.parse('$_baseUrl/directions/json').replace(
        queryParameters: {
          'origin': '${origin.latitude},${origin.longitude}',
          'destination': '${destination.latitude},${destination.longitude}',
          'mode': travelMode,
          'key': GoogleMapsConfig.apiKey,
          'region': 'in', // Bias results to India
          'language': 'en',
          if (avoidTolls) 'avoid': 'tolls',
          if (avoidHighways) 'avoid': 'highways',
          if (avoidFerries) 'avoid': 'ferries',
        },
      );

      debugPrint('🌐 DIRECTIONS API: Request URL: $url');
      debugPrint('🌐 DIRECTIONS API: API Key (first 10 chars): ${GoogleMapsConfig.apiKey.substring(0, 10)}...');

      debugPrint('\n🗺️ === GOOGLE DIRECTIONS API REQUEST DEBUG ===');
      debugPrint('🌐 SERVICE: Google Directions API');
      debugPrint('📍 ORIGIN: lat=${origin.latitude}, lng=${origin.longitude}');
      debugPrint('📍 DESTINATION: lat=${destination.latitude}, lng=${destination.longitude}');
      debugPrint('🚗 TRAVEL MODE: $travelMode');
      debugPrint('🚫 AVOID TOLLS: $avoidTolls');
      debugPrint('🚫 AVOID HIGHWAYS: $avoidHighways');
      debugPrint('🚫 AVOID FERRIES: $avoidFerries');
      debugPrint('🌐 FULL URL: $url');
      debugPrint('📦 REQUEST METHOD: GET');
      debugPrint('🔐 API KEY: ${GoogleMapsConfig.apiKey.substring(0, 10)}...[HIDDEN]');
      debugPrint('🗺️ === END DIRECTIONS API REQUEST DEBUG ===\n');

      final response = await http.get(url);

      // COMPREHENSIVE DIRECTIONS API RESPONSE DEBUGGING
      if (kDebugMode) {
        debugPrint('\n📥 === GOOGLE DIRECTIONS API RESPONSE DEBUG ===');
        debugPrint('📊 STATUS CODE: ${response.statusCode}');
        debugPrint('📦 RESPONSE HEADERS:');
        response.headers.forEach((key, value) {
          debugPrint('   $key: $value');
        });
        debugPrint('📦 RESPONSE BODY LENGTH: ${response.body.length} characters');
        if (response.statusCode == 200) {
          try {
            final data = json.decode(response.body);
            debugPrint('📦 RESPONSE STATUS: ${data['status']}');
            if (data['status'] == 'OK') {
              final routes = data['routes'] as List;
              debugPrint('📦 ROUTES COUNT: ${routes.length}');
              if (routes.isNotEmpty) {
                final firstRoute = routes.first;
                final legs = firstRoute['legs'] as List;
                debugPrint('📦 LEGS COUNT: ${legs.length}');
                if (legs.isNotEmpty) {
                  final firstLeg = legs.first;
                  debugPrint('📦 DISTANCE: ${firstLeg['distance']['text']}');
                  debugPrint('📦 DURATION: ${firstLeg['duration']['text']}');
                  debugPrint('📦 STEPS COUNT: ${(firstLeg['steps'] as List).length}');
                }
                final polyline = firstRoute['overview_polyline']['points'];
                debugPrint('📦 POLYLINE LENGTH: ${polyline.toString().length} characters');
              }
            } else {
              debugPrint('📦 ERROR MESSAGE: ${data['error_message'] ?? 'No error message'}');
            }
          } catch (e) {
            debugPrint('📦 ERROR PARSING RESPONSE: $e');
            debugPrint('📦 RAW RESPONSE: ${response.body.substring(0, 500)}...');
          }
        } else {
          debugPrint('📦 ERROR RESPONSE BODY: ${response.body}');
        }
        debugPrint('📥 === END DIRECTIONS API RESPONSE DEBUG ===\n');
      }

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        if (data['status'] == 'OK') {
          final route = RouteModel.fromDirectionsResponse(data);
          debugPrint('✅ ROUTE CALCULATED: ${route.distance} in ${route.duration}');
          debugPrint('📊 POLYLINE POINTS: ${route.polylinePoints.length}');
          return route;
        } else {
          final errorMessage = data['error_message'] ?? data['status'];
          debugPrint('❌ DIRECTIONS ERROR: $errorMessage');
          return null;
        }
      } else {
        debugPrint('❌ DIRECTIONS HTTP ERROR: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      debugPrint('❌ DIRECTIONS EXCEPTION: $e');
      return null;
    }
  }

  /// Calculate route with waypoints (for multi-stop routes)
  Future<RouteModel?> getDirectionsWithWaypoints({
    required LatLng origin,
    required LatLng destination,
    required List<LatLng> waypoints,
    String travelMode = 'driving',
    bool optimizeWaypoints = true,
  }) async {
    try {
      // Convert waypoints to string format
      final waypointsString = waypoints
          .map((point) => '${point.latitude},${point.longitude}')
          .join('|');

      final url = Uri.parse('$_baseUrl/directions/json').replace(
        queryParameters: {
          'origin': '${origin.latitude},${origin.longitude}',
          'destination': '${destination.latitude},${destination.longitude}',
          'waypoints': optimizeWaypoints
              ? 'optimize:true|$waypointsString'
              : waypointsString,
          'mode': travelMode,
          'key': GoogleMapsConfig.apiKey,
          'region': 'in',
          'language': 'en',
        },
      );

      debugPrint('🗺️ DIRECTIONS API: Calculating route with waypoints');
      debugPrint('📍 WAYPOINTS: ${waypoints.length}');

      final response = await http.get(url);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        if (data['status'] == 'OK') {
          final route = RouteModel.fromDirectionsResponse(data);
          debugPrint('✅ WAYPOINT ROUTE CALCULATED: ${route.distance} in ${route.duration}');
          return route;
        } else {
          final errorMessage = data['error_message'] ?? data['status'];
          debugPrint('❌ WAYPOINT DIRECTIONS ERROR: $errorMessage');
          return null;
        }
      } else {
        debugPrint('❌ WAYPOINT DIRECTIONS HTTP ERROR: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      debugPrint('❌ WAYPOINT DIRECTIONS EXCEPTION: $e');
      return null;
    }
  }

  /// Get distance matrix between multiple origins and destinations
  Future<Map<String, dynamic>?> getDistanceMatrix({
    required List<LatLng> origins,
    required List<LatLng> destinations,
    String travelMode = 'driving',
  }) async {
    try {
      final originsString = origins
          .map((point) => '${point.latitude},${point.longitude}')
          .join('|');

      final destinationsString = destinations
          .map((point) => '${point.latitude},${point.longitude}')
          .join('|');

      final url = Uri.parse('$_baseUrl/distancematrix/json').replace(
        queryParameters: {
          'origins': originsString,
          'destinations': destinationsString,
          'mode': travelMode,
          'key': GoogleMapsConfig.apiKey,
          'region': 'in',
          'language': 'en',
        },
      );

      debugPrint('🗺️ DISTANCE MATRIX API: Calculating distances');

      final response = await http.get(url);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        if (data['status'] == 'OK') {
          debugPrint('✅ DISTANCE MATRIX CALCULATED');
          return data;
        } else {
          final errorMessage = data['error_message'] ?? data['status'];
          debugPrint('❌ DISTANCE MATRIX ERROR: $errorMessage');
          return null;
        }
      } else {
        debugPrint('❌ DISTANCE MATRIX HTTP ERROR: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      debugPrint('❌ DISTANCE MATRIX EXCEPTION: $e');
      return null;
    }
  }

  /// Calculate estimated travel time considering traffic
  Future<String?> getEstimatedTravelTime({
    required LatLng origin,
    required LatLng destination,
    DateTime? departureTime,
  }) async {
    try {
      final departureTimeSeconds = departureTime?.millisecondsSinceEpoch ??
          DateTime.now().millisecondsSinceEpoch;

      final url = Uri.parse('$_baseUrl/directions/json').replace(
        queryParameters: {
          'origin': '${origin.latitude},${origin.longitude}',
          'destination': '${destination.latitude},${destination.longitude}',
          'mode': 'driving',
          'departure_time': (departureTimeSeconds ~/ 1000).toString(),
          'traffic_model': 'best_guess',
          'key': GoogleMapsConfig.apiKey,
          'region': 'in',
        },
      );

      final response = await http.get(url);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        if (data['status'] == 'OK') {
          final routes = data['routes'] as List;
          if (routes.isNotEmpty) {
            final legs = routes.first['legs'] as List;
            if (legs.isNotEmpty) {
              final durationInTraffic = legs.first['duration_in_traffic'];
              if (durationInTraffic != null) {
                return durationInTraffic['text'] as String;
              }
              return legs.first['duration']['text'] as String;
            }
          }
        }
      }
      return null;
    } catch (e) {
      debugPrint('❌ TRAVEL TIME ESTIMATION ERROR: $e');
      return null;
    }
  }

  /// Get multiple route alternatives between two points
  Future<RouteAlternatives?> getRouteAlternatives({
    required LatLng origin,
    required LatLng destination,
    String travelMode = 'driving',
    bool avoidTolls = false,
    bool avoidHighways = false,
    bool avoidFerries = false,
  }) async {
    try {
      final url = Uri.parse('$_baseUrl/directions/json').replace(
        queryParameters: {
          'origin': '${origin.latitude},${origin.longitude}',
          'destination': '${destination.latitude},${destination.longitude}',
          'mode': travelMode,
          'alternatives': 'true', // Request alternative routes
          'key': GoogleMapsConfig.apiKey,
          'region': 'in', // Bias results to India
          'language': 'en',
          if (avoidTolls) 'avoid': 'tolls',
          if (avoidHighways) 'avoid': 'highways',
          if (avoidFerries) 'avoid': 'ferries',
        },
      );

      debugPrint('\n🗺️ === GOOGLE DIRECTIONS ALTERNATIVES API REQUEST DEBUG ===');
      debugPrint('🌐 SERVICE: Google Directions API (Alternatives)');
      debugPrint('📍 ORIGIN: lat=${origin.latitude}, lng=${origin.longitude}');
      debugPrint('📍 DESTINATION: lat=${destination.latitude}, lng=${destination.longitude}');
      debugPrint('🚗 TRAVEL MODE: $travelMode');
      debugPrint('🔄 ALTERNATIVES: true');
      debugPrint('🚫 AVOID TOLLS: $avoidTolls');
      debugPrint('🚫 AVOID HIGHWAYS: $avoidHighways');
      debugPrint('🚫 AVOID FERRIES: $avoidFerries');
      debugPrint('🌐 FULL URL: $url');
      debugPrint('📦 REQUEST METHOD: GET');
      debugPrint('🔐 API KEY: ${GoogleMapsConfig.apiKey.substring(0, 10)}...[HIDDEN]');
      debugPrint('🗺️ === END ALTERNATIVES API REQUEST DEBUG ===\n');

      final response = await http.get(url);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        if (data['status'] == 'OK') {
          final routes = data['routes'] as List;
          debugPrint('✅ ROUTE ALTERNATIVES: Found ${routes.length} routes');

          if (routes.isNotEmpty) {
            final alternatives = RouteAlternatives.fromDirectionsResponse(
              data,
              origin,
              destination,
            );

            debugPrint('🛣️ ALTERNATIVES: ${alternatives.alternatives.length} routes processed');
            for (int i = 0; i < alternatives.alternatives.length; i++) {
              final alt = alternatives.alternatives[i];
              debugPrint('   Route ${i + 1}: ${alt.route.distance} in ${alt.route.duration} (${alt.optimization.displayName})');
            }

            return alternatives;
          }
        } else {
          final errorMessage = data['error_message'] ?? data['status'];
          debugPrint('❌ ROUTE ALTERNATIVES ERROR: $errorMessage');
          return null;
        }
      } else {
        debugPrint('❌ ROUTE ALTERNATIVES HTTP ERROR: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      debugPrint('❌ ROUTE ALTERNATIVES EXCEPTION: $e');
      return null;
    }

    return null;
  }
}
