import 'dart:convert';

class VehicleResponse {
  final Map<String, List<ApiVehicle>> data;
  final bool success;

  VehicleResponse({
    required this.data,
    required this.success,
  });

  factory VehicleResponse.fromJson(Map<String, dynamic> json) {
    final Map<String, List<ApiVehicle>> vehiclesByBrand = {};
    
    if (json['data'] != null) {
      json['data'].forEach((key, value) {
        if (value is List) {
          vehiclesByBrand[key] = value
              .map((vehicle) => ApiVehicle.fromJson(vehicle))
              .toList();
        }
      });
    }

    return VehicleResponse(
      data: vehiclesByBrand,
      success: json['success'] ?? false,
    );
  }

  List<ApiVehicle> get allVehicles {
    return data['All'] ?? [];
  }

  List<String> get brands {
    return data.keys.where((key) => key != 'All').toList();
  }

  List<ApiVehicle> getVehiclesByBrand(String brand) {
    return data[brand] ?? [];
  }
}

class ApiVehicle {
  final int id;
  final String name;
  final String? variants;
  final String batteryCapacity;
  final String vehicleImage;
  final int? isActive;
  final int? brandId;
  final String? brandName;
  final String? createdAt;
  final String? updatedAt;
  final bool isSelected;

  ApiVehicle({
    required this.id,
    required this.name,
    this.variants,
    required this.batteryCapacity,
    required this.vehicleImage,
    this.isActive,
    this.brandId,
    this.brandName,
    this.createdAt,
    this.updatedAt,
    this.isSelected = false,
  });

  factory ApiVehicle.fromJson(Map<String, dynamic> json) {
    return ApiVehicle(
      id: json['id'],
      name: json['name'],
      variants: json['variants'],
      batteryCapacity: json['battery_capacity'].toString(),
      vehicleImage: json['vehicle_image'],
      isActive: json['is_active'],
      brandId: json['brand_id'],
      brandName: json['brand_name'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
    );
  }

  ApiVehicle copyWith({
    int? id,
    String? name,
    String? variants,
    String? batteryCapacity,
    String? vehicleImage,
    int? isActive,
    int? brandId,
    String? brandName,
    String? createdAt,
    String? updatedAt,
    bool? isSelected,
  }) {
    return ApiVehicle(
      id: id ?? this.id,
      name: name ?? this.name,
      variants: variants ?? this.variants,
      batteryCapacity: batteryCapacity ?? this.batteryCapacity,
      vehicleImage: vehicleImage ?? this.vehicleImage,
      isActive: isActive ?? this.isActive,
      brandId: brandId ?? this.brandId,
      brandName: brandName ?? this.brandName,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isSelected: isSelected ?? this.isSelected,
    );
  }

  List<String> getVariantsList() {
    if (variants == null || variants!.isEmpty) {
      return [];
    }
    
    try {
      // Remove extra quotes and parse the string
      String cleanVariants = variants!.replaceAll('\\', '');
      if (cleanVariants.startsWith('"') && cleanVariants.endsWith('"')) {
        cleanVariants = cleanVariants.substring(1, cleanVariants.length - 1);
      }
      
      return cleanVariants.split(',').map((v) => v.trim()).toList();
    } catch (e) {
      return [];
    }
  }
}

class VehicleRegistration {
  final String registrationNumber;
  final int vehicleId;

  VehicleRegistration({
    required this.registrationNumber,
    required this.vehicleId,
  });

  Map<String, dynamic> toJson() {
    return {
      'regno': registrationNumber,
      'vehicle_id': vehicleId,
    };
  }

  String toJsonString() {
    return jsonEncode(toJson());
  }
}
