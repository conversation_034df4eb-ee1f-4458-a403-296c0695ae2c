import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:ecoplug/services/charging_session_service.dart';

/// Service for detecting gun connection status in real-time
/// This service monitors whether the charging gun is physically connected to the vehicle
class GunConnectionService {
  // Singleton pattern
  static final GunConnectionService _instance =
      GunConnectionService._internal();
  factory GunConnectionService() => _instance;
  GunConnectionService._internal();

  final ChargingSessionService _chargingService = ChargingSessionService();

  // Stream controllers for gun connection status
  final Map<String, StreamController<bool>> _connectionControllers = {};

  // Current connection status for each connector
  final Map<String, bool> _connectionStatus = {};

  // Timers for periodic checking
  final Map<String, Timer> _connectionTimers = {};

  /// Start monitoring gun connection for a specific connector
  /// Returns a stream that emits true when gun is connected, false when disconnected
  Stream<bool> monitorGunConnection({
    required String evseUid,
    required String connectorId,
  }) {
    final String key = '${evseUid}_$connectorId';

    // Create stream controller if it doesn't exist
    if (!_connectionControllers.containsKey(key)) {
      _connectionControllers[key] = StreamController<bool>.broadcast();
      _connectionStatus[key] = false; // Default to disconnected

      // Start periodic checking
      _startPeriodicCheck(evseUid, connectorId, key);
    }

    return _connectionControllers[key]!.stream;
  }

  /// Start periodic gun connection checking
  void _startPeriodicCheck(String evseUid, String connectorId, String key) {
    // Cancel existing timer if any
    _connectionTimers[key]?.cancel();

    // Check immediately
    _checkGunConnection(evseUid, connectorId, key);

    // Set up periodic checking every 10 seconds
    _connectionTimers[key] = Timer.periodic(const Duration(seconds: 10), (_) {
      _checkGunConnection(evseUid, connectorId, key);
    });

    debugPrint('🔌 Started gun connection monitoring for $key');
  }

  /// Check gun connection status using a lightweight API call
  Future<void> _checkGunConnection(
      String evseUid, String connectorId, String key) async {
    try {
      // Use a lightweight API call to check gun connection
      // This simulates checking if the gun is physically connected
      final response = await _chargingService.startChargingSession(
        evseUid: evseUid,
        connectorId: connectorId,
        chargingValue: 0.1, // Minimal value for connection test
        instantCharging: false,
      );

      bool isConnected = false;

      if (response.success) {
        // Gun is connected - charging session can start
        isConnected = true;
        debugPrint('🔌 Gun connected for $key');
      } else {
        // Check if the error indicates gun not connected
        final errorMessage = response.message.toLowerCase();
        if (_isGunConnectionError(errorMessage)) {
          isConnected = false;
          debugPrint('🔌 Gun not connected for $key: ${response.message}');
        } else {
          // Other error - assume gun is connected but there's another issue
          isConnected = true;
          debugPrint(
              '🔌 Gun connected but other error for $key: ${response.message}');
        }
      }

      // Update status if changed
      if (_connectionStatus[key] != isConnected) {
        _connectionStatus[key] = isConnected;
        _connectionControllers[key]?.add(isConnected);
        debugPrint('🔌 Gun connection status changed for $key: $isConnected');
      }
    } catch (e) {
      debugPrint('🔌 Error checking gun connection for $key: $e');
      // On error, assume gun is not connected
      if (_connectionStatus[key] != false) {
        _connectionStatus[key] = false;
        _connectionControllers[key]?.add(false);
      }
    }
  }

  /// Check if error message indicates gun connection issue
  bool _isGunConnectionError(String errorMessage) {
    final lowerError = errorMessage.toLowerCase();

    return lowerError.contains('connector') &&
            lowerError.contains('not') &&
            lowerError.contains('connected') ||
        lowerError.contains('gun') &&
            lowerError.contains('not') &&
            lowerError.contains('connected') ||
        lowerError.contains('vehicle') &&
            lowerError.contains('not') &&
            lowerError.contains('connected') ||
        lowerError.contains('plug') &&
            lowerError.contains('not') &&
            lowerError.contains('connected') ||
        lowerError.contains('cable') &&
            lowerError.contains('not') &&
            lowerError.contains('connected') ||
        lowerError.contains('no vehicle detected') ||
        lowerError.contains('vehicle connection') ||
        lowerError.contains('charging port') ||
        lowerError.contains('connection failed') ||
        lowerError.contains('not plugged');
  }

  /// Get current gun connection status for a connector
  bool isGunConnected({
    required String evseUid,
    required String connectorId,
  }) {
    final String key = '${evseUid}_$connectorId';
    return _connectionStatus[key] ?? false;
  }

  /// Stop monitoring gun connection for a specific connector
  void stopMonitoring({
    required String evseUid,
    required String connectorId,
  }) {
    final String key = '${evseUid}_$connectorId';

    _connectionTimers[key]?.cancel();
    _connectionTimers.remove(key);

    _connectionControllers[key]?.close();
    _connectionControllers.remove(key);

    _connectionStatus.remove(key);

    debugPrint('🔌 Stopped gun connection monitoring for $key');
  }

  /// Stop all monitoring
  void stopAllMonitoring() {
    for (final timer in _connectionTimers.values) {
      timer.cancel();
    }
    _connectionTimers.clear();

    for (final controller in _connectionControllers.values) {
      controller.close();
    }
    _connectionControllers.clear();

    _connectionStatus.clear();

    debugPrint('🔌 Stopped all gun connection monitoring');
  }

  /// Dispose resources
  void dispose() {
    stopAllMonitoring();
  }
}
