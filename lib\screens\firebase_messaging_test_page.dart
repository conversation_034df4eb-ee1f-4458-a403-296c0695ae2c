import 'package:flutter/material.dart';
import '../services/messaging_service.dart';
import '../services/fcm_subscription_service.dart';
import '../widgets/messaging_integration_widget.dart';
import '../widgets/fcm_subscription_debug_widget.dart';

/// Firebase Messaging Test Page following the tutorial pattern
/// Comprehensive testing interface for FCM functionality
class FirebaseMessagingTestPage extends StatefulWidget {
  const FirebaseMessagingTestPage({super.key});

  @override
  State<FirebaseMessagingTestPage> createState() =>
      _FirebaseMessagingTestPageState();
}

class _FirebaseMessagingTestPageState extends State<FirebaseMessagingTestPage> {
  final MessagingService _messagingService = MessagingService();
  final FCMSubscriptionService _subscriptionService = FCMSubscriptionService();

  bool _isLoading = false;
  List<String> _testResults = [];

  @override
  void initState() {
    super.initState();
    _initializeServices();
  }

  /// Initialize all messaging services
  Future<void> _initializeServices() async {
    setState(() {
      _isLoading = true;
      _testResults.clear();
    });

    try {
      // Initialize messaging service
      await _messagingService.init();
      _addTestResult('✅ Messaging Service initialized successfully');

      // Initialize subscription service
      await _subscriptionService.initialize();
      _addTestResult('✅ FCM Subscription Service initialized successfully');
    } catch (e) {
      _addTestResult('❌ Error initializing services: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// Add test result to the list
  void _addTestResult(String result) {
    setState(() {
      _testResults.add(
          '${DateTime.now().toLocal().toString().substring(11, 19)} - $result');
    });
  }

  /// Test charging notification subscription
  Future<void> _testChargingSubscription() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final testTransactionId = 'TEST_${DateTime.now().millisecondsSinceEpoch}';

      _addTestResult('🔔 Testing charging notification subscription...');
      _addTestResult('📋 Transaction ID: $testTransactionId');

      // Test subscription
      final subscriptionSuccess = await _subscriptionService
          .subscribeToChargingNotifications(testTransactionId);

      if (subscriptionSuccess) {
        _addTestResult('✅ Successfully subscribed to charging notifications');

        // Wait a moment
        await Future.delayed(const Duration(seconds: 2));

        // Test unsubscription
        final unsubscriptionSuccess =
            await _subscriptionService.unsubscribeFromChargingNotifications();

        if (unsubscriptionSuccess) {
          _addTestResult(
              '✅ Successfully unsubscribed from charging notifications');
        } else {
          _addTestResult('❌ Failed to unsubscribe from charging notifications');
        }
      } else {
        _addTestResult('❌ Failed to subscribe to charging notifications');
      }
    } catch (e) {
      _addTestResult('❌ Error during charging subscription test: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// Test topic subscription
  Future<void> _testTopicSubscription() async {
    setState(() {
      _isLoading = true;
    });

    try {
      _addTestResult('🔔 Testing topic subscription...');

      // Subscribe to test topic
      await _messagingService.subscribeToTopic('ecoplug_test');
      _addTestResult('✅ Subscribed to ecoplug_test topic');

      // Wait a moment
      await Future.delayed(const Duration(seconds: 2));

      // Unsubscribe from test topic
      await _messagingService.unsubscribeFromTopic('ecoplug_test');
      _addTestResult('✅ Unsubscribed from ecoplug_test topic');
    } catch (e) {
      _addTestResult('❌ Error during topic subscription test: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// Clear test results
  void _clearResults() {
    setState(() {
      _testResults.clear();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Firebase Messaging Test'),
        backgroundColor: const Color(0xFF00BCD4),
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _clearResults,
            icon: const Icon(Icons.clear_all),
            tooltip: 'Clear Results',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Header Card
            Card(
              elevation: 4,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    const Icon(
                      Icons.cloud_queue,
                      size: 48,
                      color: Color(0xFF00BCD4),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Firebase Cloud Messaging Test',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Test FCM functionality following the tutorial pattern',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade600,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Test Actions Card
            Card(
              elevation: 4,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Test Actions',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Initialize Services Button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: _isLoading ? null : _initializeServices,
                        icon: const Icon(Icons.refresh),
                        label: const Text('Reinitialize Services'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                      ),
                    ),

                    const SizedBox(height: 12),

                    // Test Charging Subscription Button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed:
                            _isLoading ? null : _testChargingSubscription,
                        icon: const Icon(Icons.electric_car),
                        label: const Text('Test Charging Subscription'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                      ),
                    ),

                    const SizedBox(height: 12),

                    // Test Topic Subscription Button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: _isLoading ? null : _testTopicSubscription,
                        icon: const Icon(Icons.topic),
                        label: const Text('Test Topic Subscription'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orange,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Loading Indicator
            if (_isLoading)
              const Card(
                child: Padding(
                  padding: EdgeInsets.all(16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircularProgressIndicator(),
                      SizedBox(width: 16),
                      Text('Running tests...'),
                    ],
                  ),
                ),
              ),

            // Test Results Card
            if (_testResults.isNotEmpty)
              Card(
                elevation: 4,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            'Test Results',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            '${_testResults.length} results',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Container(
                        height: 200,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade100,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.grey.shade300),
                        ),
                        child: ListView.builder(
                          itemCount: _testResults.length,
                          itemBuilder: (context, index) {
                            final result = _testResults[index];
                            return Padding(
                              padding: const EdgeInsets.symmetric(vertical: 2),
                              child: Text(
                                result,
                                style: TextStyle(
                                  fontSize: 12,
                                  fontFamily: 'monospace',
                                  color: result.contains('❌')
                                      ? Colors.red
                                      : result.contains('✅')
                                          ? Colors.green
                                          : Colors.black87,
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),

            const SizedBox(height: 16),

            // Messaging Integration Widget
            const MessagingIntegrationWidget(),

            const SizedBox(height: 16),

            // FCM Subscription Debug Widget
            const FCMSubscriptionDebugWidget(),
          ],
        ),
      ),
    );
  }
}
