import 'dart:async';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../core/api/api_service.dart';

/// Enhanced FCM Charging Session Manager
/// Provides a clean interface for managing FCM subscriptions during charging sessions
/// Replaces local charging notifications with server-side FCM notifications
class FCMChargingSessionManager {
  static final FCMChargingSessionManager _instance = FCMChargingSessionManager._internal();
  factory FCMChargingSessionManager() => _instance;
  FCMChargingSessionManager._internal();

  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  final ApiService _apiService = ApiService();
  
  String? _fcmToken;
  String? _currentSessionId;
  String? _currentTransactionId;
  bool _isSubscribed = false;
  bool _isInitialized = false;

  /// Initialize the FCM charging session manager
  Future<void> initialize() async {
    if (_isInitialized) {
      debugPrint('🔔 FCM Charging Session Manager already initialized');
      return;
    }

    try {
      debugPrint('🔔 ===== INITIALIZING FCM CHARGING SESSION MANAGER =====');
      
      // Get FCM token
      await _getFCMToken();
      
      // Load any existing session state
      await _loadSessionState();
      
      _isInitialized = true;
      debugPrint('✅ FCM Charging Session Manager initialized successfully');
      
      if (_currentSessionId != null) {
        debugPrint('🔔 Restored session: $_currentSessionId');
      }
    } catch (e) {
      debugPrint('❌ Error initializing FCM Charging Session Manager: $e');
      rethrow;
    }
  }

  /// Start charging session with FCM subscription
  /// This replaces local charging notifications with server-side FCM
  Future<bool> startChargingSession({
    required String sessionId,
    required String transactionId,
  }) async {
    try {
      debugPrint('🔔 ===== STARTING CHARGING SESSION WITH FCM =====');
      debugPrint('🔔 Session ID: $sessionId');
      debugPrint('🔔 Transaction ID: $transactionId');
      
      if (_fcmToken == null) {
        await _getFCMToken();
        if (_fcmToken == null) {
          debugPrint('❌ Cannot start charging session: FCM token not available');
          return false;
        }
      }

      // Subscribe to server-side charging notifications
      final subscriptionSuccess = await _subscribeToChargingNotifications(
        sessionId: sessionId,
        transactionId: transactionId,
      );

      if (subscriptionSuccess) {
        _currentSessionId = sessionId;
        _currentTransactionId = transactionId;
        _isSubscribed = true;
        
        // Save session state
        await _saveSessionState();
        
        debugPrint('✅ Charging session started with FCM notifications');
        debugPrint('🔔 All charging updates will come from server via FCM');
        return true;
      } else {
        debugPrint('❌ Failed to start charging session with FCM');
        return false;
      }
    } catch (e) {
      debugPrint('❌ Error starting charging session: $e');
      return false;
    }
  }

  /// Stop charging session and unsubscribe from FCM
  Future<bool> stopChargingSession() async {
    try {
      debugPrint('🔔 ===== STOPPING CHARGING SESSION =====');
      
      if (!_isSubscribed || _currentSessionId == null) {
        debugPrint('ℹ️ No active charging session to stop');
        return true;
      }

      // Unsubscribe from server-side charging notifications
      final unsubscriptionSuccess = await _unsubscribeFromChargingNotifications();

      if (unsubscriptionSuccess) {
        _currentSessionId = null;
        _currentTransactionId = null;
        _isSubscribed = false;
        
        // Clear session state
        await _clearSessionState();
        
        debugPrint('✅ Charging session stopped and FCM unsubscribed');
        return true;
      } else {
        debugPrint('❌ Failed to properly stop charging session');
        return false;
      }
    } catch (e) {
      debugPrint('❌ Error stopping charging session: $e');
      return false;
    }
  }

  /// Subscribe to charging notifications via server
  Future<bool> _subscribeToChargingNotifications({
    required String sessionId,
    required String transactionId,
  }) async {
    try {
      // Prepare subscription data for server
      final subscriptionData = {
        'fcm_token': _fcmToken!,
        'session_id': sessionId,
        'transaction_id': transactionId,
        'notification_type': 'charging',
        'platform': defaultTargetPlatform == TargetPlatform.iOS ? 'ios' : 'android',
        'app_version': '1.0.0',
        'subscription_timestamp': DateTime.now().toIso8601String(),
      };

      debugPrint('📤 Subscribing to server-side charging notifications...');
      debugPrint('📄 Subscription data: $subscriptionData');

      // Send subscription request to server
      final response = await _apiService.post(
        '/user/notifications/subscribe',
        data: subscriptionData,
      );

      debugPrint('📥 Server response: $response');

      // Check if subscription was successful
      if (response != null && response['success'] == true) {
        debugPrint('✅ Successfully subscribed to server-side charging notifications');
        
        // Subscribe to FCM topic for real-time updates
        final topicName = 'charging_session_$sessionId';
        final topicSuccess = await _subscribeToFCMTopic(topicName);
        
        if (topicSuccess) {
          debugPrint('✅ FCM topic subscription successful: $topicName');
        } else {
          debugPrint('⚠️ FCM topic subscription failed but server subscription succeeded');
        }
        
        return true;
      } else {
        debugPrint('❌ Server subscription failed');
        debugPrint('❌ Response: $response');
        return false;
      }
    } catch (e) {
      debugPrint('❌ Error subscribing to charging notifications: $e');
      return false;
    }
  }

  /// Unsubscribe from charging notifications
  Future<bool> _unsubscribeFromChargingNotifications() async {
    try {
      if (_currentSessionId == null || _currentTransactionId == null) {
        debugPrint('⚠️ No active session to unsubscribe from');
        return true;
      }

      // Prepare unsubscription data
      final unsubscriptionData = {
        'fcm_token': _fcmToken!,
        'session_id': _currentSessionId!,
        'transaction_id': _currentTransactionId!,
        'notification_type': 'charging',
        'unsubscription_timestamp': DateTime.now().toIso8601String(),
      };

      debugPrint('📤 Unsubscribing from server-side charging notifications...');

      // Send unsubscription request to server
      final response = await _apiService.post(
        '/user/notifications/unsubscribe',
        data: unsubscriptionData,
      );

      debugPrint('📥 Server response: $response');

      // Check if unsubscription was successful
      if (response != null && response['success'] == true) {
        debugPrint('✅ Successfully unsubscribed from server-side charging notifications');
        
        // Unsubscribe from FCM topic
        final topicName = 'charging_session_${_currentSessionId!}';
        final topicSuccess = await _unsubscribeFromFCMTopic(topicName);
        
        if (topicSuccess) {
          debugPrint('✅ FCM topic unsubscription successful: $topicName');
        } else {
          debugPrint('⚠️ FCM topic unsubscription failed but server unsubscription succeeded');
        }
        
        return true;
      } else {
        debugPrint('❌ Server unsubscription failed');
        return false;
      }
    } catch (e) {
      debugPrint('❌ Error unsubscribing from charging notifications: $e');
      return false;
    }
  }

  /// Subscribe to FCM topic with enhanced error handling
  Future<bool> _subscribeToFCMTopic(String topic) async {
    try {
      debugPrint('🔔 Subscribing to FCM topic: $topic');
      
      await _firebaseMessaging.subscribeToTopic(topic).timeout(
        const Duration(seconds: 10),
        onTimeout: () => throw TimeoutException('FCM subscription timeout', const Duration(seconds: 10)),
      );
      
      debugPrint('✅ Successfully subscribed to FCM topic: $topic');
      return true;
    } on TimeoutException catch (e) {
      debugPrint('❌ FCM topic subscription timeout: $e');
      return false;
    } catch (e) {
      debugPrint('❌ Error subscribing to FCM topic $topic: $e');
      return false;
    }
  }

  /// Unsubscribe from FCM topic with enhanced error handling
  Future<bool> _unsubscribeFromFCMTopic(String topic) async {
    try {
      debugPrint('🔕 Unsubscribing from FCM topic: $topic');
      
      await _firebaseMessaging.unsubscribeFromTopic(topic).timeout(
        const Duration(seconds: 10),
        onTimeout: () => throw TimeoutException('FCM unsubscription timeout', const Duration(seconds: 10)),
      );
      
      debugPrint('✅ Successfully unsubscribed from FCM topic: $topic');
      return true;
    } on TimeoutException catch (e) {
      debugPrint('❌ FCM topic unsubscription timeout: $e');
      return false;
    } catch (e) {
      debugPrint('❌ Error unsubscribing from FCM topic $topic: $e');
      return false;
    }
  }

  /// Get FCM token
  Future<void> _getFCMToken() async {
    try {
      _fcmToken = await _firebaseMessaging.getToken();
      if (_fcmToken != null) {
        debugPrint('🔔 FCM Token obtained: ${_fcmToken!.substring(0, 20)}...');
      } else {
        debugPrint('❌ Failed to obtain FCM token');
      }
    } catch (e) {
      debugPrint('❌ Error getting FCM token: $e');
    }
  }

  /// Save session state to local storage
  Future<void> _saveSessionState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      if (_currentSessionId != null) {
        await prefs.setString('fcm_charging_session_id', _currentSessionId!);
      }
      if (_currentTransactionId != null) {
        await prefs.setString('fcm_charging_transaction_id', _currentTransactionId!);
      }
      await prefs.setBool('fcm_charging_subscribed', _isSubscribed);
    } catch (e) {
      debugPrint('❌ Error saving session state: $e');
    }
  }

  /// Load session state from local storage
  Future<void> _loadSessionState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _currentSessionId = prefs.getString('fcm_charging_session_id');
      _currentTransactionId = prefs.getString('fcm_charging_transaction_id');
      _isSubscribed = prefs.getBool('fcm_charging_subscribed') ?? false;
    } catch (e) {
      debugPrint('❌ Error loading session state: $e');
    }
  }

  /// Clear session state from local storage
  Future<void> _clearSessionState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('fcm_charging_session_id');
      await prefs.remove('fcm_charging_transaction_id');
      await prefs.remove('fcm_charging_subscribed');
    } catch (e) {
      debugPrint('❌ Error clearing session state: $e');
    }
  }

  /// Get current session info
  Map<String, dynamic> get sessionInfo => {
    'session_id': _currentSessionId,
    'transaction_id': _currentTransactionId,
    'is_subscribed': _isSubscribed,
    'fcm_token_available': _fcmToken != null,
    'is_initialized': _isInitialized,
  };

  /// Check if currently subscribed to charging notifications
  bool get isSubscribed => _isSubscribed;

  /// Get current session ID
  String? get currentSessionId => _currentSessionId;

  /// Get current transaction ID
  String? get currentTransactionId => _currentTransactionId;
}
