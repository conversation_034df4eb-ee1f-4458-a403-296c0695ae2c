class StationMarkersResponse {
  List<StationMarker>? data;
  String? message;
  bool? success;

  StationMarkersResponse({this.data, this.message, this.success});

  StationMarkersResponse.fromJson(Map<String, dynamic> json) {
    if (json['data'] != null) {
      data = <StationMarker>[];
      json['data'].forEach((v) {
        data!.add(StationMarker.fromJson(v));
      });
    }
    message = json['message'];
    success = json['success'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> result = {};
    if (data != null) {
      result['data'] = data!.map((v) => v.toJson()).toList();
    }
    result['message'] = message;
    result['success'] = success;
    return result;
  }
}

class StationMarker {
  int? stationId;
  String? stationName;
  double? longitude;
  double? latitude;
  String? focusedMapPinUrl;
  String? mapPinUrl;

  StationMarker(
      {this.stationId,
      this.stationName,
      this.longitude,
      this.latitude,
      this.focusedMapPinUrl,
      this.mapPinUrl});

  StationMarker.fromJson(Map<String, dynamic> json) {
    stationId = json['station_id'];
    stationName = json['station_name'];
    longitude = json['longitude'] is double
        ? json['longitude']
        : double.tryParse(json['longitude'].toString());
    latitude = json['latitude'] is double
        ? json['latitude']
        : double.tryParse(json['latitude'].toString());
    focusedMapPinUrl = json['focused_map_pin_url'];
    mapPinUrl = json['map_pin_url'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['station_id'] = stationId;
    data['station_name'] = stationName;
    data['longitude'] = longitude;
    data['latitude'] = latitude;
    data['focused_map_pin_url'] = focusedMapPinUrl;
    data['map_pin_url'] = mapPinUrl;
    return data;
  }
}
