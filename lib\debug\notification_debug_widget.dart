import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:ecoplug/debug/notification_debug_service.dart';

/// Notification Debug Widget
/// Simple floating action button for testing notifications
/// Only visible in debug mode
class NotificationDebugWidget extends StatefulWidget {
  const NotificationDebugWidget({super.key});

  @override
  State<NotificationDebugWidget> createState() => _NotificationDebugWidgetState();
}

class _NotificationDebugWidgetState extends State<NotificationDebugWidget> {
  final NotificationDebugService _debugService = NotificationDebugService();
  bool _isRunning = false;

  @override
  Widget build(BuildContext context) {
    // Only show in debug mode
    if (!kDebugMode) {
      return const SizedBox.shrink();
    }

    return Positioned(
      bottom: 100,
      right: 16,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Quick test button
          FloatingActionButton(
            heroTag: "notification_quick_test",
            mini: true,
            backgroundColor: Colors.orange,
            onPressed: _isRunning ? null : _runQuickTest,
            child: _isRunning 
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Icon(Icons.notifications_active, size: 20),
          ),
          const SizedBox(height: 8),
          // Welcome test button
          FloatingActionButton(
            heroTag: "notification_welcome_test",
            mini: true,
            backgroundColor: Colors.green,
            onPressed: _isRunning ? null : _runWelcomeTest,
            child: const Icon(Icons.waving_hand, size: 20),
          ),
          const SizedBox(height: 8),
          // Full diagnosis button
          FloatingActionButton(
            heroTag: "notification_full_diagnosis",
            mini: true,
            backgroundColor: Colors.blue,
            onPressed: _isRunning ? null : _runFullDiagnosis,
            child: const Icon(Icons.bug_report, size: 20),
          ),
          const SizedBox(height: 8),
          // Cleanup button
          FloatingActionButton(
            heroTag: "notification_cleanup",
            mini: true,
            backgroundColor: Colors.red,
            onPressed: _isRunning ? null : _cleanupNotifications,
            child: const Icon(Icons.clear_all, size: 20),
          ),
        ],
      ),
    );
  }

  /// Run quick notification test
  Future<void> _runQuickTest() async {
    setState(() => _isRunning = true);

    try {
      debugPrint('🔍 Running quick notification test...');
      final success = await _debugService.quickNotificationTest();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              success 
                  ? '✅ Quick test passed! Check notification tray.' 
                  : '❌ Quick test failed. Check debug console.',
            ),
            backgroundColor: success ? Colors.green : Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      debugPrint('❌ Quick test error: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Test error: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isRunning = false);
      }
    }
  }

  /// Run welcome notification test
  Future<void> _runWelcomeTest() async {
    setState(() => _isRunning = true);

    try {
      debugPrint('🔍 Running welcome notification test...');
      final success = await _debugService.testWelcomeNotification();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              success 
                  ? '✅ Welcome test sent! Check notification tray for "Welcome to EcoPlug! 😊⚡"' 
                  : '❌ Welcome test failed. Check debug console.',
            ),
            backgroundColor: success ? Colors.green : Colors.red,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    } catch (e) {
      debugPrint('❌ Welcome test error: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Welcome test error: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isRunning = false);
      }
    }
  }

  /// Run full notification diagnosis
  Future<void> _runFullDiagnosis() async {
    setState(() => _isRunning = true);

    try {
      debugPrint('🔍 Running full notification diagnosis...');
      final results = await _debugService.runFullDiagnosis();
      
      final overallStatus = results['overall_status'] ?? 'unknown';
      final isSuccess = overallStatus == 'all_systems_working';
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isSuccess 
                  ? '✅ Full diagnosis: All systems working!' 
                  : '⚠️ Full diagnosis: Issues found. Check debug console.',
            ),
            backgroundColor: isSuccess ? Colors.green : Colors.orange,
            duration: const Duration(seconds: 4),
          ),
        );

        // Show detailed results dialog
        _showDiagnosisResults(results);
      }
    } catch (e) {
      debugPrint('❌ Full diagnosis error: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Diagnosis error: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isRunning = false);
      }
    }
  }

  /// Clean up test notifications
  Future<void> _cleanupNotifications() async {
    setState(() => _isRunning = true);

    try {
      debugPrint('🧹 Cleaning up test notifications...');
      await _debugService.cleanupTestNotifications();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('🧹 Test notifications cleaned up'),
            backgroundColor: Colors.blue,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      debugPrint('❌ Cleanup error: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Cleanup error: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isRunning = false);
      }
    }
  }

  /// Show diagnosis results dialog
  void _showDiagnosisResults(Map<String, dynamic> results) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Notification Diagnosis Results'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildResultItem('Overall Status', results['overall_status']),
              const Divider(),
              _buildResultItem('Local Manager', 
                  results['local_manager']?['overall_success']?.toString()),
              _buildResultItem('Welcome Service', 
                  results['welcome_service']?['overall_success']?.toString()),
              _buildResultItem('Charging Manager', 
                  results['charging_manager']?['overall_success']?.toString()),
              _buildResultItem('Notification Display', 
                  results['notification_display']?['overall_success']?.toString()),
              const Divider(),
              const Text(
                'Check debug console for detailed logs.',
                style: TextStyle(fontSize: 12, fontStyle: FontStyle.italic),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  /// Build result item widget
  Widget _buildResultItem(String label, String? value) {
    final isSuccess = value == 'true' || value == 'all_systems_working';
    final color = isSuccess ? Colors.green : Colors.red;
    final icon = isSuccess ? Icons.check_circle : Icons.error;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(icon, color: color, size: 16),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              '$label: ${value ?? 'unknown'}',
              style: TextStyle(fontSize: 12, color: color),
            ),
          ),
        ],
      ),
    );
  }
}
