/// Centralized configuration for Google Maps settings and API access.
///
/// This class provides static configuration values for Google Maps integration.
/// The API key should be set in the environment or directly in code for development.
class GoogleMapsConfig {
  // API key - Replace with your actual key or use a secure method to load it
  static final String _apiKey = 'AIzaSyBCU1EjZJ8ZRmWpYxaK55nAKKcehh-cTwM';

  /// Get the Google Maps API key
  ///
  /// Returns the configured API key
  static String get apiKey {
    return _apiKey;
  }

  // DELETED: No default coordinates - only use real user location
  // Map zoom settings
  static const double defaultZoom = 5.0;
  static const double minZoom = 5.0;
  static const double maxZoom = 19.0;

  // Map interaction settings
  static const bool enableMyLocationButton = true;
  static const bool enableZoomControls = true;
  static const bool enableCompass = true;
  static const bool enableMapToolbar = true;

  // Gesture settings
  static const bool enableRotateGestures = true;
  static const bool enableScrollGestures = true;
  static const bool enableTiltGestures = true;
  static const bool enableZoomGestures = true;

  // Animation settings
  static const Duration animationDuration = Duration(milliseconds: 500);

  // Camera padding
  static const double cameraPadding = 50.0;

  // Maximum radius for nearby searches (in meters)
  static const int maxSearchRadius = 50000; // 50km

  // Default search radius (in meters)
  static const int defaultSearchRadius = 10000; // 10km
}
