import 'package:flutter/foundation.dart';
import '../models/api_response.dart';
import 'user_repository.dart';

/// Extension methods for UserRepository
///
/// These methods provide a cleaner, more secure interface for common operations
/// while leveraging the existing secure implementations in the base repository.
extension UserRepositoryExtension on UserRepository {
  /// A simplified method for updating user profile basics with named parameters
  ///
  /// This method provides several security and usability benefits:
  /// - Uses named parameters for better readability and API clarity
  /// - Validates input parameters before making API calls
  /// - Leverages the existing secure implementation in UserRepository
  /// - Provides comprehensive error handling and logging
  /// - Maintains all security features of the original method:
  ///   - Token authentication
  ///   - Local data persistence
  ///   - Offline sync capabilities
  ///   - Retry mechanisms
  ///   - Error handling for network issues
  Future<ApiResponse<bool>> simpleProfileUpdate({
    required String userId,
    required String name,
    required String email,
    String? gstNumber,
    String? businessName,
  }) async {
    try {
      // Input validation
      if (userId.isEmpty) {
        debugPrint('ERROR: User ID cannot be empty');
        return ApiResponse<bool>(
          success: false,
          message: 'User ID is required. Please log in again.',
          data: false,
        );
      }

      if (name.isEmpty) {
        debugPrint('ERROR: Name cannot be empty');
        return ApiResponse<bool>(
          message: 'Name cannot be empty',
          success: false,
          data: false,
        );
      }

      // Basic email validation
      final emailRegex = RegExp(r'^[^@]+@[^@]+\.[^@]+');
      if (!emailRegex.hasMatch(email)) {
        debugPrint('ERROR: Invalid email format: $email');
        return ApiResponse<bool>(
          message: 'Invalid email format',
          success: false,
          data: false,
        );
      }
      
      // Validate GST number format if provided
      if (gstNumber != null && gstNumber.isNotEmpty) {
        final gstRegex = RegExp(
          r'^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$'
        );
        if (!gstRegex.hasMatch(gstNumber)) {
          debugPrint('ERROR: Invalid GST number format: $gstNumber');
          return ApiResponse<bool>(
            message: 'Invalid GST number format',
            success: false,
            data: false,
          );
        }
      }

      debugPrint('\n=== PROFILE UPDATE REQUEST ===');
      debugPrint('User ID: $userId');
      debugPrint('Name: $name');
      debugPrint('Email: $email');

      // Now perform the actual update using the new updateUserProfileWithGst method
      // This leverages all the security features of the base repository
      final response = await updateUserProfileWithGst(
        userId: userId,
        name: name,
        email: email,
        gstNumber: gstNumber,
        businessName: businessName,
      );

      // Enhanced logging for better debugging
      if (response.success) {
        debugPrint('\n=== PROFILE UPDATE SUCCESSFUL ===');
        debugPrint('Profile updated successfully for user: $userId');
        debugPrint('Response message: ${response.message}');
      } else {
        debugPrint('\n=== PROFILE UPDATE FAILED ===');
        debugPrint('Failed to update profile for user: $userId');
        debugPrint('Error: ${response.message}');
      }

      return ApiResponse<bool>(
        success: response.success,
        message: response.message,
        data: response.data,
      );
    } catch (e, stackTrace) {
      // Comprehensive error logging
      debugPrint('\n=== CRITICAL ERROR IN PROFILE UPDATE ===');
      debugPrint('Error type: ${e.runtimeType}');
      debugPrint('Error message: $e');
      debugPrint('Stack trace: $stackTrace');

      return ApiResponse<bool>(
        success: false,
        message:
            'An unexpected error occurred while updating your profile. Please try again later.',
        data: false,
      );
    }
  }
}
