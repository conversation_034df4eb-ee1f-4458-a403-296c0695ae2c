/// Model for profile response from API
class ProfileResponse {
  final ProfileData data;
  final List<dynamic> fleetuser;
  final String message;
  final bool success;

  ProfileResponse({
    required this.data,
    required this.fleetuser,
    required this.message,
    required this.success,
  });

  factory ProfileResponse.fromJson(Map<String, dynamic> json) {
    return ProfileResponse(
      data: ProfileData.fromJson(json['data'] ?? {}),
      fleetuser: json['fleetuser'] ?? [],
      message: json['message'] ?? '',
      success: json['success'] ?? false,
    );
  }
}

/// Model for profile data
class ProfileData {
  final int id;
  final String uid;
  final String name;
  final String email;
  final String address;
  final String state;
  final String pincode;
  final String mobileNumber;
  final int status;
  final String source;
  final String domain;
  final String companyUid;
  final String createdAt;
  final String updatedAt;
  final int instantCharging;
  final List<String> rfidCode;
  final int rfidStatus;
  final List<dynamic> vids;
  final int autochargeStatus;
  final String tariffId;
  final String type;
  final int fleetAdminId;
  final String? gstNo;
  final String? businessName;
  final int maxAmpLimit;
  final Balance balance;
  final int vehicle;
  final ProfileInfo profile;

  ProfileData({
    required this.id,
    required this.uid,
    required this.name,
    required this.email,
    required this.address,
    required this.state,
    required this.pincode,
    required this.mobileNumber,
    required this.status,
    required this.source,
    required this.domain,
    required this.companyUid,
    required this.createdAt,
    required this.updatedAt,
    required this.instantCharging,
    required this.rfidCode,
    required this.rfidStatus,
    required this.vids,
    required this.autochargeStatus,
    required this.tariffId,
    required this.type,
    required this.fleetAdminId,
    this.gstNo,
    this.businessName,
    required this.maxAmpLimit,
    required this.balance,
    required this.vehicle,
    required this.profile,
  });

  factory ProfileData.fromJson(Map<String, dynamic> json) {
    return ProfileData(
      id: json['id'] ?? 0,
      uid: json['uid'] ?? '',
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      address: json['address'] ?? '',
      state: json['state'] ?? '',
      pincode: json['pincode'] ?? '',
      mobileNumber: json['mobile_number'] ?? '',
      status: json['status'] ?? 0,
      source: json['source'] ?? '',
      domain: json['domain'] ?? '',
      companyUid: json['company_uid'] ?? '',
      createdAt: json['created_at'] ?? '',
      updatedAt: json['updated_at'] ?? '',
      instantCharging: json['instant_charging'] ?? 0,
      rfidCode: json['rfid_code'] != null
          ? (json['rfid_code'] as List).map((item) => item.toString()).toList()
          : [],
      rfidStatus: json['rfid_status'] ?? 0,
      vids: json['vids'] ?? [],
      autochargeStatus: json['autocharge_status'] ?? 0,
      tariffId: json['tariff_id'] ?? '',
      type: json['type'] ?? '',
      fleetAdminId: json['fleet_admin_id'] ?? 0,
      gstNo: json['gst_no'],
      businessName: json['business_name'],
      maxAmpLimit: json['max_amp_limit'] ?? 90,
      balance: Balance.fromJson(json['balance'] ?? {}),
      vehicle: json['vehicle'] ?? 0,
      profile: ProfileInfo.fromJson(json['profile'] ?? {}),
    );
  }
}

/// Model for balance
class Balance {
  final double balance;

  Balance({
    required this.balance,
  });

  factory Balance.fromJson(Map<String, dynamic> json) {
    return Balance(
      balance: (json['balance'] as num?)?.toDouble() ?? 0.0,
    );
  }
}

/// Model for profile info
class ProfileInfo {
  final int vehicle;

  ProfileInfo({
    required this.vehicle,
  });

  factory ProfileInfo.fromJson(Map<String, dynamic> json) {
    return ProfileInfo(
      vehicle: json['vehicle'] ?? 0,
    );
  }
}
