import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../models/station.dart';
import '../../../models/evse.dart';
import '../../../features/station/services/station_service.dart';
// Importing PaginatedStationsResponse is necessary since it's used in the conversion code
import '../../../providers/providers.dart'; // For stationServiceProvider

enum StationListMode { list, search }

/// Station state class
class StationsState {
  final List<Station> stations;
  final StationListMode mode;
  final bool isLoading;
  final bool isLoadingMore;
  final bool hasMore;
  final String? errorMessage;
  final int currentPage;
  final int totalPages;
  final String currentSearchQuery;
  final Map<String, dynamic>? filters;
  final String selectedFilter;

  StationsState({
    List<Station>? stations,
    this.mode = StationListMode.list,
    this.isLoading = false,
    this.isLoadingMore = false,
    this.hasMore = true,
    this.errorMessage,
    this.currentPage = 1,
    this.totalPages = 1,
    this.currentSearchQuery = '',
    this.filters,
    this.selectedFilter = 'All',
  }) : stations = stations ?? <Station>[];

  StationsState copyWith({
    List<Station>? stations,
    StationListMode? mode,
    bool? isLoading,
    bool? isLoadingMore,
    bool? hasMore,
    String? errorMessage,
    int? currentPage,
    int? totalPages,
    String? currentSearchQuery,
    Map<String, dynamic>? filters,
    String? selectedFilter,
  }) {
    return StationsState(
      stations: stations ?? this.stations,
      mode: mode ?? this.mode,
      isLoading: isLoading ?? this.isLoading,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      hasMore: hasMore ?? this.hasMore,
      errorMessage: errorMessage,
      currentPage: currentPage ?? this.currentPage,
      totalPages: totalPages ?? this.totalPages,
      currentSearchQuery: currentSearchQuery ?? this.currentSearchQuery,
      filters: filters ?? this.filters,
      selectedFilter: selectedFilter ?? this.selectedFilter,
    );
  }
}

/// Stations provider
final stationsProvider =
    StateNotifierProvider<StationsNotifier, StationsState>((ref) {
  return StationsNotifier(ref);
});

/// Stations notifier
class StationsNotifier extends StateNotifier<StationsState> {
  final StationService _stationService;

  StationsNotifier(Ref ref)
      : _stationService = ref.read(stationServiceProvider),
        super(StationsState()) {
    // Initialize by loading stations
    loadStations(refresh: true);
  }

  // Alias for loadStations with refresh=true for backward compatibility
  Future<void> fetchInitialStations() async {
    return loadStations(refresh: true);
  }

  Future<void> loadStations({bool refresh = false}) async {
    if (state.isLoading) return;

    state = state.copyWith(isLoading: true, errorMessage: null);

    try {
      final response = await _stationService.fetchStationsPaginated(
        page: refresh ? 1 : state.currentPage,
        limit: 10,
      );

      // Convert PaginatedStation objects to Station objects - properly handle all API data
      List<Station> stationsList = [];
      if (response.data != null) {
        stationsList = response.data!.map((paginatedStation) {
          // Convert connector types to proper Connector objects for Station model
          final connectorTypes = paginatedStation.getConnectorTypes();
          final connectors = connectorTypes
              .map((type) => Connector(
                    id: '${paginatedStation.uid ?? paginatedStation.stationId}_${type.name ?? 'unknown'}',
                    name: type.name ?? 'Connector type not specified',
                    type: type.name ?? 'Connector type not specified',
                    power: type.power, // Keep as nullable string from API
                    availableGuns: type.availableGuns ?? 0,
                    maxElectricPower:
                        type.maxElectricPower, // PRESERVE EXACT API FORMAT
                    priceLabel: null, // ConnectorType doesn't have price field
                    icon: type.icon, // CRITICAL: Pass real icon URL from API
                  ))
              .toList();

          // Convert connector types to EVSEs as well for compatibility
          final evses = connectorTypes
              .map((type) => Evse(
                    id: '${paginatedStation.uid ?? paginatedStation.stationId}_${type.name ?? 'unknown'}',
                    name: type.name ?? 'Connector type not specified',
                    status: paginatedStation.status ?? 'Status not specified',
                    power: type.power != null
                        ? (double.tryParse(type.power!) ?? 0.0)
                        : 0.0,
                    connectorType: type.name ?? 'Connector type not specified',
                    availableConnectors: type.availableGuns ?? 0,
                  ))
              .toList();

          return Station(
            id: paginatedStation.stationId?.toString() ?? '',
            uid: paginatedStation.uid ??
                '', // Use actual UID from API or empty string
            name: paginatedStation.name ?? '',
            address: paginatedStation.address ?? '',
            city: paginatedStation.city, // Real city or null
            latitude: paginatedStation.latitude ?? 0.0,
            longitude: paginatedStation.longitude ?? 0.0,
            distance: paginatedStation.distance ?? 0.0,
            status:
                paginatedStation.status ?? 'Unknown', // Real status or default
            rating: paginatedStation.rating ??
                0.0, // Use 0.0 only when API provides no rating (model requirement)
            reviews: paginatedStation.reviewCount ??
                0, // Use 0 only when API provides no reviews (model requirement)
            images: paginatedStation.imageUrl != null
                ? [paginatedStation.imageUrl!]
                : [], // Real images or empty list
            evses: evses,
            connectors:
                connectors, // Properly populated connectors for station cards
            types: paginatedStation.types, // Real types data from API
          );
        }).toList();
      }

      state = state.copyWith(
        stations: refresh
            ? List<Station>.from(stationsList)
            : List<Station>.from([...state.stations, ...stationsList]),
        currentPage: response.currentPage,
        totalPages: response.totalPages,
        isLoading: false,
        mode: StationListMode.list,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'Failed to load stations: ${e.toString()}',
      );
    }
  }

  Future<void> fetchMoreStations() async {
    if (state.isLoading ||
        state.currentPage >= state.totalPages ||
        state.mode == StationListMode.search) {
      return;
    }

    state = state.copyWith(isLoading: true);
    try {
      final response = await _stationService.fetchStationsPaginated(
          page: state.currentPage + 1, limit: 10);

      // Convert PaginatedStation objects to Station objects - properly handle all API data
      List<Station> stationsList = [];
      if (response.data != null) {
        stationsList = response.data!.map((paginatedStation) {
          // Convert connector types to proper Connector objects for Station model
          final connectorTypes = paginatedStation.getConnectorTypes();
          final connectors = connectorTypes
              .map((type) => Connector(
                    id: '${paginatedStation.uid ?? paginatedStation.stationId}_${type.name ?? 'unknown'}',
                    name: type.name ?? 'Connector type not specified',
                    type: type.name ?? 'Connector type not specified',
                    power: type.power, // Keep as nullable string from API
                    availableGuns: type.availableGuns ?? 0,
                    maxElectricPower:
                        type.maxElectricPower, // PRESERVE EXACT API FORMAT
                    priceLabel: null, // ConnectorType doesn't have price field
                    icon: type.icon, // CRITICAL: Pass real icon URL from API
                  ))
              .toList();

          // Convert connector types to EVSEs as well for compatibility
          final evses = connectorTypes
              .map((type) => Evse(
                    id: '${paginatedStation.uid ?? paginatedStation.stationId}_${type.name ?? 'unknown'}',
                    name: type.name ?? 'Connector type not specified',
                    status: paginatedStation.status ?? 'Status not specified',
                    power: type.power != null
                        ? (double.tryParse(type.power!) ?? 0.0)
                        : 0.0,
                    connectorType: type.name ?? 'Connector type not specified',
                    availableConnectors: type.availableGuns ?? 0,
                  ))
              .toList();

          return Station(
            id: paginatedStation.stationId?.toString() ?? '',
            uid: paginatedStation.uid ??
                '', // Use actual UID from API or empty string
            name: paginatedStation.name ?? '',
            address: paginatedStation.address ?? '',
            city: paginatedStation.city, // Real city or null
            latitude: paginatedStation.latitude ?? 0.0,
            longitude: paginatedStation.longitude ?? 0.0,
            distance: paginatedStation.distance ?? 0.0,
            status:
                paginatedStation.status ?? 'Unknown', // Real status or default
            rating: paginatedStation.rating ??
                0.0, // Use 0.0 only when API provides no rating (model requirement)
            reviews: paginatedStation.reviewCount ??
                0, // Use 0 only when API provides no reviews (model requirement)
            images: paginatedStation.imageUrl != null
                ? [paginatedStation.imageUrl!]
                : [], // Real images or empty list
            evses: evses,
            connectors:
                connectors, // Properly populated connectors for station cards
            types: paginatedStation.types, // Real types data from API
          );
        }).toList();
      }

      state = state.copyWith(
        stations: List<Station>.from([...state.stations, ...stationsList]),
        currentPage: response.currentPage,
        isLoading: false,
        errorMessage: null,
      );
    } catch (e) {
      state = state.copyWith(isLoading: false, errorMessage: e.toString());
    }
  }

  Future<void> searchStations(String query) async {
    if (state.isLoading) return;

    state = state.copyWith(
      isLoading: true,
      errorMessage: null,
      currentSearchQuery: query,
      stations: <Station>[],
      currentPage: 0,
      hasMore: true,
    );

    await loadStations(refresh: true);
  }

  void setFilter(String filter) {
    if (state.selectedFilter == filter) return;

    state = state.copyWith(
      selectedFilter: filter,
      stations: <Station>[],
      currentPage: 0,
      hasMore: true,
    );

    loadStations(refresh: true);
  }

  // Get station details
  Future<Station?> getStationDetails(String stationId) async {
    try {
      // DELETED: Station.empty() usage removed - no default stations
      // Check if the station is in the cache
      if (state.stations.isNotEmpty) {
        try {
          final cachedStation = state.stations.firstWhere(
            (station) => station.id == stationId,
          );
          return cachedStation;
        } catch (e) {
          // Station not found in cache, continue to API call
        }
      }

      // If not in cache, fetch from API
      final response = await _stationService.getStationDetails(stationId);
      if (response.success && response.data != null) {
        // Convert StationDetail to Station
        final stationDetail = response.data!;
        return Station(
          id: stationDetail.uid,
          name: stationDetail.name,
          address: stationDetail.address,
          latitude: stationDetail.coordinates.latitude,
          longitude: stationDetail.coordinates.longitude,
          distance: 0.0,
          status: stationDetail.status ?? 'Unknown',
          rating: stationDetail.rating ?? 0.0,
          reviews: stationDetail.totalReviews ?? 0,
          images: stationDetail.images ?? [],
          evses: [],
          connectors: [],
          uid: stationDetail.uid,
        );
      }
      return null;
    } catch (e) {
      debugPrint('Error getting station details: $e');
      return null;
    }
  }
}
