import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:ecoplug/models/charging_session.dart';
import 'package:ecoplug/services/active_charging_notification_service.dart';
import 'package:ecoplug/services/charging_session_service.dart';

/// Charging Session Notification Manager
/// Coordinates between charging session state and active notifications
/// Manages the lifecycle of charging session notifications
class ChargingSessionNotificationManager {
  static final ChargingSessionNotificationManager _instance =
      ChargingSessionNotificationManager._internal();
  factory ChargingSessionNotificationManager() => _instance;
  ChargingSessionNotificationManager._internal();

  final ActiveChargingNotificationService _notificationService =
      ActiveChargingNotificationService();
  final ChargingSessionService _chargingService = ChargingSessionService();

  bool _isInitialized = false;
  ChargingSession? _currentSession;
  StreamSubscription<ChargingSession?>? _sessionSubscription;
  Timer? _sessionMonitorTimer;

  /// Initialize the charging session notification manager
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint(
          '🔋 ===== INITIALIZING CHARGING SESSION NOTIFICATION MANAGER =====');

      // Initialize the notification service
      await _notificationService.initialize();

      // Start monitoring charging sessions
      _startSessionMonitoring();

      _isInitialized = true;
      debugPrint(
          '✅ Charging session notification manager initialized successfully');
    } catch (e) {
      debugPrint(
          '❌ Error initializing charging session notification manager: $e');
      rethrow;
    }
  }

  /// Start monitoring charging sessions for notification management
  void _startSessionMonitoring() {
    debugPrint('🔋 Starting charging session monitoring...');

    // Monitor session changes every 10 seconds
    _sessionMonitorTimer = Timer.periodic(const Duration(seconds: 10), (timer) {
      _checkChargingSessionStatus();
    });

    debugPrint('✅ Charging session monitoring started');
  }

  /// Check current charging session status and manage notifications accordingly
  Future<void> _checkChargingSessionStatus() async {
    try {
      // In a real implementation, this would check with your charging session service
      // to get the current active session status

      // For now, we'll use the ChargingFlowManager to check session status
      final flowManager = ChargingFlowManager();

      // Check if there's an active charging session
      // This is a placeholder - you'll need to implement actual session checking
      // based on your charging session management logic

      debugPrint('🔋 Checking charging session status...');
    } catch (e) {
      debugPrint('❌ Error checking charging session status: $e');
    }
  }

  /// Start charging session notification
  /// Call this when a charging session begins
  /// DISABLED: Local notifications removed - using FCM only
  Future<void> startChargingSession(ChargingSession session) async {
    debugPrint('🔋 ===== CHARGING SESSION NOTIFICATION DISABLED =====');
    debugPrint('🔋 Session ID: ${session.id}');
    debugPrint('🔋 Station: ${session.stationUid}');
    debugPrint('🔋 Connector: ${session.connectorId}');
    debugPrint('🔋 Initial Charge: ${(session.currentCharge * 100).round()}%');
    debugPrint('🔋 Local notifications disabled - using FCM only');
    debugPrint('✅ FCM notifications will handle charging session updates');

    // Store session for consistency but don't trigger notifications
    _currentSession = session;
  }

  /// Update charging session notification with new data
  /// DISABLED: Local notifications removed - using FCM only
  Future<void> updateChargingSession(ChargingSession updatedSession) async {
    debugPrint('🔋 ===== CHARGING SESSION UPDATE DISABLED =====');
    debugPrint('🔋 Session ID: ${updatedSession.id}');
    debugPrint('🔋 Charge: ${(updatedSession.currentCharge * 100).round()}%');
    debugPrint(
        '🔋 Power: ${updatedSession.currentPower.toStringAsFixed(1)} kW');
    debugPrint(
        '🔋 Energy: ${updatedSession.energyDelivered.toStringAsFixed(2)} kWh');
    debugPrint('🔋 Cost: ₹${updatedSession.cost.toStringAsFixed(2)}');
    debugPrint('🔋 Local notifications disabled - using FCM only');
    debugPrint('✅ FCM notifications will handle charging session updates');

    // Store session for consistency but don't trigger notifications
    _currentSession = updatedSession;
  }

  /// Stop charging session notification
  /// DISABLED: Local notifications removed - using FCM only
  Future<void> stopChargingSession() async {
    debugPrint('🔋 ===== CHARGING SESSION STOP DISABLED =====');
    if (_currentSession != null) {
      debugPrint('🔋 Session ID: ${_currentSession!.id}');
    }
    debugPrint('🔋 Local notifications disabled - using FCM only');
    debugPrint('✅ FCM notifications will handle charging session completion');

    // Clear the current session for consistency
    _currentSession = null;
  }

  /// Handle charging session completion
  /// Call this when charging session completes successfully
  Future<void> completeChargingSession(ChargingSession completedSession) async {
    try {
      debugPrint('🔋 ===== COMPLETING CHARGING SESSION =====');
      debugPrint('🔋 Session ID: ${completedSession.id}');
      debugPrint(
          '🔋 Final Charge: ${(completedSession.currentCharge * 100).round()}%');
      debugPrint(
          '🔋 Total Energy: ${completedSession.energyDelivered.toStringAsFixed(2)} kWh');
      debugPrint('🔋 Total Cost: ₹${completedSession.cost.toStringAsFixed(2)}');
      debugPrint('🔋 Duration: ${completedSession.formattedDuration}');

      // Stop the ongoing notification
      await stopChargingSession();

      // Optionally show a completion notification
      await _showChargingCompletionNotification(completedSession);

      debugPrint('✅ Charging session completed successfully');
    } catch (e) {
      debugPrint('❌ Error completing charging session: $e');
    }
  }

  /// Show charging completion notification
  Future<void> _showChargingCompletionNotification(
      ChargingSession session) async {
    try {
      debugPrint('🔋 Showing charging completion notification...');

      final chargePercentage = (session.currentCharge * 100).round();
      final energyDelivered = session.energyDelivered.toStringAsFixed(2);
      final cost = session.cost.toStringAsFixed(2);
      final duration = session.formattedDuration;
      final co2Saved = session.co2Saved.toStringAsFixed(1);

      // This would use your existing notification service to show a completion notification
      // For now, we'll just log the completion
      debugPrint(
          '🔋 Charging completed: ${chargePercentage}% • ${energyDelivered} kWh • ₹$cost • $duration');
      debugPrint('🔋 Environmental impact: ${co2Saved} kg CO₂ saved');

      // You can implement a completion notification here using your existing notification services
    } catch (e) {
      debugPrint('❌ Error showing charging completion notification: $e');
    }
  }

  /// Handle charging session error
  /// Call this when a charging session encounters an error
  Future<void> handleChargingSessionError(
      String sessionId, String errorMessage) async {
    try {
      debugPrint('🔋 ===== HANDLING CHARGING SESSION ERROR =====');
      debugPrint('🔋 Session ID: $sessionId');
      debugPrint('🔋 Error: $errorMessage');

      // Stop the ongoing notification
      await stopChargingSession();

      // Optionally show an error notification
      debugPrint('🔋 Charging session error handled');
    } catch (e) {
      debugPrint('❌ Error handling charging session error: $e');
    }
  }

  /// Get current active charging session
  ChargingSession? get currentSession => _currentSession;

  /// Check if there's an active charging session
  bool get hasActiveSession => _currentSession != null;

  /// Check if notification service is active
  bool get isNotificationActive => _notificationService.isChargingSessionActive;

  /// Get service initialization status
  bool get isInitialized => _isInitialized;

  /// Dispose of the manager and clean up resources
  void dispose() {
    debugPrint(
        '🔋 ===== DISPOSING CHARGING SESSION NOTIFICATION MANAGER =====');

    _sessionSubscription?.cancel();
    _sessionSubscription = null;

    _sessionMonitorTimer?.cancel();
    _sessionMonitorTimer = null;

    _notificationService.dispose();

    _currentSession = null;
    _isInitialized = false;

    debugPrint('✅ Charging session notification manager disposed');
  }

  /// Manual session update for testing
  /// This method can be called from the charging session screen for testing
  Future<void> manualSessionUpdate(ChargingSession session) async {
    debugPrint('🔋 Manual session update triggered');
    await updateChargingSession(session);
  }

  /// Force start notification for testing
  /// This method can be used for testing the notification system
  Future<void> forceStartNotification(ChargingSession session) async {
    debugPrint('🔋 Force starting notification for testing');
    await startChargingSession(session);
  }

  /// Force stop notification for testing
  /// This method can be used for testing the notification system
  Future<void> forceStopNotification() async {
    debugPrint('🔋 Force stopping notification for testing');
    await stopChargingSession();
  }
}
