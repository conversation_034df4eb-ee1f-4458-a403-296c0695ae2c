import 'package:intl/intl.dart';

/// Utility class for handling timezone conversions and date formatting
/// Ensures accurate time display according to user's location and timezone
class TimezoneUtils {
  /// Parse UTC timestamp from API and convert to local timezone
  /// 
  /// API timestamps are typically in UTC format like:
  /// - "2024-01-15T10:30:00Z" (with Z suffix)
  /// - "2024-01-15T10:30:00.000Z" (with milliseconds)
  /// - "2024-01-15 10:30:00" (without timezone info, assumed UTC)
  static DateTime parseUtcToLocal(String? timestamp) {
    if (timestamp == null || timestamp.isEmpty) {
      return DateTime.now();
    }

    try {
      DateTime utcDateTime;
      
      // Handle different timestamp formats from API
      if (timestamp.endsWith('Z')) {
        // Already has UTC indicator
        utcDateTime = DateTime.parse(timestamp);
      } else if (timestamp.contains('T') && !timestamp.endsWith('Z')) {
        // ISO format without timezone, assume UTC
        utcDateTime = DateTime.parse('${timestamp}Z');
      } else {
        // Simple date format, assume UTC
        utcDateTime = DateTime.parse(timestamp).toUtc();
      }
      
      // Convert UTC to local timezone
      return utcDateTime.toLocal();
    } catch (e) {
      // If parsing fails, return current time
      return DateTime.now();
    }
  }

  /// Format DateTime for transaction display with proper timezone
  /// Returns user-friendly format like "Today, 2:30 PM" or "Jan 15, 2024"
  static String formatTransactionDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = DateTime(now.year, now.month, now.day - 1);
    final dateToCheck = DateTime(dateTime.year, dateTime.month, dateTime.day);

    if (dateToCheck == today) {
      return 'Today, ${DateFormat('h:mm a').format(dateTime)}';
    } else if (dateToCheck == yesterday) {
      return 'Yesterday, ${DateFormat('h:mm a').format(dateTime)}';
    } else if (now.difference(dateTime).inDays < 7) {
      // Within a week, show day name
      return '${DateFormat('EEEE').format(dateTime)}, ${DateFormat('h:mm a').format(dateTime)}';
    } else {
      return DateFormat('MMM dd, yyyy').format(dateTime);
    }
  }

  /// Format DateTime for charging session display
  /// Returns format like "Jan 15" for date and "2:30 PM" for time
  static String formatChargingSessionDate(DateTime dateTime) {
    return DateFormat('MMM dd').format(dateTime);
  }

  /// Format time for charging session display
  static String formatChargingSessionTime(DateTime dateTime) {
    return DateFormat('hh:mm a').format(dateTime);
  }

  /// Format full date and time for detailed views
  /// Returns format like "Jan 15, 2024 • 2:30 PM"
  static String formatFullDateTime(DateTime dateTime) {
    return DateFormat('MMM dd, yyyy • h:mm a').format(dateTime);
  }

  /// Format date only
  static String formatDateOnly(DateTime dateTime) {
    return DateFormat('MMM dd, yyyy').format(dateTime);
  }

  /// Format time only
  static String formatTimeOnly(DateTime dateTime) {
    return DateFormat('h:mm a').format(dateTime);
  }

  /// Get relative time description (e.g., "2 hours ago", "Yesterday")
  static String getRelativeTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes} min ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours} hour${difference.inHours > 1 ? 's' : ''} ago';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return formatDateOnly(dateTime);
    }
  }

  /// Check if a DateTime is today
  static bool isToday(DateTime dateTime) {
    final now = DateTime.now();
    return dateTime.year == now.year &&
           dateTime.month == now.month &&
           dateTime.day == now.day;
  }

  /// Check if a DateTime is yesterday
  static bool isYesterday(DateTime dateTime) {
    final now = DateTime.now();
    final yesterday = now.subtract(const Duration(days: 1));
    return dateTime.year == yesterday.year &&
           dateTime.month == yesterday.month &&
           dateTime.day == yesterday.day;
  }

  /// Get timezone offset string (e.g., "+05:30" for IST)
  static String getTimezoneOffset() {
    final now = DateTime.now();
    final offset = now.timeZoneOffset;
    final hours = offset.inHours;
    final minutes = offset.inMinutes.remainder(60);
    final sign = offset.isNegative ? '-' : '+';
    return '$sign${hours.abs().toString().padLeft(2, '0')}:${minutes.abs().toString().padLeft(2, '0')}';
  }

  /// Get timezone name (e.g., "IST", "UTC")
  static String getTimezoneName() {
    return DateTime.now().timeZoneName;
  }

  /// Debug method to log timezone information
  static void logTimezoneInfo() {
    final now = DateTime.now();
    print('🕐 TIMEZONE INFO:');
    print('   Local time: ${formatFullDateTime(now)}');
    print('   UTC time: ${formatFullDateTime(now.toUtc())}');
    print('   Timezone: ${getTimezoneName()}');
    print('   Offset: ${getTimezoneOffset()}');
  }
}
