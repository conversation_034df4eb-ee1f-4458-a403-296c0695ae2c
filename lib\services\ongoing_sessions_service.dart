import 'package:flutter/material.dart';
import '../core/api/api_service.dart';
import '../core/api/api_config.dart';
import '../models/ongoing_session.dart';

/// Service for managing ongoing charging sessions
class OngoingSessionsService {
  final ApiService _apiService = ApiService();

  /// Fetch ongoing charging sessions from API
  Future<OngoingSessionsResponse?> getOngoingSessions() async {
    try {
      debugPrint('🔋 ===== FETCHING ONGOING CHARGING SESSIONS =====');
      debugPrint('🔋 Endpoint: ${ApiConfig.chargingSessionOngoingList}');
      debugPrint(
          '🔋 Full URL: ${ApiConfig.apiUrl}${ApiConfig.chargingSessionOngoingList}');
      debugPrint(
          '🔋 Expected URL: https://api2.eeil.online/api/v1/user/sessions/on-going-list');

      final response =
          await _apiService.get(ApiConfig.chargingSessionOngoingList);

      debugPrint('🔋 ===== API RESPONSE RECEIVED =====');
      debugPrint('🔋 Response is null: ${response == null}');
      debugPrint('🔋 Response type: ${response.runtimeType}');

      if (response == null) {
        debugPrint('❌ API response is null');
        return OngoingSessionsResponse(data: [], success: false);
      }

      debugPrint('🔋 Raw response: $response');

      // Parse the response
      final ongoingSessionsResponse =
          OngoingSessionsResponse.fromJson(response);

      debugPrint('🔋 ===== PARSED RESPONSE =====');
      debugPrint('🔋 Success: ${ongoingSessionsResponse.success}');
      debugPrint('🔋 Sessions count: ${ongoingSessionsResponse.data.length}');
      debugPrint(
          '🔋 Has active sessions: ${ongoingSessionsResponse.hasActiveSessions}');

      if (ongoingSessionsResponse.hasActiveSessions) {
        debugPrint('🔋 ===== ACTIVE SESSIONS FOUND =====');
        for (int i = 0; i < ongoingSessionsResponse.data.length; i++) {
          final session = ongoingSessionsResponse.data[i];
          debugPrint('🔋 Session ${i + 1}:');
          debugPrint('🔋   ID: ${session.id}');
          debugPrint('🔋   Charger: ${session.charger.chargerName}');
          debugPrint('🔋   Status: ${session.status}');
          debugPrint(
              '🔋   Authorization Reference: ${session.authorizationReference}');
          debugPrint('🔋   Connector ID: ${session.connectorId}');
        }
      } else {
        debugPrint('🔋 No active charging sessions found');
      }

      return ongoingSessionsResponse;
    } catch (e) {
      debugPrint('❌ Error fetching ongoing sessions: $e');
      return OngoingSessionsResponse(data: [], success: false);
    }
  }

  /// Check if there are any ongoing sessions (simplified method)
  Future<bool> hasOngoingSessions() async {
    try {
      final response = await getOngoingSessions();
      return response?.hasActiveSessions ?? false;
    } catch (e) {
      debugPrint('❌ Error checking ongoing sessions: $e');
      return false;
    }
  }

  /// Get count of ongoing sessions
  Future<int> getOngoingSessionsCount() async {
    try {
      final response = await getOngoingSessions();
      return response?.activeSessionsCount ?? 0;
    } catch (e) {
      debugPrint('❌ Error getting ongoing sessions count: $e');
      return 0;
    }
  }
}
