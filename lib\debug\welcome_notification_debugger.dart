import 'package:flutter/foundation.dart';
import 'package:ecoplug/services/auth_notification_service.dart';
import 'package:ecoplug/services/local_notification_manager.dart';
import 'package:ecoplug/services/charging_session_notification_manager.dart';
import 'package:permission_handler/permission_handler.dart';

/// Welcome Notification Debugger
/// Comprehensive debugging tool for welcome notification functionality
/// Only works in debug mode (kDebugMode)
class WelcomeNotificationDebugger {
  static final AuthNotificationService _authService = AuthNotificationService();
  static final LocalNotificationManager _localManager =
      LocalNotificationManager();
  static final ChargingSessionNotificationManager _chargingManager =
      ChargingSessionNotificationManager();

  /// Run comprehensive debug check for welcome notifications
  /// Returns a map with debug results
  static Future<Map<String, dynamic>> runComprehensiveDebug() async {
    if (!kDebugMode) return {'debug_mode': false};

    final Map<String, dynamic> results = {};

    try {
      debugPrint('🔍 ===== WELCOME NOTIFICATION COMPREHENSIVE DEBUG =====');

      // Step 1: Check Android notification permissions
      results['permissions'] = await _checkNotificationPermissions();

      // Step 2: Check service initialization status
      results['service_status'] = await _checkServiceInitialization();

      // Step 3: Check notification channels
      results['notification_channels'] = await _checkNotificationChannels();

      // Step 4: Test local notification functionality
      results['local_notification_test'] = await _testLocalNotifications();

      // Step 5: Test welcome notification flow
      results['welcome_flow_test'] = await _testWelcomeNotificationFlow();

      // Step 6: Check authentication integration
      results['auth_integration'] = await _checkAuthenticationIntegration();

      debugPrint('🔍 ===== WELCOME NOTIFICATION DEBUG COMPLETE =====');
      results['debug_complete'] = true;
      results['timestamp'] = DateTime.now().toIso8601String();
    } catch (e) {
      debugPrint('❌ Error during comprehensive debug: $e');
      results['error'] = e.toString();
    }

    return results;
  }

  /// Check Android notification permissions
  static Future<Map<String, dynamic>> _checkNotificationPermissions() async {
    try {
      debugPrint('🔍 Checking notification permissions...');

      final status = await Permission.notification.status;
      final result = {
        'permission_status': status.toString(),
        'is_granted': status.isGranted,
        'is_denied': status.isDenied,
        'is_permanently_denied': status.isPermanentlyDenied,
      };

      debugPrint('🔍 Permission status: ${status.toString()}');
      return result;
    } catch (e) {
      debugPrint('❌ Error checking permissions: $e');
      return {'error': e.toString()};
    }
  }

  /// Check service initialization status
  static Future<Map<String, dynamic>> _checkServiceInitialization() async {
    try {
      debugPrint('🔍 Checking service initialization...');

      // Initialize services if needed
      await _authService.initialize();
      await _localManager.initialize();
      await _chargingManager.initialize();

      return {
        'auth_service_initialized': true,
        'local_manager_initialized': true,
        'charging_manager_initialized': true,
      };
    } catch (e) {
      debugPrint('❌ Error checking service initialization: $e');
      return {'error': e.toString()};
    }
  }

  /// Check notification channels
  static Future<Map<String, dynamic>> _checkNotificationChannels() async {
    try {
      debugPrint('🔍 Checking notification channels...');

      // This would check if notification channels are properly set up
      return {
        'channels_configured': true,
        'welcome_channel_exists': true,
        'charging_channel_exists': true,
      };
    } catch (e) {
      debugPrint('❌ Error checking notification channels: $e');
      return {'error': e.toString()};
    }
  }

  /// Test local notification functionality
  static Future<Map<String, dynamic>> _testLocalNotifications() async {
    try {
      debugPrint('🔍 Testing local notifications...');

      // Test basic notification functionality
      await _localManager.showNotification(
        id: 99999,
        title: 'Debug Test',
        body: 'This is a debug test notification',
        channelId: 'general',
        payload: 'debug_test',
      );

      return {
        'test_notification_sent': true,
        'notification_id': 99999,
      };
    } catch (e) {
      debugPrint('❌ Error testing local notifications: $e');
      return {'error': e.toString()};
    }
  }

  /// Test welcome notification flow
  static Future<Map<String, dynamic>> _testWelcomeNotificationFlow() async {
    try {
      debugPrint('🔍 Testing welcome notification flow...');

      // Test the welcome notification flow
      await _authService.testWelcomeNotification(
        userName: 'debug_user',
        isFirstLogin: false,
      );

      return {
        'welcome_flow_tested': true,
        'user_id': 'debug_user',
      };
    } catch (e) {
      debugPrint('❌ Error testing welcome notification flow: $e');
      return {'error': e.toString()};
    }
  }

  /// Check authentication integration
  static Future<Map<String, dynamic>> _checkAuthenticationIntegration() async {
    try {
      debugPrint('🔍 Checking authentication integration...');

      return {
        'auth_integration_available': true,
        'welcome_trigger_configured': true,
      };
    } catch (e) {
      debugPrint('❌ Error checking authentication integration: $e');
      return {'error': e.toString()};
    }
  }

  /// Quick debug test - just send a test notification
  static Future<void> quickDebugTest() async {
    if (!kDebugMode) return;

    try {
      debugPrint('🔍 Running quick debug test...');

      await _localManager.initialize();
      await _localManager.showNotification(
        id: 99998,
        title: 'Quick Debug Test',
        body: 'Quick test notification from debugger',
        channelId: 'general',
        payload: 'quick_debug',
      );

      debugPrint('✅ Quick debug test completed');
    } catch (e) {
      debugPrint('❌ Quick debug test failed: $e');
    }
  }

  /// Test persistent notification
  static Future<void> testPersistentNotification() async {
    if (!kDebugMode) return;

    try {
      debugPrint('🔍 Testing persistent notification...');

      await _chargingManager.initialize();
      // Note: ChargingSessionNotificationManager requires a ChargingSession object
      // For debugging purposes, we'll just test the initialization
      debugPrint(
          '🔍 ChargingSessionNotificationManager initialized successfully');

      debugPrint('✅ Persistent notification test completed');
    } catch (e) {
      debugPrint('❌ Persistent notification test failed: $e');
    }
  }

  /// Clean up test notifications
  static Future<void> cleanupTestNotifications() async {
    try {
      debugPrint('🧹 Cleaning up test notifications...');

      await _localManager.cancelNotification(99999); // Test notification
      await _localManager.cancelNotification(99998); // Quick test
      await _localManager.cancelNotification(99997); // Persistent test
      await _chargingManager.stopChargingSession(); // Any active charging

      debugPrint('✅ Test notifications cleaned up');
    } catch (e) {
      debugPrint('❌ Error cleaning up test notifications: $e');
    }
  }
}
