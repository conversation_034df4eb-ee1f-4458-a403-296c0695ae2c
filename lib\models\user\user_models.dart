/// User profile model
class UserProfile {
  final String id;
  final String name;
  final String phoneNumber;
  final String email;
  final String? profileImage;
  final List<Vehicle> vehicles;
  final String? gstNo;
  final String? businessName;
  
  UserProfile({
    required this.id,
    required this.name,
    required this.phoneNumber,
    required this.email,
    this.profileImage,
    required this.vehicles,
    this.gstNo,
    this.businessName,
  });
  
  factory UserProfile.fromJson(Map<String, dynamic> json) {
    return UserProfile(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      phoneNumber: json['phoneNumber'] ?? '',
      email: json['email'] ?? '',
      profileImage: json['profileImage'],
      vehicles: (json['vehicles'] as List<dynamic>?)
          ?.map((e) => Vehicle.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
      gstNo: json['gst_no'],
      businessName: json['business_name'],
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'phoneNumber': phoneNumber,
      'email': email,
      'profileImage': profileImage,
      'vehicles': vehicles.map((e) => e.toJson()).toList(),
      'gst_no': gstNo,
      'business_name': businessName,
    };
  }
}

/// Vehicle model
class Vehicle {
  final String id;
  final String name;
  final String licensePlate;
  final String? imageUrl;
  final bool isDefault;
  final String? brand;
  final String? model;
  final String? year;
  
  Vehicle({
    required this.id,
    required this.name,
    required this.licensePlate,
    this.imageUrl,
    required this.isDefault,
    this.brand,
    this.model,
    this.year,
  });
  
  factory Vehicle.fromJson(Map<String, dynamic> json) {
    return Vehicle(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      licensePlate: json['licensePlate'] ?? '',
      imageUrl: json['imageUrl'],
      isDefault: json['isDefault'] ?? false,
      brand: json['brand'],
      model: json['model'],
      year: json['year'],
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'licensePlate': licensePlate,
      'imageUrl': imageUrl,
      'isDefault': isDefault,
      'brand': brand,
      'model': model,
      'year': year,
    };
  }
}

/// Promocode model
class Promocode {
  final String id;
  final String code;
  final String description;
  final double discount;
  final String discountType; // 'percentage', 'fixed'
  final DateTime validUntil;
  
  Promocode({
    required this.id,
    required this.code,
    required this.description,
    required this.discount,
    required this.discountType,
    required this.validUntil,
  });
  
  factory Promocode.fromJson(Map<String, dynamic> json) {
    return Promocode(
      id: json['id'] ?? '',
      code: json['code'] ?? '',
      description: json['description'] ?? '',
      discount: (json['discount'] as num?)?.toDouble() ?? 0.0,
      discountType: json['discountType'] ?? 'percentage',
      validUntil: json['validUntil'] != null 
          ? DateTime.parse(json['validUntil']) 
          : DateTime.now().add(const Duration(days: 30)),
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'code': code,
      'description': description,
      'discount': discount,
      'discountType': discountType,
      'validUntil': validUntil.toIso8601String(),
    };
  }
}
