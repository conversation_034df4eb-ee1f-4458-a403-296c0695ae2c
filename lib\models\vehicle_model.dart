/// Model class for a vehicle
class Vehicle {
  final String id;
  final String name;
  final String licensePlate;
  final String vehicleType;
  final String connectorType;
  bool isDefault;

  Vehicle({
    required this.id,
    required this.name,
    required this.licensePlate,
    required this.vehicleType,
    required this.connectorType,
    required this.isDefault,
  });

  /// Create a Vehicle from JSON
  factory Vehicle.fromJson(Map<String, dynamic> json) {
    return Vehicle(
      id: json['id'] as String? ?? '',
      name: json['name'] as String? ?? '',
      licensePlate: json['license_plate'] as String? ?? '',
      vehicleType: json['vehicle_type'] as String? ?? '',
      connectorType: json['connector_type'] as String? ?? '',
      isDefault: json['is_default'] as bool? ?? false,
    );
  }

  /// Convert Vehicle to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'license_plate': licensePlate,
      'vehicle_type': vehicleType,
      'connector_type': connectorType,
      'is_default': isDefault,
    };
  }

  /// Create a copy of this Vehicle with the given fields replaced
  Vehicle copyWith({
    String? id,
    String? name,
    String? licensePlate,
    String? vehicleType,
    String? connectorType,
    bool? isDefault,
  }) {
    return Vehicle(
      id: id ?? this.id,
      name: name ?? this.name,
      licensePlate: licensePlate ?? this.licensePlate,
      vehicleType: vehicleType ?? this.vehicleType,
      connectorType: connectorType ?? this.connectorType,
      isDefault: isDefault ?? this.isDefault,
    );
  }
}
