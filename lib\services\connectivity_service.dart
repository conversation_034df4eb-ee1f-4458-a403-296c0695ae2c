import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';

/// Service for monitoring network connectivity
class ConnectivityService {
  // Singleton pattern
  static final ConnectivityService _instance = ConnectivityService._internal();
  factory ConnectivityService() => _instance;
  ConnectivityService._internal();

  // Connectivity change stream
  final _connectivity = Connectivity();
  final _internetChecker = InternetConnectionChecker();
  
  // Stream controllers
  final _connectionStatusController = StreamController<bool>.broadcast();
  
  // Public stream of connection status
  Stream<bool> get connectionStatus => _connectionStatusController.stream;
  
  // Current connection status
  bool _hasConnection = true;
  bool get hasConnection => _hasConnection;
  
  // Initialize the service
  void initialize() {
    // Check connection on startup
    _checkConnection();
    
    // Listen for connectivity changes
    _connectivity.onConnectivityChanged.listen((_) {
      _checkConnection();
    });
    
    // Set up periodic checks for actual internet connectivity
    Timer.periodic(const Duration(seconds: 30), (_) {
      _checkConnection();
    });
  }
  
  // Check if the device has an internet connection
  Future<void> _checkConnection() async {
    try {
      // First check with connectivity_plus (faster but less reliable)
      final connectivityResult = await _connectivity.checkConnectivity();
      
      if (connectivityResult == ConnectivityResult.none) {
        _updateConnectionStatus(false);
        return;
      }
      
      // Then verify with internet_connection_checker (more reliable but slower)
      final hasInternet = await _internetChecker.hasConnection;
      _updateConnectionStatus(hasInternet);
    } catch (e) {
      debugPrint('Error checking connection: $e');
      // Assume connection is available if check fails
      _updateConnectionStatus(true);
    }
  }
  
  // Update the connection status
  void _updateConnectionStatus(bool hasConnection) {
    if (_hasConnection != hasConnection) {
      _hasConnection = hasConnection;
      _connectionStatusController.add(hasConnection);
      
      debugPrint('Connection status changed: ${hasConnection ? 'Connected' : 'Disconnected'}');
    }
  }
  
  // Manually check connection (can be called before making important API calls)
  Future<bool> checkConnectionManually() async {
    await _checkConnection();
    return _hasConnection;
  }
  
  // Dispose resources
  void dispose() {
    _connectionStatusController.close();
  }
}
