import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:io';

/// Integration service that replaces the ChargingNotificationPinBar widget
/// with native Android notifications that have identical visual design
class PinBarNotificationIntegrationService {
  static final PinBarNotificationIntegrationService _instance =
      PinBarNotificationIntegrationService._internal();
  factory PinBarNotificationIntegrationService() => _instance;
  PinBarNotificationIntegrationService._internal();

  static const MethodChannel _channel = MethodChannel('custom_charging_notification');

  bool _isInitialized = false;
  bool _isNotificationVisible = false;
  static const int _pinBarNotificationId = 2001;

  /// Initialize the pin bar notification integration service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint('🔔 ===== INITIALIZING PIN BAR NOTIFICATION INTEGRATION =====');

      if (Platform.isAndroid) {
        debugPrint('🔔 Calling initialize method on Android...');
        final result = await _channel.invokeMethod('initialize');
        debugPrint('🔔 Initialize method result: $result');
        _isInitialized = true;
      } else {
        debugPrint('🔔 Not Android platform, skipping native initialization');
        _isInitialized = true;
      }

      debugPrint('✅ Pin Bar Notification Integration Service initialized successfully');
    } catch (e) {
      debugPrint('❌ Error initializing pin bar notification integration: $e');
      _isInitialized = false;
    }
  }

  /// Show native notification that exactly replicates the ChargingNotificationPinBar widget
  /// This replaces the in-app widget with a system notification
  /// Works for both Direct Mode and Normal Mode charging
  Future<void> showPinBarNotification({
    required bool isCharging,
    required double chargePercentage,
    required String currentPower,
    required String energyDelivered,
    required String currentPrice,
    required double carbonSavings,
    required double energySourcePercentage,
    required String chargingTimer,
    String? chargingMode, // 'direct' or 'normal'
    VoidCallback? onClose,
    VoidCallback? onTap,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    if (!Platform.isAndroid) {
      debugPrint('🔔 Not Android platform, pin bar notification not supported');
      return;
    }

    try {
      debugPrint('🔔 ===== SHOWING PIN BAR NOTIFICATION (WIDGET REPLACEMENT) =====');
      debugPrint('🔔 Charging: $isCharging');
      debugPrint('🔔 Percentage: ${(chargePercentage * 100).toInt()}%');
      debugPrint('🔔 Power: $currentPower');
      debugPrint('🔔 Energy: $energyDelivered');
      debugPrint('🔔 Price: $currentPrice');
      debugPrint('🔔 Timer: $chargingTimer');

      // Extract numeric values for display
      final powerValue = _extractNumericValue(currentPower);
      final energyValue = _extractNumericValue(energyDelivered);
      final co2Value = carbonSavings.toStringAsFixed(1);
      final renewableValue = energySourcePercentage.toStringAsFixed(0);
      final percentageText = '${(chargePercentage * 100).toInt()}%';
      final statusText = isCharging ? 'ACTIVE CHARGING' : 'CHARGING COMPLETE';

      // Prepare notification data that matches the widget's data structure
      final notificationData = {
        'notificationId': _pinBarNotificationId,
        'isCharging': isCharging,
        'chargePercentage': chargePercentage,
        'currentPower': currentPower,
        'energyDelivered': energyDelivered,
        'currentPrice': currentPrice,
        'co2Saved': '$co2Value kg',
        'chargingTimer': chargingTimer,
        'percentageText': percentageText,
        'powerValue': powerValue,
        'energyValue': energyValue,
        'co2Value': co2Value,
        'renewableValue': renewableValue,
        'statusText': statusText,
        'chargingMode': chargingMode ?? 'normal', // Include charging mode
      };

      debugPrint('🔔 Sending notification data: $notificationData');
      await _channel.invokeMethod('showCustomNotification', notificationData);

      _isNotificationVisible = true;
      debugPrint('✅ Pin bar notification shown successfully (replacing widget)');
    } catch (e) {
      debugPrint('❌ Error showing pin bar notification: $e');
      debugPrint('❌ Error details: ${e.toString()}');
    }
  }

  /// Update the existing pin bar notification with new data
  /// This is equivalent to updating the widget state
  Future<void> updatePinBarNotification({
    required bool isCharging,
    required double chargePercentage,
    required String currentPower,
    required String energyDelivered,
    required String currentPrice,
    required double carbonSavings,
    required double energySourcePercentage,
    required String chargingTimer,
    String? chargingMode, // 'direct' or 'normal'
  }) async {
    if (!_isNotificationVisible) {
      // If notification is not visible, show it instead of updating
      await showPinBarNotification(
        isCharging: isCharging,
        chargePercentage: chargePercentage,
        currentPower: currentPower,
        energyDelivered: energyDelivered,
        currentPrice: currentPrice,
        carbonSavings: carbonSavings,
        energySourcePercentage: energySourcePercentage,
        chargingTimer: chargingTimer,
        chargingMode: chargingMode,
      );
      return;
    }

    try {
      debugPrint('🔔 ===== UPDATING PIN BAR NOTIFICATION =====');

      // Use the same method as showing, which will update the existing notification
      await showPinBarNotification(
        isCharging: isCharging,
        chargePercentage: chargePercentage,
        currentPower: currentPower,
        energyDelivered: energyDelivered,
        currentPrice: currentPrice,
        carbonSavings: carbonSavings,
        energySourcePercentage: energySourcePercentage,
        chargingTimer: chargingTimer,
        chargingMode: chargingMode,
      );

      debugPrint('✅ Pin bar notification updated successfully');
    } catch (e) {
      debugPrint('❌ Error updating pin bar notification: $e');
    }
  }

  /// Hide the pin bar notification (equivalent to setting widget visibility to false)
  Future<void> hidePinBarNotification() async {
    if (!Platform.isAndroid || !_isNotificationVisible) return;

    try {
      debugPrint('🔔 ===== HIDING PIN BAR NOTIFICATION =====');

      await _channel.invokeMethod('hideNotification', {
        'notificationId': _pinBarNotificationId,
      });

      _isNotificationVisible = false;
      debugPrint('✅ Pin bar notification hidden successfully');
    } catch (e) {
      debugPrint('❌ Error hiding pin bar notification: $e');
    }
  }

  /// Check if the notification is currently visible
  bool get isNotificationVisible => _isNotificationVisible;

  /// Check if the service is initialized
  bool get isInitialized => _isInitialized;

  /// Extract numeric value from strings like "7.2 kW" -> "7.2"
  String _extractNumericValue(String value) {
    final regex = RegExp(r'[\d.]+');
    final match = regex.firstMatch(value);
    return match?.group(0) ?? '0';
  }

  /// Request notification permissions (Android 13+)
  Future<bool> requestPermissions() async {
    if (!Platform.isAndroid) return true;

    try {
      final result = await _channel.invokeMethod('requestPermissions');
      return result as bool? ?? false;
    } catch (e) {
      debugPrint('❌ Error requesting notification permissions: $e');
      return false;
    }
  }

  /// Check if notifications are supported on this platform
  Future<bool> isSupported() async {
    if (!Platform.isAndroid) return false;

    try {
      final result = await _channel.invokeMethod('isSupported');
      return result as bool? ?? false;
    } catch (e) {
      debugPrint('❌ Error checking notification support: $e');
      return false;
    }
  }

  /// Dispose of the service and clean up resources
  Future<void> dispose() async {
    if (_isNotificationVisible) {
      await hidePinBarNotification();
    }
    _isInitialized = false;
    debugPrint('🔔 Pin Bar Notification Integration Service disposed');
  }
}
