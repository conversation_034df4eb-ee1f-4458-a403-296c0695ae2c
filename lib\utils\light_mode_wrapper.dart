import 'package:flutter/material.dart';

/// A wrapper widget that forces light mode for specific pages
/// regardless of the app's theme setting
class LightModeWrapper extends StatelessWidget {
  final Widget child;

  const LightModeWrapper({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    // Force light theme for this widget and its children
    return Theme(
      data: ThemeData.light().copyWith(
        // Copy important theme attributes from the app's light theme
        primaryColor: Theme.of(context).primaryColor,
        colorScheme: ColorScheme.light(
          primary: Theme.of(context).primaryColor,
          secondary: Theme.of(context).colorScheme.secondary,
        ),
        appBarTheme: AppBarTheme(
          backgroundColor: Colors.white,
          foregroundColor: Colors.black87,
          elevation: 0,
          iconTheme: IconThemeData(color: Theme.of(context).primaryColor),
        ),
      ),
      child: child,
    );
  }
}
