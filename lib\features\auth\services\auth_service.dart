import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../repositories/auth_repository.dart';
import '../../../core/models/api_response.dart';
import '../models/auth_models.dart';
import '../../../shared/models/user.dart';

/// Service for handling authentication
class AuthService {
  final AuthRepository _authRepository;
  
  // Keys for storing data in SharedPreferences
  static const String _userDataKey = 'user_data';
  static const String _lastPhoneNumberKey = 'last_phone_number';
  static const String _lastOtpTimeKey = 'last_otp_time';

  AuthService(this._authRepository);

  /// Send OTP to the provided phone number
  Future<ApiResponse<LoginResponse>> sendOtp(String phoneNumber) async {
    try {
      // Save the last phone number used
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_lastPhoneNumberKey, phoneNumber);
      await prefs.setInt(_lastOtpTimeKey, DateTime.now().millisecondsSinceEpoch);

      // Call the repository to send OTP
      return await _authRepository.login(phoneNumber);
    } catch (e) {
      debugPrint('Error in sendOtp: $e');
      return ApiResponse<LoginResponse>.error('Failed to send OTP: $e');
    }
  }

  /// Verify OTP
  Future<ApiResponse<VerifyOtpResponse>> verifyOtp(String phoneNumber, String otp) async {
    try {
      // Call the repository to verify OTP
      final response = await _authRepository.verifyOtp(phoneNumber, otp);

      // If successful, save user data
      if (response.success && response.data != null && response.data!.userInfo != null) {
        await _saveUserData(response.data!.userInfo!);
      }

      return response;
    } catch (e) {
      debugPrint('Error in verifyOtp: $e');
      return ApiResponse<VerifyOtpResponse>.error('Failed to verify OTP: $e');
    }
  }

  /// Get user profile
  Future<ApiResponse<User>> getUserProfile() async {
    try {
      // Call the repository to get user profile
      return await _authRepository.getUserProfile();
    } catch (e) {
      debugPrint('Error in getUserProfile: $e');
      return ApiResponse<User>.error('Failed to get user profile: $e');
    }
  }

  /// Update user profile
  Future<ApiResponse<User>> updateUserProfile(Map<String, dynamic> userData) async {
    try {
      // Call the repository to update user profile
      return await _authRepository.updateUserProfile(userData);
    } catch (e) {
      debugPrint('Error in updateUserProfile: $e');
      return ApiResponse<User>.error('Failed to update user profile: $e');
    }
  }

  /// Logout user
  Future<ApiResponse<void>> logout() async {
    try {
      // Call the repository to logout
      final response = await _authRepository.logout();

      // Clear user data
      await _clearUserData();

      return response;
    } catch (e) {
      debugPrint('Error in logout: $e');
      return ApiResponse<void>.error('Failed to logout: $e');
    }
  }

  /// Check if user is logged in
  Future<bool> isLoggedIn() async {
    return await _authRepository.isLoggedIn();
  }

  /// Save user data to SharedPreferences
  Future<void> _saveUserData(UserInfo userInfo) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_userDataKey, userInfo.toJson().toString());
    } catch (e) {
      debugPrint('Error saving user data: $e');
    }
  }

  /// Clear user data from SharedPreferences
  Future<void> _clearUserData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_userDataKey);
    } catch (e) {
      debugPrint('Error clearing user data: $e');
    }
  }

  /// Get the last phone number used
  Future<String?> getLastPhoneNumber() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_lastPhoneNumberKey);
    } catch (e) {
      debugPrint('Error getting last phone number: $e');
      return null;
    }
  }

  /// Get the time when the last OTP was sent
  Future<DateTime?> getLastOtpTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final timestamp = prefs.getInt(_lastOtpTimeKey);
      if (timestamp != null) {
        return DateTime.fromMillisecondsSinceEpoch(timestamp);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting last OTP time: $e');
      return null;
    }
  }
}
