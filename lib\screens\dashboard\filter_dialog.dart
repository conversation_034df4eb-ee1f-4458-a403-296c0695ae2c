import 'package:flutter/material.dart';

class FilterDialog extends StatefulWidget {
  final Map<String, bool> selectedConnectorFilters;
  final bool showOnlyAvailable;
  final Function(Map<String, bool>, bool) onApplyFilters;

  const FilterDialog({
    super.key,
    required this.selectedConnectorFilters,
    required this.showOnlyAvailable,
    required this.onApplyFilters,
  });

  @override
  State<FilterDialog> createState() => _FilterDialogState();
}

class _FilterDialogState extends State<FilterDialog> {
  late Map<String, bool> _selectedConnectorFilters;
  late bool _showOnlyAvailable;
  bool _hasChanges = false;

  @override
  void initState() {
    super.initState();
    // Create a copy of the filters to avoid modifying the original
    _selectedConnectorFilters = Map.from(widget.selectedConnectorFilters);
    _showOnlyAvailable = widget.showOnlyAvailable;
  }

  void _checkForChanges() {
    bool connectorChanges = false;

    for (var key in widget.selectedConnectorFilters.keys) {
      if (widget.selectedConnectorFilters[key] !=
          _selectedConnectorFilters[key]) {
        connectorChanges = true;
        break;
      }
    }

    setState(() {
      _hasChanges =
          connectorChanges || widget.showOnlyAvailable != _showOnlyAvailable;
    });
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final primaryColor = const Color(0xFF67C44C);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
        side: isDarkMode
            ? BorderSide(color: const Color(0xFF67C44C).withAlpha(50), width: 1)
            : BorderSide.none,
      ),
      elevation: isDarkMode ? 0 : 8,
      backgroundColor: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: const Color(0xFF67C44C).withAlpha(20),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.filter_alt_rounded,
                    color: Color(0xFF67C44C),
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  'Filter Stations',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode ? Colors.white : Colors.black,
                  ),
                ),
                const Spacer(),
                Container(
                  decoration: BoxDecoration(
                    color: isDarkMode
                        ? const Color(0xFF262626)
                        : Colors.grey.shade100,
                    shape: BoxShape.circle,
                  ),
                  child: IconButton(
                    icon: const Icon(Icons.close, size: 20),
                    onPressed: () => Navigator.pop(context),
                    color: isDarkMode ? Colors.white70 : Colors.black54,
                    padding: const EdgeInsets.all(8),
                    constraints: const BoxConstraints(),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // Availability filter
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: primaryColor.withAlpha(20),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.power_rounded,
                    color: primaryColor,
                    size: 16,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  'Availability',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: isDarkMode ? Colors.white : Colors.black87,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Container(
              decoration: BoxDecoration(
                color:
                    isDarkMode ? const Color(0xFF262626) : Colors.grey.shade50,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: isDarkMode
                      ? const Color(0xFF2E2E2E)
                      : Colors.grey.shade200,
                ),
              ),
              child: SwitchListTile(
                title: Text(
                  'Show only available stations',
                  style: TextStyle(
                    color: isDarkMode ? Colors.white70 : Colors.black87,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                value: _showOnlyAvailable,
                activeColor: primaryColor,
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                onChanged: (value) {
                  setState(() {
                    _showOnlyAvailable = value;
                  });
                  _checkForChanges();
                },
              ),
            ),
            const SizedBox(height: 20),

            // Connector types
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: primaryColor.withAlpha(20),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.electrical_services_rounded,
                    color: primaryColor,
                    size: 16,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  'Connector Types',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: isDarkMode ? Colors.white : Colors.black87,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            // Connector type checkboxes
            ..._selectedConnectorFilters.entries.map((entry) {
              return CheckboxListTile(
                title: Text(
                  entry.key,
                  style: TextStyle(
                    color: isDarkMode ? Colors.white70 : Colors.black87,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                value: entry.value,
                activeColor: primaryColor,
                contentPadding: EdgeInsets.zero,
                controlAffinity: ListTileControlAffinity.leading,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                onChanged: (value) {
                  setState(() {
                    _selectedConnectorFilters[entry.key] = value ?? false;
                  });
                  _checkForChanges();
                },
              );
            }),

            const SizedBox(height: 20),

            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                OutlinedButton.icon(
                  onPressed: () {
                    // Reset all filters
                    setState(() {
                      for (var key in _selectedConnectorFilters.keys) {
                        _selectedConnectorFilters[key] = false;
                      }
                      _showOnlyAvailable = false;
                    });
                    _checkForChanges();
                  },
                  icon: Icon(
                    Icons.refresh_rounded,
                    size: 18,
                    color: isDarkMode ? Colors.white70 : Colors.black54,
                  ),
                  label: Text(
                    'Reset All',
                    style: TextStyle(
                      color: isDarkMode ? Colors.white70 : Colors.black54,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  style: OutlinedButton.styleFrom(
                    side: BorderSide(
                      color: isDarkMode
                          ? Colors.grey.shade700
                          : Colors.grey.shade300,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _hasChanges
                      ? () {
                          widget.onApplyFilters(
                              _selectedConnectorFilters, _showOnlyAvailable);
                          Navigator.pop(context);
                        }
                      : null,
                  icon: const Icon(
                    Icons.check_rounded,
                    size: 18,
                  ),
                  label: const Text(
                    'Apply Filters',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: primaryColor,
                    foregroundColor: Colors.white,
                    disabledBackgroundColor: isDarkMode
                        ? Colors.grey.shade800
                        : Colors.grey.shade300,
                    disabledForegroundColor: isDarkMode
                        ? Colors.grey.shade500
                        : Colors.grey.shade600,
                    elevation: 0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
