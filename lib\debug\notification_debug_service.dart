import 'package:flutter/foundation.dart';
import 'package:ecoplug/services/local_notification_manager.dart';
import 'package:ecoplug/services/welcome_notification_service.dart';
import 'package:ecoplug/services/charging_session_notification_manager.dart';
import 'package:ecoplug/models/charging_session.dart';

/// Notification Debug Service
/// Comprehensive debugging and testing service for all notification systems
/// Helps identify and fix notification infrastructure issues
class NotificationDebugService {
  static final NotificationDebugService _instance = NotificationDebugService._internal();
  factory NotificationDebugService() => _instance;
  NotificationDebugService._internal();

  final LocalNotificationManager _localManager = LocalNotificationManager();
  final WelcomeNotificationService _welcomeService = WelcomeNotificationService();
  final ChargingSessionNotificationManager _chargingManager = ChargingSessionNotificationManager();

  /// Run comprehensive notification system diagnosis
  Future<Map<String, dynamic>> runFullDiagnosis() async {
    if (!kDebugMode) {
      return {'error': 'Debug mode only'};
    }

    debugPrint('🔍 ===== RUNNING FULL NOTIFICATION SYSTEM DIAGNOSIS =====');

    final results = <String, dynamic>{
      'timestamp': DateTime.now().toIso8601String(),
      'platform': 'Android',
    };

    try {
      // Step 1: Test local notification manager
      debugPrint('🔍 Step 1: Testing Local Notification Manager...');
      results['local_manager'] = await _testLocalNotificationManager();

      // Step 2: Test welcome notification service
      debugPrint('🔍 Step 2: Testing Welcome Notification Service...');
      results['welcome_service'] = await _testWelcomeNotificationService();

      // Step 3: Test charging notification manager
      debugPrint('🔍 Step 3: Testing Charging Notification Manager...');
      results['charging_manager'] = await _testChargingNotificationManager();

      // Step 4: Test actual notification display
      debugPrint('🔍 Step 4: Testing Actual Notification Display...');
      results['notification_display'] = await _testNotificationDisplay();

      // Step 5: Overall assessment
      results['overall_status'] = _assessOverallStatus(results);

      debugPrint('✅ Full notification diagnosis completed');
      return results;
    } catch (e) {
      debugPrint('❌ Error during notification diagnosis: $e');
      results['error'] = e.toString();
      results['overall_status'] = 'error';
      return results;
    }
  }

  /// Test local notification manager
  Future<Map<String, dynamic>> _testLocalNotificationManager() async {
    final result = <String, dynamic>{};

    try {
      // Initialize if not already done
      await _localManager.initialize();

      // Get status
      final status = _localManager.getStatus();
      result['status'] = status;
      result['initialized'] = status['initialized'];
      result['permissions_granted'] = status['permissions_granted'];
      result['channels_created'] = status['channels_created'];

      // Test basic notification
      final testSuccess = await _localManager.testNotification();
      result['test_notification_success'] = testSuccess;

      result['overall_success'] = status['initialized'] && 
                                 status['permissions_granted'] && 
                                 testSuccess;

      debugPrint('🔍 Local Manager Test: ${result['overall_success'] ? 'PASSED' : 'FAILED'}');
    } catch (e) {
      result['error'] = e.toString();
      result['overall_success'] = false;
      debugPrint('❌ Local Manager Test: FAILED - $e');
    }

    return result;
  }

  /// Test welcome notification service
  Future<Map<String, dynamic>> _testWelcomeNotificationService() async {
    final result = <String, dynamic>{};

    try {
      // Initialize service
      await _welcomeService.initialize();

      // Check if initialized
      result['initialized'] = _welcomeService.isInitialized;

      // Check if notifications are enabled
      final notificationsEnabled = await _welcomeService.areNotificationsEnabled();
      result['notifications_enabled'] = notificationsEnabled;

      // Test welcome notification
      debugPrint('🔍 Testing welcome notification display...');
      await _welcomeService.showWelcomeNotification(
        userName: 'Debug User',
        isFirstLogin: true,
      );
      result['welcome_notification_sent'] = true;

      result['overall_success'] = result['initialized'] && 
                                 result['notifications_enabled'] && 
                                 result['welcome_notification_sent'];

      debugPrint('🔍 Welcome Service Test: ${result['overall_success'] ? 'PASSED' : 'FAILED'}');
    } catch (e) {
      result['error'] = e.toString();
      result['overall_success'] = false;
      debugPrint('❌ Welcome Service Test: FAILED - $e');
    }

    return result;
  }

  /// Test charging notification manager
  Future<Map<String, dynamic>> _testChargingNotificationManager() async {
    final result = <String, dynamic>{};

    try {
      // Initialize manager
      await _chargingManager.initialize();

      // Check if initialized
      result['initialized'] = _chargingManager.isInitialized;

      // Create test charging session
      final testSession = ChargingSession(
        id: 'DEBUG_SESSION_${DateTime.now().millisecondsSinceEpoch}',
        stationUid: 'DEBUG_STATION',
        connectorId: 'DEBUG_CONNECTOR',
        startTime: DateTime.now(),
        currentCharge: 0.65, // 65%
        currentPower: 7.5, // 7.5 kW
        energyDelivered: 15.2, // 15.2 kWh
        cost: 228.50, // ₹228.50
        co2Saved: 8.7, // 8.7 kg CO2
      );

      // Test starting charging notification
      debugPrint('🔍 Testing charging notification start...');
      await _chargingManager.startChargingSession(testSession);
      result['charging_notification_started'] = true;

      // Wait a moment
      await Future.delayed(const Duration(seconds: 2));

      // Test updating charging notification
      debugPrint('🔍 Testing charging notification update...');
      final updatedSession = testSession.copyWith(
        currentCharge: 0.75, // 75%
        currentPower: 6.8, // 6.8 kW
        energyDelivered: 18.5, // 18.5 kWh
        cost: 277.50, // ₹277.50
        co2Saved: 10.2, // 10.2 kg CO2
      );
      await _chargingManager.updateChargingSession(updatedSession);
      result['charging_notification_updated'] = true;

      // Wait a moment
      await Future.delayed(const Duration(seconds: 2));

      // Test stopping charging notification
      debugPrint('🔍 Testing charging notification stop...');
      await _chargingManager.stopChargingSession();
      result['charging_notification_stopped'] = true;

      result['overall_success'] = result['initialized'] && 
                                 result['charging_notification_started'] && 
                                 result['charging_notification_updated'] && 
                                 result['charging_notification_stopped'];

      debugPrint('🔍 Charging Manager Test: ${result['overall_success'] ? 'PASSED' : 'FAILED'}');
    } catch (e) {
      result['error'] = e.toString();
      result['overall_success'] = false;
      debugPrint('❌ Charging Manager Test: FAILED - $e');
    }

    return result;
  }

  /// Test actual notification display
  Future<Map<String, dynamic>> _testNotificationDisplay() async {
    final result = <String, dynamic>{};

    try {
      debugPrint('🔍 Testing direct notification display...');

      // Test 1: Simple notification
      final simpleSuccess = await _localManager.showNotification(
        id: 99998,
        title: 'Debug Test Notification',
        body: 'This is a debug test notification to verify display functionality.',
        channelId: 'user_welcome',
        payload: 'debug_test',
      );
      result['simple_notification'] = simpleSuccess;

      await Future.delayed(const Duration(seconds: 1));

      // Test 2: Persistent notification
      final persistentSuccess = await _localManager.showNotification(
        id: 99997,
        title: 'Debug Persistent Notification',
        body: 'This is a persistent debug notification that cannot be dismissed.',
        channelId: 'active_charging_session',
        payload: 'debug_persistent',
        ongoing: true,
        autoCancel: false,
        progress: 50,
        maxProgress: 100,
      );
      result['persistent_notification'] = persistentSuccess;

      result['overall_success'] = simpleSuccess && persistentSuccess;

      debugPrint('🔍 Notification Display Test: ${result['overall_success'] ? 'PASSED' : 'FAILED'}');
    } catch (e) {
      result['error'] = e.toString();
      result['overall_success'] = false;
      debugPrint('❌ Notification Display Test: FAILED - $e');
    }

    return result;
  }

  /// Assess overall status
  String _assessOverallStatus(Map<String, dynamic> results) {
    final localSuccess = results['local_manager']?['overall_success'] == true;
    final welcomeSuccess = results['welcome_service']?['overall_success'] == true;
    final chargingSuccess = results['charging_manager']?['overall_success'] == true;
    final displaySuccess = results['notification_display']?['overall_success'] == true;

    if (localSuccess && welcomeSuccess && chargingSuccess && displaySuccess) {
      return 'all_systems_working';
    } else if (localSuccess && displaySuccess) {
      return 'core_working_services_issues';
    } else if (localSuccess) {
      return 'manager_working_display_issues';
    } else {
      return 'critical_infrastructure_failure';
    }
  }

  /// Quick notification test
  Future<bool> quickNotificationTest() async {
    if (!kDebugMode) return false;

    try {
      debugPrint('🔍 Running quick notification test...');

      await _localManager.initialize();
      final success = await _localManager.testNotification();

      if (success) {
        debugPrint('✅ Quick notification test: PASSED');
      } else {
        debugPrint('❌ Quick notification test: FAILED');
      }

      return success;
    } catch (e) {
      debugPrint('❌ Quick notification test error: $e');
      return false;
    }
  }

  /// Test welcome notification specifically
  Future<bool> testWelcomeNotification() async {
    if (!kDebugMode) return false;

    try {
      debugPrint('🔍 Testing welcome notification specifically...');

      await _welcomeService.initialize();
      await _welcomeService.showWelcomeNotification(
        userName: 'Test User',
        isFirstLogin: true,
      );

      debugPrint('✅ Welcome notification test completed');
      debugPrint('🔍 Check Android notification tray for "Welcome to EcoPlug! 😊⚡"');
      return true;
    } catch (e) {
      debugPrint('❌ Welcome notification test error: $e');
      return false;
    }
  }

  /// Clean up test notifications
  Future<void> cleanupTestNotifications() async {
    try {
      debugPrint('🧹 Cleaning up test notifications...');

      await _localManager.cancelNotification(99999); // Test notification
      await _localManager.cancelNotification(99998); // Debug test
      await _localManager.cancelNotification(99997); // Debug persistent
      await _chargingManager.stopChargingSession(); // Any active charging

      debugPrint('✅ Test notifications cleaned up');
    } catch (e) {
      debugPrint('❌ Error cleaning up test notifications: $e');
    }
  }

  /// Get notification system status
  Map<String, dynamic> getNotificationSystemStatus() {
    return {
      'local_manager_ready': _localManager.isReady,
      'welcome_service_initialized': _welcomeService.isInitialized,
      'charging_manager_initialized': _chargingManager.isInitialized,
      'local_manager_status': _localManager.getStatus(),
    };
  }
}
