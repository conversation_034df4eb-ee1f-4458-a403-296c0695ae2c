<?xml version="1.0" encoding="utf-8"?>
<!-- Collapsed notification layout that shows essential charging info -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:background="@drawable/notification_background"
    android:padding="12dp"
    android:gravity="center_vertical">

    <!-- App icon -->
    <ImageView
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:src="@mipmap/ic_launcher"
        android:layout_marginEnd="12dp" />

    <!-- Main content -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <!-- Status and percentage -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <!-- Active charging indicator -->
            <View
                android:id="@+id/charging_indicator_collapsed"
                android:layout_width="6dp"
                android:layout_height="6dp"
                android:background="@drawable/charging_indicator_active"
                android:layout_marginEnd="6dp" />

            <!-- Status text -->
            <TextView
                android:id="@+id/status_text_collapsed"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="ACTIVE CHARGING"
                android:textColor="@color/charging_green"
                android:textSize="12sp"
                android:textStyle="bold"
                android:layout_marginEnd="8dp" />

            <!-- Battery percentage -->
            <TextView
                android:id="@+id/battery_percentage_collapsed"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="68%"
                android:textColor="@color/charging_green"
                android:textSize="12sp"
                android:textStyle="bold"
                android:layout_marginEnd="8dp" />

            <!-- Timer -->
            <TextView
                android:id="@+id/charging_timer_collapsed"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="00:45:32"
                android:textColor="@color/text_secondary"
                android:textSize="12sp"
                android:textStyle="normal" />
        </LinearLayout>

        <!-- Power and energy info -->
        <TextView
            android:id="@+id/power_energy_info"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Power: 150 kW • Energy: 32.4 kWh • Cost: ₹12.80"
            android:textColor="@color/text_secondary"
            android:textSize="11sp"
            android:textStyle="normal"
            android:layout_marginTop="2dp" />
    </LinearLayout>

    <!-- Progress indicator -->
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:layout_marginStart="8dp">

        <!-- Circular progress indicator -->
        <FrameLayout
            android:layout_width="32dp"
            android:layout_height="32dp">

            <!-- Background circle -->
            <View
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/circular_progress_background" />

            <!-- Progress circle (will be updated programmatically) -->
            <View
                android:id="@+id/circular_progress"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/circular_progress_fill" />

            <!-- Percentage text -->
            <TextView
                android:id="@+id/circular_percentage"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="68"
                android:textColor="@color/charging_green"
                android:textSize="10sp"
                android:textStyle="bold" />
        </FrameLayout>
    </LinearLayout>
</LinearLayout>
