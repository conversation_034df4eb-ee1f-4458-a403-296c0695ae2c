<?xml version="1.0" encoding="utf-8"?>
<!-- EcoPlug Notification Icon -->
<!-- Monochrome vector drawable based on actual EcoPlug logo -->
<!-- Follows Android notification icon guidelines while maintaining brand identity -->
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="24"
    android:viewportHeight="24"
    android:tint="@android:color/white">

    <!-- EcoPlug logo simplified for notification icon -->
    <!-- Based on assets/images/ecoplug_logo_dark.png -->
    <group>
        <!-- Main circular background representing the EcoPlug brand -->
        <path
            android:fillColor="@android:color/white"
            android:pathData="M12,2 C17.52,2 22,6.48 22,12 C22,17.52 17.52,22 12,22 C6.48,22 2,17.52 2,12 C2,6.48 6.48,2 12,2 Z" />

        <!-- Inner circle for depth -->
        <path
            android:fillColor="@android:color/black"
            android:pathData="M12,4 C16.42,4 20,7.58 20,12 C20,16.42 16.42,20 12,20 C7.58,20 4,16.42 4,12 C4,7.58 7.58,4 12,4 Z" />

        <!-- EcoPlug "E" letter stylized -->
        <path
            android:fillColor="@android:color/white"
            android:pathData="M8,8 L16,8 L16,10 L10,10 L10,11 L15,11 L15,13 L10,13 L10,14 L16,14 L16,16 L8,16 Z" />

        <!-- Electric plug connector element -->
        <path
            android:fillColor="@android:color/white"
            android:pathData="M11,6 L13,6 L13,8 L11,8 Z" />

        <!-- Eco leaf accent -->
        <path
            android:fillColor="@android:color/white"
            android:pathData="M14,16 C14,16 16,17 17,18 C17.5,18.5 17.5,19 17,19 C16,19 14,17.5 14,16 Z" />
    </group>
</vector>
