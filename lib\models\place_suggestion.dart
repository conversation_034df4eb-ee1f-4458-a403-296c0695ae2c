import 'package:google_maps_flutter/google_maps_flutter.dart';

/// Model for place suggestions from Google Places API
class PlaceSuggestion {
  final String placeId;
  final String description;
  final String mainText;
  final String secondaryText;
  final LatLng? coordinates;
  final String? formattedAddress;

  const PlaceSuggestion({
    required this.placeId,
    required this.description,
    required this.mainText,
    required this.secondaryText,
    this.coordinates,
    this.formattedAddress,
  });

  /// Create PlaceSuggestion from Google Places API response
  factory PlaceSuggestion.fromPrediction(Map<String, dynamic> prediction) {
    final structuredFormatting = prediction['structured_formatting'] ?? {};
    
    return PlaceSuggestion(
      placeId: prediction['place_id'] ?? '',
      description: prediction['description'] ?? '',
      mainText: structuredFormatting['main_text'] ?? '',
      secondaryText: structuredFormatting['secondary_text'] ?? '',
    );
  }

  /// Create PlaceSuggestion with coordinates from Place Details API
  PlaceSuggestion copyWithCoordinates(LatLng coordinates, String? formattedAddress) {
    return PlaceSuggestion(
      placeId: placeId,
      description: description,
      mainText: mainText,
      secondaryText: secondaryText,
      coordinates: coordinates,
      formattedAddress: formattedAddress ?? description,
    );
  }

  /// Create a current location suggestion
  factory PlaceSuggestion.currentLocation(LatLng coordinates) {
    return PlaceSuggestion(
      placeId: 'current_location',
      description: 'Current Location',
      mainText: 'Current Location',
      secondaryText: 'Using GPS',
      coordinates: coordinates,
      formattedAddress: 'Current Location',
    );
  }

  @override
  String toString() {
    return 'PlaceSuggestion(placeId: $placeId, description: $description, coordinates: $coordinates)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PlaceSuggestion && other.placeId == placeId;
  }

  @override
  int get hashCode => placeId.hashCode;
}
