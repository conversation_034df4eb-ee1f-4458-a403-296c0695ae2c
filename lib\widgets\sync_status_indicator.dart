import 'dart:async';
import 'package:flutter/material.dart';
import '../services/sync_service.dart';

/// A widget that shows the sync status of the app
class SyncStatusIndicator extends StatefulWidget {
  const SyncStatusIndicator({super.key});

  @override
  State<SyncStatusIndicator> createState() => _SyncStatusIndicatorState();
}

class _SyncStatusIndicatorState extends State<SyncStatusIndicator> {
  final SyncService _syncService = SyncService();
  bool _hasPendingSync = false;
  bool _isSyncing = false;
  StreamSubscription? _syncStatusSubscription;

  @override
  void initState() {
    super.initState();
    _checkSyncStatus();

    // Listen for sync status changes
    _syncStatusSubscription = _syncService.syncStatusStream.listen((status) {
      if (mounted) {
        setState(() {
          _isSyncing = status.isSyncing;
          _hasPendingSync = status.pendingItems > 0;
        });
      }
    });
  }

  @override
  void dispose() {
    _syncStatusSubscription?.cancel();
    super.dispose();
  }

  Future<void> _checkSyncStatus() async {
    final status = await _syncService.getSyncStatus();
    if (mounted) {
      setState(() {
        _isSyncing = status.isSyncing;
        _hasPendingSync = status.pendingItems > 0;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // Don't show anything if there's no pending sync
    if (!_hasPendingSync && !_isSyncing) {
      return const SizedBox.shrink();
    }

    return GestureDetector(
      onTap: () {
        // Show sync details dialog
        _showSyncDetailsDialog(context);
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: _isSyncing
              ? Colors.blue.withAlpha(51) // 0.2 * 255 = ~51
              : Colors.orange.withAlpha(51), // 0.2 * 255 = ~51
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (_isSyncing)
              const SizedBox(
                width: 12,
                height: 12,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                ),
              )
            else
              Icon(
                Icons.sync_problem,
                size: 14,
                color: Colors.orange.shade700,
              ),
            const SizedBox(width: 4),
            Text(
              _isSyncing ? 'Syncing...' : 'Pending sync',
              style: TextStyle(
                fontSize: 12,
                color:
                    _isSyncing ? Colors.blue.shade700 : Colors.orange.shade700,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showSyncDetailsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sync Status'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              _isSyncing
                  ? 'Your data is currently being synced with the server.'
                  : 'You have pending changes that need to be synced with the server.',
              style: const TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 16),
            if (!_isSyncing) ...[
              const Text(
                'These changes will be automatically synced when your internet connection is stable.',
                style: TextStyle(fontSize: 14),
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  // Trigger manual sync
                  _syncService.syncNow();
                  Navigator.of(context).pop();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF34C759),
                  foregroundColor: Colors.white,
                ),
                child: const Text('Sync Now'),
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
