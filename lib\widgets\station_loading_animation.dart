import 'dart:math' as math;
import 'package:flutter/material.dart';

/// A modern, animated loading widget for station details
class StationLoadingAnimation extends StatefulWidget {
  final String stationName;
  final String? errorMessage;
  final String? loadingMessage;
  final VoidCallback? onRetry;

  const StationLoadingAnimation({
    super.key,
    this.stationName = 'Charging Station',
    this.errorMessage,
    this.loadingMessage,
    this.onRetry,
  });

  @override
  State<StationLoadingAnimation> createState() =>
      _StationLoadingAnimationState();
}

class _StationLoadingAnimationState extends State<StationLoadingAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _pulseAnimation;
  late Animation<double> _rotateAnimation;
  late Animation<double> _progressAnimation;

  @override
  void initState() {
    super.initState();

    // Create animation controller
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2000),
    )..repeat();

    // Create pulse animation
    _pulseAnimation = TweenSequence<double>([
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.0, end: 1.2)
            .chain(CurveTween(curve: Curves.easeInOut)),
        weight: 50,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.2, end: 1.0)
            .chain(CurveTween(curve: Curves.easeInOut)),
        weight: 50,
      ),
    ]).animate(_controller);

    // Create rotation animation
    _rotateAnimation = Tween<double>(
      begin: 0.0,
      end: 2 * math.pi,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.linear,
      ),
    );

    // Create progress animation
    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.0, 1.0, curve: Curves.easeInOut),
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final primaryColor = Theme.of(context).colorScheme.primary;
    final errorColor = Theme.of(context).colorScheme.error;

    // Check if we have an error message
    final hasError =
        widget.errorMessage != null && widget.errorMessage!.isNotEmpty;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Animated charging icon or error icon
          AnimatedBuilder(
            animation: _controller,
            builder: (context, child) {
              return Transform.scale(
                scale: _pulseAnimation.value,
                child: Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    color: isDarkMode
                        ? Colors.grey.shade800
                        : Colors.grey.shade100,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: hasError
                            ? errorColor.withAlpha(76) // 0.3 opacity
                            : primaryColor.withAlpha(76), // 0.3 opacity
                        blurRadius: 20,
                        spreadRadius: 5,
                      ),
                    ],
                  ),
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      // Rotating circle or error icon
                      if (hasError)
                        Icon(
                          Icons.error_outline,
                          size: 60,
                          color: errorColor,
                        )
                      else
                        Transform.rotate(
                          angle: _rotateAnimation.value,
                          child: CircularProgressIndicator(
                            value: null,
                            strokeWidth: 4,
                            color: primaryColor,
                            backgroundColor: isDarkMode
                                ? Colors.grey.shade700
                                : Colors.grey.shade300,
                          ),
                        ),

                      // Lightning bolt icon (only shown when no error)
                      if (!hasError)
                        Icon(
                          Icons.bolt,
                          size: 50,
                          color: primaryColor,
                        ),
                    ],
                  ),
                ),
              );
            },
          ),

          const SizedBox(height: 40),

          // Error message or loading text
          if (hasError)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                widget.errorMessage!,
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: errorColor,
                      fontWeight: FontWeight.w500,
                    ),
                textAlign: TextAlign.center,
              ),
            )
          else
            Text(
              widget.loadingMessage ?? 'Preparing station details...',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: isDarkMode
                        ? Colors.grey.shade300
                        : Colors.grey.shade700,
                  ),
              textAlign: TextAlign.center,
            ),

          const SizedBox(height: 24),

          // Retry button or progress indicator
          if (hasError && widget.onRetry != null)
            ElevatedButton.icon(
              onPressed: widget.onRetry,
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
              style: ElevatedButton.styleFrom(
                backgroundColor: primaryColor,
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
            )
          else if (!hasError)
            SizedBox(
              width: 200,
              child: AnimatedBuilder(
                animation: _progressAnimation,
                builder: (context, child) {
                  return LinearProgressIndicator(
                    value: null, // Indeterminate
                    backgroundColor: isDarkMode
                        ? Colors.grey.shade800
                        : Colors.grey.shade200,
                    color: primaryColor,
                    minHeight: 6,
                    borderRadius: BorderRadius.circular(3),
                  );
                },
              ),
            ),

          const SizedBox(height: 40),

          // Loading tips (only shown when no error)
          if (!hasError) const LoadingTips(),
        ],
      ),
    );
  }
}

/// Shimmer text effect for loading animation
class ShimmerText extends StatefulWidget {
  final String text;
  final TextStyle? style;

  const ShimmerText({
    super.key,
    required this.text,
    this.style,
  });

  @override
  State<ShimmerText> createState() => _ShimmerTextState();
}

class _ShimmerTextState extends State<ShimmerText>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    )..repeat(reverse: true);

    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(_controller);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return ShaderMask(
          shaderCallback: (bounds) {
            return LinearGradient(
              colors: [
                Colors.grey.shade500,
                Theme.of(context).colorScheme.primary,
                Colors.grey.shade500,
              ],
              stops: [
                _animation.value - 0.3,
                _animation.value,
                _animation.value + 0.3,
              ],
            ).createShader(bounds);
          },
          child: Text(
            widget.text,
            style: widget.style?.copyWith(
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
        );
      },
    );
  }
}

/// Rotating loading tips
class LoadingTips extends StatefulWidget {
  const LoadingTips({super.key});

  @override
  State<LoadingTips> createState() => _LoadingTipsState();
}

class _LoadingTipsState extends State<LoadingTips> {
  final List<String> _tips = [
    'Did you know? Electric vehicles produce zero direct emissions.',
    'Charging at non-peak hours can save you money.',
    'Most EV owners charge their vehicles at home.',
    'Fast chargers can add up to 100 miles of range in just 30 minutes.',
    'EVs have fewer moving parts, resulting in lower maintenance costs.',
  ];

  int _currentTipIndex = 0;

  @override
  void initState() {
    super.initState();

    // Rotate tips every 5 seconds
    Future.delayed(const Duration(seconds: 5), () {
      if (mounted) {
        setState(() {
          _currentTipIndex = (_currentTipIndex + 1) % _tips.length;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
      decoration: BoxDecoration(
        color: isDarkMode
            ? Colors.grey.shade800.withAlpha(76) // 0.3 opacity
            : Colors.grey.shade100.withAlpha(128), // 0.5 opacity
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Icon(
            Icons.lightbulb_outline,
            color: Theme.of(context).colorScheme.primary,
            size: 24,
          ),
          const SizedBox(height: 8),
          AnimatedSwitcher(
            duration: const Duration(milliseconds: 500),
            child: Text(
              _tips[_currentTipIndex],
              key: ValueKey<int>(_currentTipIndex),
              style: TextStyle(
                color: isDarkMode ? Colors.grey.shade300 : Colors.grey.shade700,
                fontSize: 14,
                fontStyle: FontStyle.italic,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }
}
