/// API status enum
enum ApiStatus {
  success,
  error,
  loading,
  noData
}

/// Base API response model
class ApiResponse<T> {
  final bool success;
  final String message;
  final T? data;
  final int? statusCode;
  final ApiStatus status;
  
  ApiResponse({
    required this.success,
    required this.message,
    this.data,
    this.statusCode,
    this.status = ApiStatus.success,
  });
  
  factory ApiResponse.fromJson(Map<String, dynamic> json, T? Function(dynamic)? fromJsonT) {
    return ApiResponse<T>(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      statusCode: json['statusCode'],
      data: json['data'] != null && fromJsonT != null ? fromJsonT(json['data']) : null,
      status: json['success'] == true ? ApiStatus.success : ApiStatus.error,
    );
  }
  
  factory ApiResponse.error(String errorMessage, {int? statusCode}) {
    return ApiResponse(
      success: false,
      message: errorMessage,
      statusCode: statusCode,
      status: ApiStatus.error,
    );
  }
  
  factory ApiResponse.success(T data, {String message = 'Success'}) {
    return ApiResponse(
      success: true,
      message: message,
      data: data,
      status: ApiStatus.success,
    );
  }
  
  factory ApiResponse.loading() {
    return ApiResponse(
      success: false,
      message: 'Loading...',
      status: ApiStatus.loading,
    );
  }
  
  factory ApiResponse.noData({String message = 'No data available'}) {
    return ApiResponse(
      success: true,
      message: message,
      status: ApiStatus.noData,
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'statusCode': statusCode,
      'data': data,
    };
  }
}
