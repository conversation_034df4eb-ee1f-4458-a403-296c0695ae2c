// Connector utility functions

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:ecoplug/models/station/station_details_response.dart'
    as station_details;

class ConnectorUtils {
  // Cache for connector icons to avoid repeated network requests
  static final Map<String, Widget> connectorIconCache = {};

  // Build connector icon from URL with caching and direct API URL usage
  static Widget buildConnectorIconFromUrl(String? iconUrl, String connectorType,
      Color primaryColor, BuildContext context) {
    // Log the connector type and icon URL
    debugPrint(
        'Loading connector icon for type: $connectorType, URL: $iconUrl');

    // If no icon URL provided, show an empty container
    if (iconUrl == null || iconUrl.isEmpty) {
      debugPrint('No icon URL for connector type: $connectorType');
      return Container(
        width: 28,
        height: 28,
        alignment: Alignment.center,
        child: Icon(
          Icons.electric_bolt,
          color: primaryColor,
          size: 20,
        ),
      );
    }

    // Check if we have this icon cached
    final String cacheKey = iconUrl;
    if (connectorIconCache.containsKey(cacheKey)) {
      debugPrint('Using cached icon for $connectorType: $iconUrl');
      return connectorIconCache[cacheKey]!;
    }

    // Create a new icon widget with error handling
    final iconWidget = SvgPicture.network(
      iconUrl, // Use the exact URL from the API
      width: 28,
      height: 28,
      placeholderBuilder: (ctx) {
        // While loading, show a loading indicator
        return SizedBox(
          width: 28,
          height: 28,
          child: Center(
            child: SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(primaryColor),
              ),
            ),
          ),
        );
      },
      // Add error handling
      errorBuilder: (context, error, stackTrace) {
        debugPrint('Error loading connector icon from $iconUrl: $error');

        // Show a simple icon on error
        final errorWidget = Icon(
          Icons.electric_bolt,
          color: primaryColor,
          size: 28,
        );

        // Cache the error widget to avoid repeated network requests
        connectorIconCache[cacheKey] = errorWidget;

        return errorWidget;
      },
    );

    // Cache the icon for future use
    connectorIconCache[cacheKey] = iconWidget;

    return iconWidget;
  }

  // Helper method to build connector power text with price information
  static String buildConnectorPowerText(station_details.Connector connector) {
    // Log the connector data for debugging
    debugPrint('Building connector power text for: ${connector.type}');
    debugPrint('maxElectricPower: ${connector.maxElectricPower}');
    debugPrint('priceLabel: ${connector.priceLabel}');

    // Get price label if available
    final String priceLabel =
        connector.priceLabel != null && connector.priceLabel!.isNotEmpty
            ? '${connector.priceLabel} · '
            : '';

    // CRITICAL: Only use real connector power data from API - no defaults or fallbacks
    String powerText;
    debugPrint(
        'ConnectorUtils: Processing power for connector ${connector.connectorId}');
    debugPrint(
        'ConnectorUtils: maxElectricPower = ${connector.maxElectricPower}');

    if (connector.maxElectricPower != null && connector.maxElectricPower! > 0) {
      // Format the real API value
      debugPrint(
          '🔍 CRITICAL DEBUG: ConnectorUtils raw maxElectricPower: ${connector.maxElectricPower}');
      debugPrint(
          '🔍 CRITICAL DEBUG: ConnectorUtils maxElectricPower type: ${connector.maxElectricPower.runtimeType}');

      final String formatted = connector.maxElectricPower!
          .toStringAsFixed(2)
          .replaceAll(RegExp(r'0*$'), '')
          .replaceAll(RegExp(r'\.$'), '');

      powerText = '$formatted kW';
      debugPrint(
          '✅ CRITICAL DEBUG: ConnectorUtils formatted power: ${connector.maxElectricPower} -> $powerText');
    } else {
      // Show accurate message when API doesn't provide power data
      powerText = 'Power not specified';
      debugPrint(
          '❌ CRITICAL DEBUG: ConnectorUtils: No valid power value from API - showing "Power not specified"');
    }

    return '$priceLabel$powerText';
  }
}
