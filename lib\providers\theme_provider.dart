import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart'; // Import Flutter Riverpod

// Define the NotifierProvider for ThemeMode
final themeNotifierProvider = NotifierProvider<ThemeNotifier, ThemeMode>(ThemeNotifier.new);

// Notifier class to manage the theme mode
class ThemeNotifier extends Notifier<ThemeMode> {

  // Theme mode key for shared preferences
  static const String _themeKey = 'theme_mode';

  @override
  ThemeMode build() {
    // Load saved theme preference when the provider is created
    _loadThemePreference();
    // Default to system theme initially
    return ThemeMode.system;
  }

  // Check if dark mode is active
  bool get isDarkMode {
    if (state == ThemeMode.system) {
      // Get system brightness
      final brightness =
          WidgetsBinding.instance.platformDispatcher.platformBrightness;
      return brightness == Brightness.dark;
    }
    return state == ThemeMode.dark;
  }

  // Load saved theme preference
  Future<void> _loadThemePreference() async {
    final prefs = await SharedPreferences.getInstance();
    final savedTheme = prefs.getString(_themeKey);

    if (savedTheme != null) {
      state = _getThemeModeFromString(savedTheme); // Update state
    }
  }

  // Save theme preference
  Future<void> _saveThemePreference(ThemeMode mode) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_themeKey, mode.toString());
  }

  // Convert string to ThemeMode
  ThemeMode _getThemeModeFromString(String themeString) {
    switch (themeString) {
      case 'ThemeMode.light':
        return ThemeMode.light;
      case 'ThemeMode.dark':
        return ThemeMode.dark;
      case 'ThemeMode.system':
      default:
        return ThemeMode.system;
    }
  }

  // Set theme mode
  Future<void> setThemeMode(ThemeMode mode) async {
    if (state == mode) return;

    state = mode; // Update state
    await _saveThemePreference(mode);
  }

  // Toggle between light and dark mode
  Future<void> toggleTheme() async {
    if (state == ThemeMode.light) {
      await setThemeMode(ThemeMode.dark);
    } else {
      await setThemeMode(ThemeMode.light);
    }
  }
}
