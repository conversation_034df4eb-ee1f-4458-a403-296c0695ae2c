import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:ecoplug/tests/fcm_token_test_service.dart';
import 'package:ecoplug/widgets/firebase_icon_widget.dart';

/// Standalone FCM Token Test Screen
/// Dedicated screen for testing FCM token generation
class FCMTokenTestScreen extends StatefulWidget {
  const FCMTokenTestScreen({super.key});

  @override
  State<FCMTokenTestScreen> createState() => _FCMTokenTestScreenState();
}

class _FCMTokenTestScreenState extends State<FCMTokenTestScreen> {
  final FCMTokenTestService _testService = FCMTokenTestService();
  
  String? _currentToken;
  Map<String, dynamic>? _testResults;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    // Auto-run token generation on screen load
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _generateToken();
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('FCM Token Test'),
        backgroundColor: theme.colorScheme.surface,
        foregroundColor: theme.colorScheme.onSurface,
        actions: [
          IconButton(
            onPressed: _generateToken,
            icon: const Icon(Icons.refresh),
            tooltip: 'Regenerate Token',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Header Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    FirebaseIconWidget(
                      size: 48,
                      color: theme.colorScheme.primary,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'FCM Token Generation Test',
                      style: theme.textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Test Firebase Cloud Messaging token generation',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),

            if (_isLoading) ...[
              const Card(
                child: Padding(
                  padding: EdgeInsets.all(32),
                  child: Column(
                    children: [
                      CircularProgressIndicator(),
                      SizedBox(height: 16),
                      Text('Generating FCM Token...'),
                    ],
                  ),
                ),
              ),
            ] else ...[
              // Token Display
              if (_currentToken != null) ...[
                _buildTokenCard(_currentToken!, theme),
                const SizedBox(height: 20),
              ],

              // Test Results
              if (_testResults != null) ...[
                _buildTestResultsCard(_testResults!, theme),
                const SizedBox(height: 20),
              ],

              // Action Buttons
              _buildActionCard(theme),
              const SizedBox(height: 20),

              // Instructions
              _buildInstructionsCard(theme),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildTokenCard(String token, ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.check_circle,
                  color: Colors.green,
                ),
                const SizedBox(width: 8),
                Text(
                  'FCM Token Generated Successfully! 🎉',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Token Info
            _buildInfoRow('Token Length', '${token.length} characters', theme),
            _buildInfoRow('Token Type', 'Firebase Cloud Messaging Token', theme),
            _buildInfoRow('Generated At', DateTime.now().toString(), theme),
            
            const SizedBox(height: 16),
            
            // Token Preview
            Text(
              'Token Preview:',
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: theme.colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: theme.colorScheme.outline.withValues(alpha: 0.3)),
              ),
              child: SelectableText(
                '${token.substring(0, 100)}...',
                style: theme.textTheme.bodySmall?.copyWith(
                  fontFamily: 'monospace',
                ),
              ),
            ),
            const SizedBox(height: 16),
            
            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _copyTokenToClipboard(token),
                    icon: const Icon(Icons.copy),
                    label: const Text('Copy Full Token'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _showFullTokenDialog(token),
                    icon: const Icon(Icons.visibility),
                    label: const Text('View Full Token'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTestResultsCard(Map<String, dynamic> results, ThemeData theme) {
    final summary = results['test_summary'] as Map<String, dynamic>?;
    if (summary == null) return const SizedBox.shrink();

    final overallStatus = summary['overall_status'] as String? ?? 'unknown';
    final passedTests = summary['passed'] as int? ?? 0;
    final totalTests = summary['total_tests'] as int? ?? 0;
    final successRate = summary['success_rate'] as int? ?? 0;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Comprehensive Test Results',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            _buildInfoRow('Overall Status', overallStatus.toUpperCase(), theme),
            _buildInfoRow('Tests Passed', '$passedTests / $totalTests', theme),
            _buildInfoRow('Success Rate', '$successRate%', theme),
            _buildInfoRow('FCM Ready', summary['fcm_token_ready'] == true ? 'Yes' : 'No', theme),
            
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () => _showDetailedResults(results),
              icon: const Icon(Icons.info),
              label: const Text('View Detailed Results'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionCard(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Test Actions',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton.icon(
                  onPressed: _generateToken,
                  icon: const Icon(Icons.token),
                  label: const Text('Generate Token'),
                ),
                ElevatedButton.icon(
                  onPressed: _runComprehensiveTest,
                  icon: const Icon(Icons.verified),
                  label: const Text('Run Full Test'),
                ),
                OutlinedButton.icon(
                  onPressed: _testTokenRefresh,
                  icon: const Icon(Icons.refresh),
                  label: const Text('Test Refresh'),
                ),
                if (_currentToken != null)
                  OutlinedButton.icon(
                    onPressed: () => _copyTokenToClipboard(_currentToken!),
                    icon: const Icon(Icons.copy),
                    label: const Text('Copy Token'),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInstructionsCard(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'How to Use FCM Token',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildInstructionStep(
              '1. Copy Token',
              'Use the "Copy Full Token" button to copy the FCM token to clipboard',
              Icons.copy,
              theme,
            ),
            _buildInstructionStep(
              '2. Test with Firebase Console',
              'Go to Firebase Console → Cloud Messaging → Send test message',
              Icons.cloud,
              theme,
            ),
            _buildInstructionStep(
              '3. Share with Backend Team',
              'Provide the token to your backend team for server-side testing',
              Icons.share,
              theme,
            ),
            _buildInstructionStep(
              '4. Test Notifications',
              'Verify notifications are received in all app states',
              Icons.notifications,
              theme,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: theme.textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInstructionStep(String title, String description, IconData icon, ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            size: 20,
            color: theme.colorScheme.primary,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  description,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _generateToken() async {
    setState(() => _isLoading = true);
    
    try {
      final result = await _testService.quickTokenTest();
      
      if (result['success'] == true) {
        setState(() => _currentToken = result['token'] as String?);
        _showSnackBar('FCM token generated successfully! 🎉', Colors.green);
      } else {
        _showSnackBar('Failed to generate FCM token: ${result['message']}', Colors.red);
      }
    } catch (e) {
      _showSnackBar('Error generating token: $e', Colors.red);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _runComprehensiveTest() async {
    setState(() => _isLoading = true);
    
    try {
      final results = await _testService.runFCMTokenTests();
      setState(() => _testResults = results);
      
      final summary = results['test_summary'] as Map<String, dynamic>?;
      final overallStatus = summary?['overall_status'] as String? ?? 'unknown';
      
      final Color snackBarColor = overallStatus == 'passed' ? Colors.green : 
                                 overallStatus == 'warning' ? Colors.orange : Colors.red;
      
      _showSnackBar(
        'Comprehensive test completed: ${overallStatus.toUpperCase()}',
        snackBarColor,
      );
      
      // Update current token if available
      final tokenTest = results['token_generation_test'] as Map<String, dynamic>?;
      if (tokenTest?['status'] == 'passed') {
        setState(() => _currentToken = tokenTest!['token'] as String?);
      }
    } catch (e) {
      _showSnackBar('Comprehensive test error: $e', Colors.red);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _testTokenRefresh() async {
    setState(() => _isLoading = true);
    
    try {
      final results = await _testService.runFCMTokenTests();
      final refreshTest = results['token_refresh_test'] as Map<String, dynamic>?;
      
      if (refreshTest?['status'] == 'passed') {
        setState(() => _currentToken = refreshTest!['new_token'] as String?);
        _showSnackBar('Token refresh test successful! 🔄', Colors.green);
      } else {
        _showSnackBar('Token refresh test failed: ${refreshTest?['error']}', Colors.red);
      }
    } catch (e) {
      _showSnackBar('Token refresh error: $e', Colors.red);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _copyTokenToClipboard(String token) {
    Clipboard.setData(ClipboardData(text: token));
    _showSnackBar('FCM token copied to clipboard! 📋', Colors.green);
  }

  void _showFullTokenDialog(String token) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Full FCM Token'),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: SingleChildScrollView(
            child: SelectableText(
              token,
              style: const TextStyle(fontFamily: 'monospace', fontSize: 12),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          TextButton(
            onPressed: () {
              _copyTokenToClipboard(token);
              Navigator.of(context).pop();
            },
            child: const Text('Copy'),
          ),
        ],
      ),
    );
  }

  void _showDetailedResults(Map<String, dynamic> results) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Detailed Test Results'),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: SingleChildScrollView(
            child: Text(
              _formatResultsForDisplay(results),
              style: const TextStyle(fontFamily: 'monospace', fontSize: 12),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  String _formatResultsForDisplay(Map<String, dynamic> results) {
    final buffer = StringBuffer();
    
    void writeMap(Map<String, dynamic> map, int indent) {
      map.forEach((key, value) {
        final prefix = '  ' * indent;
        if (value is Map<String, dynamic>) {
          buffer.writeln('$prefix$key:');
          writeMap(value, indent + 1);
        } else if (value is List) {
          buffer.writeln('$prefix$key: [${value.length} items]');
        } else {
          buffer.writeln('$prefix$key: $value');
        }
      });
    }
    
    writeMap(results, 0);
    return buffer.toString();
  }

  void _showSnackBar(String message, Color backgroundColor) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: backgroundColor,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }
}
