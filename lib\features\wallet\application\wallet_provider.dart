import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../models/wallet/wallet_models.dart';
import '../services/wallet_service.dart';
import '../../../core/services/service_locator.dart';

/// Provider for WalletService
final walletServiceProvider = Provider<WalletService>((ref) {
  final serviceLocator = ServiceLocator();
  return serviceLocator.walletService;
});

/// State class for wallet data 
class WalletState {
  final WalletInfo? walletInfo;
  final bool isLoading;
  final String? errorMessage;
  final DateTime? lastUpdated;

  const WalletState({
    this.walletInfo,
    this.isLoading = false,
    this.errorMessage,
    this.lastUpdated,
  });

  WalletState copyWith({
    WalletInfo? walletInfo,
    bool? isLoading,
    String? errorMessage,
    DateTime? lastUpdated,
  }) {
    return WalletState(
      walletInfo: walletInfo ?? this.walletInfo,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }
}

/// Wallet state notifier
class WalletNotifier extends StateNotifier<WalletState> {
  final WalletService _walletService;

  WalletNotifier(this._walletService) : super(const WalletState()) {
    // Initialize the wallet state by fetching data
    fetchWallet();
  }

  /// Fetch wallet data - always fetches fresh data from the server without caching
  /// This method is called when the wallet screen is shown
  Future<void> fetchWallet() async {
    try {
      // Set loading state
      state = state.copyWith(isLoading: true, errorMessage: null);

      debugPrint('Fetching wallet data from API...');
      final response = await _walletService.getWalletInfo();

      if (response.success && response.data != null) {
        debugPrint(
            'Successfully fetched wallet data with balance: ${response.data!.balance}');

        // Update state with loaded data
        state = state.copyWith(
          walletInfo: response.data,
          isLoading: false,
          errorMessage: null,
          lastUpdated: DateTime.now(),
        );
      } else {
        debugPrint('Failed to fetch wallet data: ${response.message}');
        state = state.copyWith(
          isLoading: false,
          errorMessage: response.message ?? 'Failed to fetch wallet data',
        );
      }
    } catch (e) {
      debugPrint('Error fetching wallet: $e');
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'An error occurred: $e',
      );
    }
  }

  /// Add money to wallet
  Future<bool> addMoney(double amount) async {
    try {
      state = state.copyWith(isLoading: true, errorMessage: null);

      final response = await _walletService.addMoney(amount);

      if (response.success) {
        // Refresh wallet data after successful addition
        await fetchWallet();
        debugPrint('✅ Money added successfully: ₹$amount');
        return true;
      } else {
        state = state.copyWith(
          isLoading: false,
          errorMessage: response.message,
        );
        debugPrint('❌ Failed to add money: ${response.message}');
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'An error occurred: $e',
      );
      debugPrint('❌ Error in addMoney: $e');
      return false;
    }
  }

  /// Clear error message
  void clearError() {
    state = state.copyWith(errorMessage: null);
  }

  /// Refresh wallet data
  Future<void> refresh() async {
    await fetchWallet();
  }
}

/// Provider for wallet state
final walletProvider =
    StateNotifierProvider<WalletNotifier, WalletState>((ref) {
  final walletService = ref.read(walletServiceProvider);
  return WalletNotifier(walletService);
});

/// Provider for wallet balance (convenience provider)
final walletBalanceProvider = Provider<double>((ref) {
  final walletState = ref.watch(walletProvider);
  return walletState.walletInfo?.balance ?? 0.0;
});

/// Provider for wallet loading state (convenience provider)
final walletLoadingProvider = Provider<bool>((ref) {
  final walletState = ref.watch(walletProvider);
  return walletState.isLoading;
});

/// Provider for wallet error message (convenience provider)
final walletErrorProvider = Provider<String?>((ref) {
  final walletState = ref.watch(walletProvider);
  return walletState.errorMessage;
});
 