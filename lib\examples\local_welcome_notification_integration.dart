import 'package:flutter/material.dart';
import 'package:ecoplug/services/auth_notification_service.dart';

/// Local Welcome Notification Integration Example
/// Shows how to integrate welcome notifications with your authentication flow
class LocalWelcomeNotificationIntegration {
  static final AuthNotificationService _authNotificationService = AuthNotificationService();

  /// Initialize the welcome notification system
  /// Call this during app startup (in main.dart or app initialization)
  static Future<void> initialize() async {
    try {
      debugPrint('🔐 Initializing local welcome notification system...');
      await _authNotificationService.initialize();
      debugPrint('✅ Local welcome notification system initialized');
    } catch (e) {
      debugPrint('❌ Error initializing welcome notification system: $e');
    }
  }

  /// Handle successful login - triggers LOCAL welcome notification
  /// Call this immediately after successful authentication
  static Future<void> onLoginSuccess({
    required String userId,
    String? userName,
    String? userEmail,
  }) async {
    try {
      debugPrint('🔐 ===== LOGIN SUCCESS - TRIGGERING LOCAL WELCOME NOTIFICATION =====');
      debugPrint('🔐 User ID: $userId');
      debugPrint('🔐 User Name: $userName');
      debugPrint('🔐 User Email: $userEmail');
      debugPrint('🔐 Notification Type: LOCAL (no server dependency)');

      // Trigger local welcome notification
      await _authNotificationService.onLoginSuccess(
        userId: userId,
        userName: userName,
        userEmail: userEmail,
      );

      debugPrint('✅ Local welcome notification triggered successfully');
    } catch (e) {
      debugPrint('❌ Error triggering welcome notification: $e');
      // Login should still succeed even if notification fails
    }
  }

  /// Handle logout - clears notification state
  static Future<void> onLogout({required String userId}) async {
    try {
      debugPrint('🔐 ===== LOGOUT - CLEARING NOTIFICATION STATE =====');
      await _authNotificationService.onLogout(userId: userId);
      debugPrint('✅ Logout notification handling completed');
    } catch (e) {
      debugPrint('❌ Error handling logout notifications: $e');
    }
  }

  /// Test welcome notification manually
  static Future<void> testWelcomeNotification({
    String? userName,
    bool isFirstLogin = false,
  }) async {
    try {
      debugPrint('🧪 Testing local welcome notification...');
      
      // Generate test user ID
      final testUserId = 'test_user_${DateTime.now().millisecondsSinceEpoch}';
      
      await onLoginSuccess(
        userId: testUserId,
        userName: userName ?? 'Test User',
      );
      
      debugPrint('✅ Test welcome notification completed');
    } catch (e) {
      debugPrint('❌ Error testing welcome notification: $e');
    }
  }
}

/// Example Login Screen with Welcome Notification Integration
class LoginScreenWithWelcomeNotification extends StatefulWidget {
  const LoginScreenWithWelcomeNotification({super.key});

  @override
  State<LoginScreenWithWelcomeNotification> createState() => _LoginScreenWithWelcomeNotificationState();
}

class _LoginScreenWithWelcomeNotificationState extends State<LoginScreenWithWelcomeNotification> {
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    // Initialize welcome notification system
    LocalWelcomeNotificationIntegration.initialize();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Login with Welcome Notifications'),
        backgroundColor: theme.colorScheme.surface,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Header
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    Icon(
                      Icons.login,
                      size: 48,
                      color: theme.colorScheme.primary,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'EcoPlug Login',
                      style: theme.textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Local welcome notifications trigger after successful login',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),

            // Login Form
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    TextField(
                      controller: _emailController,
                      decoration: const InputDecoration(
                        labelText: 'Email',
                        hintText: 'Enter your email',
                        prefixIcon: Icon(Icons.email),
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.emailAddress,
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: _passwordController,
                      decoration: const InputDecoration(
                        labelText: 'Password',
                        hintText: 'Enter your password',
                        prefixIcon: Icon(Icons.lock),
                        border: OutlineInputBorder(),
                      ),
                      obscureText: true,
                    ),
                    const SizedBox(height: 20),
                    ElevatedButton(
                      onPressed: _isLoading ? null : _handleLogin,
                      child: _isLoading
                          ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : const Text('Login'),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),

            // Test Buttons
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Text(
                      'Test Welcome Notifications',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    ElevatedButton.icon(
                      onPressed: () => _testWelcomeNotification(isFirstLogin: true),
                      icon: const Icon(Icons.star),
                      label: const Text('Test First Login'),
                    ),
                    const SizedBox(height: 8),
                    ElevatedButton.icon(
                      onPressed: () => _testWelcomeNotification(isFirstLogin: false),
                      icon: const Icon(Icons.login),
                      label: const Text('Test Return User'),
                    ),
                  ],
                ),
              ),
            ),
            const Spacer(),

            // Info Card
            Card(
              color: theme.colorScheme.primaryContainer,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.info,
                          color: theme.colorScheme.onPrimaryContainer,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Local Notification Features',
                          style: theme.textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onPrimaryContainer,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '• Triggers immediately after login success\n'
                      '• Works in foreground, background, and terminated states\n'
                      '• No server dependency - fully local\n'
                      '• Personalized with user name\n'
                      '• Session-based duplicate prevention',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onPrimaryContainer,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Handle login process with welcome notification
  Future<void> _handleLogin() async {
    if (_emailController.text.isEmpty || _passwordController.text.isEmpty) {
      _showSnackBar('Please enter email and password', Colors.orange);
      return;
    }

    setState(() => _isLoading = true);

    try {
      // Simulate login API call
      await Future.delayed(const Duration(seconds: 2));

      // Simulate successful authentication
      final userId = 'user_${DateTime.now().millisecondsSinceEpoch}';
      final userName = _extractUserNameFromEmail(_emailController.text);

      debugPrint('🔐 ===== AUTHENTICATION SUCCESSFUL =====');
      debugPrint('🔐 User ID: $userId');
      debugPrint('🔐 User Name: $userName');

      // Trigger local welcome notification
      await LocalWelcomeNotificationIntegration.onLoginSuccess(
        userId: userId,
        userName: userName,
        userEmail: _emailController.text,
      );

      _showSnackBar('Login successful! Welcome notification sent 🎉', Colors.green);

      // Clear form
      _emailController.clear();
      _passwordController.clear();

    } catch (e) {
      debugPrint('❌ Login error: $e');
      _showSnackBar('Login failed: $e', Colors.red);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// Test welcome notification
  Future<void> _testWelcomeNotification({bool isFirstLogin = false}) async {
    try {
      await LocalWelcomeNotificationIntegration.testWelcomeNotification(
        userName: 'Test User',
        isFirstLogin: isFirstLogin,
      );

      _showSnackBar(
        'Test welcome notification sent! ${isFirstLogin ? '(First Login)' : '(Return User)'}',
        Colors.blue,
      );
    } catch (e) {
      _showSnackBar('Test failed: $e', Colors.red);
    }
  }

  /// Extract user name from email
  String _extractUserNameFromEmail(String email) {
    final parts = email.split('@');
    if (parts.isNotEmpty) {
      return parts[0].split('.').map((part) => 
          part[0].toUpperCase() + part.substring(1)).join(' ');
    }
    return 'User';
  }

  /// Show snackbar message
  void _showSnackBar(String message, Color backgroundColor) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: backgroundColor,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }
}
