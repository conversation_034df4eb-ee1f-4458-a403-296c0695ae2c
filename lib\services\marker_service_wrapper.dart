import 'package:flutter/foundation.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import '../models/station/station_marker_response.dart';
import 'persistent_marker_service.dart';

/// A unified wrapper for all marker-related services
/// This class redirects all marker-related operations to PersistentMarkerService
/// to eliminate duplicate implementations and ensure consistent behavior
class MarkerServiceWrapper {
  // Singleton pattern
  static final MarkerServiceWrapper _instance =
      MarkerServiceWrapper._internal();
  factory MarkerServiceWrapper() => _instance;
  MarkerServiceWrapper._internal();

  // Use the PersistentMarkerService for all operations
  final PersistentMarkerService _persistentService = PersistentMarkerService();

  /// Initialize all marker services
  Future<void> initialize() async {
    await _persistentService.initialize();
    debugPrint('MarkerServiceWrapper: Initialized all marker services');
  }

  /// Get cached markers or fetch new ones
  Future<List<StationMarkerData>> getMarkers(
      Future<List<StationMarkerData>> Function() fetchFunction) async {
    return _persistentService.getMarkers(fetchFunction);
  }

  /// Get a marker image with retries and caching
  Future<Uint8List?> getMarkerImage(String url,
      {bool forceRefresh = false}) async {
    return _persistentService.getMarkerImage(url, forceRefresh: forceRefresh);
  }

  /// Get BitmapDescriptor for marker
  Future<BitmapDescriptor?> getBitmapDescriptorFromUrl(
    String url, {
    double width = 25.0,
    double height = 37.0,
    bool forceRefresh = false,
  }) async {
    return _persistentService.getBitmapDescriptorFromUrl(
      url,
      width: width,
      height: height,
      forceRefresh: forceRefresh,
    );
  }

  /// Get a BitmapDescriptor for a specific station status
  Future<BitmapDescriptor?> getMarkerDescriptorForStatus(
    String status, {
    bool focused = false,
    double width = 40.0,
    double height = 40.0,
    bool forceRefresh = false,
  }) async {
    return _persistentService.getMarkerDescriptorForStatus(
      status,
      focused: focused,
      width: width,
      height: height,
      forceRefresh: forceRefresh,
    );
  }

  /// Get a fallback BitmapDescriptor, ensuring we never use default Google Maps markers
  Future<BitmapDescriptor> getFallbackDescriptor(String url) async {
    return await _persistentService.getFallbackDescriptor(url);
  }

  /// Preload common marker images to avoid delays
  Future<void> preloadCommonMarkers() async {
    await _persistentService.preloadCommonMarkers();
  }

  /// Clear the cache
  Future<void> clearCache() async {
    await _persistentService.clearCache();
  }

  /// Check if a specific marker URL is available in the cache
  bool isMarkerCached(String url) {
    return _persistentService.isMarkerCached(url);
  }

  /// Get statistics about the cache state
  Map<String, dynamic> getCacheStatistics() {
    return _persistentService.getCacheStatistics();
  }
}

/// Legacy classes for backward compatibility

/// Alias to MarkerServiceWrapper for backward compatibility
class MapMarkerService {
  // Singleton pattern
  static final MapMarkerService _instance = MapMarkerService._internal();
  factory MapMarkerService() => _instance;
  MapMarkerService._internal();

  // Use the wrapper
  final _wrapper = MarkerServiceWrapper();

  /// Get cached markers or fetch new ones
  Future<List<StationMarkerData>> getMarkers(
      Future<List<StationMarkerData>> Function() fetchFunction) async {
    return _wrapper.getMarkers(fetchFunction);
  }

  /// Clear the cache
  Future<void> clearCache() async {
    await _wrapper.clearCache();
  }

  /// Get a marker image
  Future<Uint8List?> getMarkerImage(String url) {
    return _wrapper.getMarkerImage(url);
  }
}

/// Alias to MarkerServiceWrapper for backward compatibility
class MarkerImageProvider {
  // Singleton pattern
  static final MarkerImageProvider _instance = MarkerImageProvider._internal();
  factory MarkerImageProvider() => _instance;
  MarkerImageProvider._internal();

  // Use the wrapper
  final _wrapper = MarkerServiceWrapper();

  /// Get a marker image
  Future<Uint8List?> getMarkerImage(String url) {
    return _wrapper.getMarkerImage(url);
  }

  /// Get BitmapDescriptor for marker
  Future<BitmapDescriptor?> getBitmapDescriptorFromUrl(
    String url, {
    double width = 40.0,
    double height = 40.0,
  }) async {
    return _wrapper.getBitmapDescriptorFromUrl(url,
        width: width, height: height);
  }
}
