# API UID Configuration Guide

## Overview
This document outlines which APIs contain station UIDs and which APIs should be used for station details navigation.

## APIs that CONTAIN Station UIDs ✅

### 1. Nearest Station API
- **Endpoint**: `/user/stations/nearest`
- **Contains UID**: ✅ YES
- **UID Field**: `uid` (UUID string)
- **Usage**: Get stations near user location with UIDs for navigation
- **Navigation**: Can navigate to station details page

### 2. Station Search API
- **Endpoint**: `/user/station/search`
- **Contains UID**: ✅ YES
- **UID Field**: `uid` (UUID string)
- **Usage**: Search stations by name/location with UIDs for navigation
- **Navigation**: Can navigate to station details page

### 3. Station Paginate API
- **Endpoint**: `/user/station/paginate`
- **Contains UID**: ✅ YES
- **UID Field**: `uid` (UUID string)
- **Usage**: Paginated station listing with UIDs for navigation
- **Navigation**: Can navigate to station details page

### 4. Station Details API
- **Endpoint**: `/user/stations/details?uid=[UUID]`
- **Contains UID**: ✅ YES (as parameter)
- **UID Field**: `uid` (UUID string parameter)
- **Usage**: Get detailed station information using UID
- **Navigation**: Target endpoint for station details

## APIs that DO NOT contain Station UIDs ❌

### 1. Marker API
- **Endpoint**: `/user/stations/markers`
- **Contains UID**: ❌ NO
- **Purpose**: Only for map display (coordinates, map pin URLs)
- **Fields**: `station_id` (numeric), `latitude`, `longitude`, `mapPinUrl`, `focusedMapPinUrl`
- **Usage**: Display markers on Google Maps
- **Navigation**: ❌ CANNOT navigate to station details

## Navigation Rules

### ✅ ALLOWED Navigation Patterns
```dart
// From Nearest Station API
final nearestStations = await stationRepository.getNearestStations();
final uid = nearestStations.first.uid;
Navigator.push(context, MaterialPageRoute(
  builder: (context) => StationDetailsPage(stationUid: uid)
));

// From Station Search API
final searchResults = await stationRepository.searchStations(query);
final uid = searchResults.first.uid;
Navigator.push(context, MaterialPageRoute(
  builder: (context) => StationDetailsPage(stationUid: uid)
));

// From Station Paginate API
final paginatedStations = await stationRepository.getStationsPaginated();
final uid = paginatedStations.first.uid;
Navigator.push(context, MaterialPageRoute(
  builder: (context) => StationDetailsPage(stationUid: uid)
));
```

### ❌ FORBIDDEN Navigation Patterns
```dart
// ❌ DO NOT DO THIS - Markers don't contain UIDs
final markers = await stationRepository.getStationMarkers();
final uid = markers.first.uid; // ❌ This field doesn't exist
Navigator.push(context, MaterialPageRoute(
  builder: (context) => StationDetailsPage(stationUid: uid) // ❌ Will fail
));

// ❌ DO NOT DO THIS - No UID enrichment from markers
final enrichedStation = _enrichStationWithUid(markerData); // ❌ Removed method
```

## Implementation Guidelines

### 1. Station List Pages
- Use **Station Paginate API** for main listing
- Use **Station Search API** for search functionality
- Extract UIDs directly from API responses
- Navigate to station details using extracted UIDs

### 2. Dashboard/Map Display
- Use **Marker API** only for map pin display
- Use **Nearest Station API** for station cards with navigation
- Show message to users when they tap markers without UIDs

### 3. Station Details Navigation
- Only accept UIDs from proper APIs (nearest, search, paginate)
- Validate UID exists before navigation
- Show error messages for invalid/missing UIDs

## Code Examples

### Correct UID Extraction
```dart
// ✅ From Nearest Station API
final station = nearestStationResponse.data.first;
final uid = station.uid; // Direct UID access

// ✅ From Station Search API
final station = searchResponse.data.first;
final uid = station.uid; // Direct UID access

// ✅ From Station Paginate API
final station = paginateResponse.data.first;
final uid = station.uid; // Direct UID access
```

### Incorrect UID Extraction (REMOVED)
```dart
// ❌ REMOVED - Don't extract UIDs from markers
// final uid = markerData['uid']; // This field doesn't exist
// final enrichedStation = _enrichStationWithUid(marker); // Method removed
```

## Migration Notes

### Removed Methods
- `_enrichStationWithUid()` - Removed from dashboard and map widgets
- `StationMatchingUtils.enrichStationWithUid()` - Deprecated
- UID extraction from marker models - Removed

### Updated Navigation
- Marker taps now show user-friendly messages
- Direct users to Station List for charging options
- Simplified navigation logic without UID enrichment

## Testing Guidelines

### Test Cases to Verify
1. ✅ Station List navigation works with UIDs from paginate API
2. ✅ Search results navigation works with UIDs from search API
3. ✅ Nearest stations navigation works with UIDs from nearest API
4. ✅ Marker taps show appropriate user messages
5. ✅ No attempts to extract UIDs from marker data
6. ✅ Station details page receives valid UIDs only

### Error Scenarios to Handle
1. Missing UID in API response
2. Invalid UID format
3. Network errors during UID-based navigation
4. User tapping markers (show helpful message)

## Summary

**Key Principle**: Only use UIDs from APIs that actually contain them (nearest, search, paginate). Never attempt to extract or enrich UIDs from marker data, as markers are only for map display purposes.

## UID Problem Resolution Status ✅

### Current Implementation Status
1. **✅ NearestStation Model**: UID is required and non-nullable
2. **✅ PaginatedStation Model**: UID is nullable but validated during conversion
3. **✅ StationMarker Model**: Explicitly does NOT contain UIDs
4. **✅ Station Model**: UID is required and non-nullable
5. **✅ StationConverter**: Validates UIDs and throws exceptions for invalid/missing UIDs
6. **✅ Navigation**: All station detail navigation uses proper UIDs from valid APIs
7. **✅ Error Handling**: Proper validation and error messages for missing/invalid UIDs

### Verification Checklist
- [x] Marker API does not attempt UID extraction
- [x] Nearest Station API provides required UIDs
- [x] Station Search API provides required UIDs
- [x] Station Paginate API provides required UIDs
- [x] Station Details API accepts UID parameter
- [x] All navigation uses validated UIDs
- [x] Error handling for missing/invalid UIDs
- [x] No fallback or default UID values
- [x] Proper UID format validation (UUID)
- [x] Debug logging for UID extraction and validation
