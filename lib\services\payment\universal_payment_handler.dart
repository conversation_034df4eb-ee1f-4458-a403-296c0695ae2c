import 'package:flutter/foundation.dart';
import 'phonepe_service.dart';
import 'payu_service.dart';
import 'cashfree_service.dart';

/// Universal payment response handler that provides consistent error handling
/// and user feedback across all payment gateways (PhonePe, PayU, Cashfree)
class UniversalPaymentHandler {
  static const String _logPrefix = '🌐 UNIVERSAL_PAYMENT:';

  /// Universal payment result that can represent any gateway's response
  static UniversalPaymentResult createUniversalResult({
    required String gateway,
    required String status,
    required String message,
    Map<String, dynamic>? data,
    String? transactionId,
    double? amount,
    String? errorCode,
  }) {
    debugPrint('$_logPrefix Creating universal result for $gateway');
    debugPrint('$_logPrefix Status: $status, Message: $message');
    
    final normalizedStatus = normalizeStatus(status);
    final userFriendlyMessage = _getUserFriendlyMessage(gateway, status, message);
    
    return UniversalPaymentResult(
      gateway: gateway,
      status: normalizedStatus,
      originalStatus: status,
      message: userFriendlyMessage,
      originalMessage: message,
      data: data ?? {},
      transactionId: transactionId,
      amount: amount,
      errorCode: errorCode,
      timestamp: DateTime.now(),
    );
  }

  /// Convert PhonePe result to universal result
  static UniversalPaymentResult fromPhonePeResult(PaymentResult result, {String? transactionId, double? amount}) {
    debugPrint('$_logPrefix Converting PhonePe result: ${result.type}');
    
    String status;
    switch (result.type) {
      case PaymentResultType.success:
        status = 'SUCCESS';
        break;
      case PaymentResultType.interrupted:
        status = 'CANCELLED';
        break;
      case PaymentResultType.failed:
        status = 'FAILED';
        break;
      case PaymentResultType.pending:
        status = 'PENDING';
        break;
      case PaymentResultType.timeout:
        status = 'TIMEOUT';
        break;
      case PaymentResultType.networkError:
        status = 'NETWORK_ERROR';
        break;
      default:
        status = 'UNKNOWN';
    }

    return createUniversalResult(
      gateway: 'PhonePe',
      status: status,
      message: result.message,
      data: result.data,
      transactionId: transactionId,
      amount: amount,
    );
  }

  /// Convert PayU result to universal result
  static UniversalPaymentResult fromPayUResult(PayUPaymentResult result, {String? transactionId, double? amount}) {
    debugPrint('$_logPrefix Converting PayU result: ${result.type}');
    
    String status;
    switch (result.type) {
      case PayUResultType.success:
        status = 'SUCCESS';
        break;
      case PayUResultType.cancelled:
        status = 'CANCELLED';
        break;
      case PayUResultType.failed:
        status = 'FAILED';
        break;
      case PayUResultType.timeout:
        status = 'TIMEOUT';
        break;
      case PayUResultType.networkError:
        status = 'NETWORK_ERROR';
        break;
      default:
        status = 'UNKNOWN';
    }

    return createUniversalResult(
      gateway: 'PayU',
      status: status,
      message: result.message,
      data: result.data,
      transactionId: transactionId,
      amount: amount,
    );
  }

  /// Convert Cashfree result to universal result
  static UniversalPaymentResult fromCashfreeResult(CashfreePaymentResult result, {String? transactionId, double? amount}) {
    debugPrint('$_logPrefix Converting Cashfree result: ${result.type}');
    
    String status;
    switch (result.type) {
      case CashfreePaymentResultType.success:
        status = 'SUCCESS';
        break;
      case CashfreePaymentResultType.cancelled:
        status = 'CANCELLED';
        break;
      case CashfreePaymentResultType.failed:
        status = 'FAILED';
        break;
      case CashfreePaymentResultType.pending:
        status = 'PENDING';
        break;
      case CashfreePaymentResultType.timeout:
        status = 'TIMEOUT';
        break;
      case CashfreePaymentResultType.networkError:
        status = 'NETWORK_ERROR';
        break;
      default:
        status = 'UNKNOWN';
    }

    return createUniversalResult(
      gateway: 'Cashfree',
      status: status,
      message: result.message,
      data: result.data,
      transactionId: transactionId,
      amount: amount,
    );
  }

  /// Normalize status across all gateways
  static String normalizeStatus(String status) {
    final normalizedStatus = status.toUpperCase().trim();
    
    // Handle common variations
    switch (normalizedStatus) {
      case 'SUCCESSFUL':
      case 'COMPLETED':
      case 'PAID':
        return 'SUCCESS';
      case 'CANCELED':
      case 'ABORTED':
        return 'CANCELLED';
      case 'FAILURE':
      case 'ERROR':
        return 'FAILED';
      case 'PROCESSING':
      case 'INITIATED':
        return 'PENDING';
      default:
        return normalizedStatus;
    }
  }

  /// Get user-friendly message based on gateway and status
  static String _getUserFriendlyMessage(String gateway, String status, String originalMessage) {
    final normalizedStatus = normalizeStatus(status);
    
    switch (normalizedStatus) {
      case 'SUCCESS':
        return 'Payment completed successfully via $gateway';
      case 'CANCELLED':
        return 'Payment was cancelled. You can try again.';
      case 'FAILED':
        return _getFailureMessage(gateway, originalMessage);
      case 'PENDING':
        return 'Payment is being processed. Please wait for confirmation.';
      case 'TIMEOUT':
        return 'Payment timed out. Please check your connection and try again.';
      case 'NETWORK_ERROR':
        return 'Network error occurred. Please check your connection and try again.';
      default:
        return originalMessage.isNotEmpty ? originalMessage : 'Payment status unknown. Please contact support.';
    }
  }

  /// Get specific failure message based on gateway
  static String _getFailureMessage(String gateway, String originalMessage) {
    // Check for common error patterns
    final lowerMessage = originalMessage.toLowerCase();
    
    if (lowerMessage.contains('insufficient')) {
      return 'Insufficient balance. Please check your account and try again.';
    }
    
    if (lowerMessage.contains('network') || lowerMessage.contains('connection')) {
      return 'Network error occurred. Please check your connection and try again.';
    }
    
    if (lowerMessage.contains('timeout')) {
      return 'Payment timed out. Please try again.';
    }
    
    if (lowerMessage.contains('invalid') || lowerMessage.contains('incorrect')) {
      return 'Invalid payment details. Please check and try again.';
    }
    
    // Gateway-specific messages
    switch (gateway.toLowerCase()) {
      case 'phonepe':
        return 'PhonePe payment failed. Please try again or use a different payment method.';
      case 'payu':
        return 'PayU payment failed. Please try again or use a different payment method.';
      case 'cashfree':
        return 'Cashfree payment failed. Please try again or use a different payment method.';
      default:
        return originalMessage.isNotEmpty ? originalMessage : 'Payment failed. Please try again.';
    }
  }

  /// Validate universal payment result
  static bool isValidResult(UniversalPaymentResult result) {
    if (result.gateway.isEmpty) {
      debugPrint('$_logPrefix Invalid result: Gateway is empty');
      return false;
    }
    
    if (result.status.isEmpty) {
      debugPrint('$_logPrefix Invalid result: Status is empty');
      return false;
    }
    
    if (result.message.isEmpty) {
      debugPrint('$_logPrefix Invalid result: Message is empty');
      return false;
    }
    
    return true;
  }

  /// Check if result indicates success
  static bool isSuccess(UniversalPaymentResult result) {
    return normalizeStatus(result.status) == 'SUCCESS';
  }

  /// Check if result indicates failure
  static bool isFailure(UniversalPaymentResult result) {
    final status = normalizeStatus(result.status);
    return status == 'FAILED' || status == 'TIMEOUT' || status == 'NETWORK_ERROR';
  }

  /// Check if result indicates cancellation
  static bool isCancelled(UniversalPaymentResult result) {
    return normalizeStatus(result.status) == 'CANCELLED';
  }

  /// Check if result indicates pending status
  static bool isPending(UniversalPaymentResult result) {
    return normalizeStatus(result.status) == 'PENDING';
  }
}

/// Universal payment result class
class UniversalPaymentResult {
  final String gateway;
  final String status;
  final String originalStatus;
  final String message;
  final String originalMessage;
  final Map<String, dynamic> data;
  final String? transactionId;
  final double? amount;
  final String? errorCode;
  final DateTime timestamp;

  const UniversalPaymentResult({
    required this.gateway,
    required this.status,
    required this.originalStatus,
    required this.message,
    required this.originalMessage,
    required this.data,
    this.transactionId,
    this.amount,
    this.errorCode,
    required this.timestamp,
  });

  @override
  String toString() {
    return 'UniversalPaymentResult(gateway: $gateway, status: $status, message: $message, transactionId: $transactionId, amount: $amount)';
  }
}
