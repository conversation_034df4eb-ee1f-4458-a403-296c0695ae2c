import 'package:flutter/material.dart';

class BatteryAnimationWidget extends StatelessWidget {
  final double progress;
  final double currentKwh;
  final double targetKwh;
  final bool isComplete;

  const BatteryAnimationWidget({
    super.key,
    required this.progress,
    required this.currentKwh,
    required this.targetKwh,
    required this.isComplete,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF102A40),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Battery Level',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                '${(progress * 100).toInt()}%',
                style: TextStyle(
                  color: isComplete ? Colors.green : Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          // Battery visualization
          SizedBox(
            height: 80,
            width: double.infinity,
            child: CustomPaint(
              painter: BatteryPainter(
                progress: progress,
                isComplete: isComplete,
              ),
            ),
          ),

          const SizedBox(height: 20),

          // Energy details
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${currentKwh.toStringAsFixed(1)} kWh',
                style: const TextStyle(
                  color: Colors.white70,
                  fontSize: 16,
                ),
              ),
              Text(
                '${targetKwh.toStringAsFixed(1)} kWh',
                style: const TextStyle(
                  color: Colors.white70,
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class BatteryPainter extends CustomPainter {
  final double progress;
  final bool isComplete;

  BatteryPainter({required this.progress, required this.isComplete});

  @override
  void paint(Canvas canvas, Size size) {
    final width = size.width;
    final height = size.height;

    // Draw battery outline
    final batteryWidth = width - 15;
    final batteryHeight = height;
    final batteryRect = RRect.fromRectAndRadius(
      Rect.fromLTWH(0, 0, batteryWidth, batteryHeight),
      const Radius.circular(10),
    );

    final outlinePaint = Paint()
      ..color = Colors.white24
      ..style = PaintingStyle.stroke
      ..strokeWidth = 3;

    canvas.drawRRect(batteryRect, outlinePaint);

    // Draw battery tip
    final tipRect = Rect.fromLTWH(
        batteryWidth, batteryHeight * 0.3, 15, batteryHeight * 0.4);

    canvas.drawRect(tipRect, outlinePaint);

    // Draw fill level
    final fillWidth = (batteryWidth - 6) * progress;
    final fillRect = RRect.fromRectAndRadius(
      Rect.fromLTWH(3, 3, fillWidth, batteryHeight - 6),
      const Radius.circular(7),
    );

    final fillPaint = Paint()
      ..shader = LinearGradient(
        colors: isComplete
            ? [const Color(0xFF00C853), const Color(0xFF69F0AE)]
            : [const Color(0xFF0091EA), const Color(0xFF00B0FF)],
      ).createShader(Rect.fromLTWH(0, 0, fillWidth, batteryHeight));

    canvas.drawRRect(fillRect, fillPaint);

    // Draw lightning bolt when charging
    if (!isComplete) {
      final boltPath = Path()
        ..moveTo(batteryWidth * 0.5, 5)
        ..lineTo(batteryWidth * 0.35, batteryHeight * 0.5)
        ..lineTo(batteryWidth * 0.5, batteryHeight * 0.5)
        ..lineTo(batteryWidth * 0.5, batteryHeight - 5)
        ..lineTo(batteryWidth * 0.65, batteryHeight * 0.5)
        ..lineTo(batteryWidth * 0.5, batteryHeight * 0.5)
        ..close();

      final boltPaint = Paint()
        ..color = Colors.white
        ..style = PaintingStyle.fill;

      canvas.drawPath(boltPath, boltPaint);
    } else {
      // Draw checkmark when complete
      final checkPath = Path()
        ..moveTo(batteryWidth * 0.3, batteryHeight * 0.5)
        ..lineTo(batteryWidth * 0.45, batteryHeight * 0.7)
        ..lineTo(batteryWidth * 0.7, batteryHeight * 0.3);

      final checkPaint = Paint()
        ..color = Colors.white
        ..style = PaintingStyle.stroke
        ..strokeWidth = 4
        ..strokeCap = StrokeCap.round;

      canvas.drawPath(checkPath, checkPaint);
    }
  }

  @override
  bool shouldRepaint(BatteryPainter oldDelegate) {
    return oldDelegate.progress != progress ||
        oldDelegate.isComplete != isComplete;
  }
}
