import 'package:flutter/foundation.dart';
import '../services/active_charging_notification_service.dart';
import '../services/welcome_notification_service.dart';
import '../services/local_notification_manager.dart';
import '../services/fcm_service.dart';

/// Utility class to test both charging and welcome notifications
/// This ensures notifications actually appear in Android notification tray
class NotificationTestUtility {
  static final ActiveChargingNotificationService _chargingService =
      ActiveChargingNotificationService();
  static final WelcomeNotificationService _welcomeService =
      WelcomeNotificationService();

  /// Test both charging and welcome notifications with permission request
  static Future<void> testAllNotifications() async {
    if (!kDebugMode) {
      debugPrint('⚠️ Notification testing only available in debug mode');
      return;
    }

    debugPrint('🧪 ===== TESTING ALL NOTIFICATION SERVICES =====');
    debugPrint('🧪 This will test both charging and welcome notifications');
    debugPrint(
        '🧪 📲 IMPORTANT: Android permission dialog should appear first!');
    debugPrint('🧪 🔔 Please ALLOW notifications when prompted');

    try {
      // Initialize services first (this will trigger permission request)
      debugPrint('🧪 Initializing services and requesting permissions...');
      await initializeServices();

      // Wait a moment for permissions to be processed
      await Future.delayed(const Duration(seconds: 1));

      // Check if permissions were granted
      if (!servicesReady) {
        debugPrint(
            '🧪 ❌ Services not ready - permissions may have been denied');
        debugPrint(
            '🧪 💡 Please enable notifications in Android Settings > Apps > EcoPlug > Notifications');
        return;
      }

      debugPrint('🧪 ✅ Services ready - permissions granted!');
      debugPrint('🧪 📱 Now testing notifications...');

      // Test welcome notification first
      debugPrint('🧪 Testing welcome notification...');
      await _welcomeService.testWelcomeNotification();

      // Wait 2 seconds between tests
      await Future.delayed(const Duration(seconds: 2));

      // Test charging notification
      debugPrint('🧪 Testing charging notification...');
      await _chargingService.testChargingNotification();

      debugPrint('🧪 ✅ All notification tests completed');
      debugPrint('🧪 📱 Check your Android notification tray for:');
      debugPrint('🧪 🎉 "Welcome to EcoPlug! 😊⚡" (Welcome notification)');
      debugPrint('🧪 🔋 "Charging Active • 65%" (Charging notification)');
    } catch (e) {
      debugPrint('🧪 ❌ Error during notification testing: $e');
    }
  }

  /// Test only welcome notification
  static Future<void> testWelcomeNotification() async {
    if (!kDebugMode) {
      debugPrint('⚠️ Notification testing only available in debug mode');
      return;
    }

    debugPrint('🧪 ===== TESTING WELCOME NOTIFICATION =====');
    try {
      await _welcomeService.testWelcomeNotification();
      debugPrint('🧪 ✅ Welcome notification test completed');
    } catch (e) {
      debugPrint('🧪 ❌ Error testing welcome notification: $e');
    }
  }

  /// Test only charging notification
  static Future<void> testChargingNotification() async {
    if (!kDebugMode) {
      debugPrint('⚠️ Notification testing only available in debug mode');
      return;
    }

    debugPrint('🧪 ===== TESTING CHARGING NOTIFICATION =====');
    try {
      // First test with local notification manager directly
      await testChargingSessionLocalNotification();

      // Then test with charging service
      await _chargingService.testChargingNotification();
      debugPrint('🧪 ✅ Charging notification test completed');
    } catch (e) {
      debugPrint('🧪 ❌ Error testing charging notification: $e');
    }
  }

  /// Test charging session notification using local notification manager directly
  static Future<void> testChargingSessionLocalNotification() async {
    try {
      debugPrint('🧪 ===== TESTING CHARGING SESSION LOCAL NOTIFICATION =====');
      debugPrint('🧪 This simulates real charging session data format');

      final localManager = LocalNotificationManager();

      // Simulate realistic charging session data (like what comes from API)
      final batteryPercent = 67; // Realistic battery percentage
      final power = '18.3 kW'; // Realistic power output
      final energy = '12.7 kWh'; // Realistic energy delivered
      final duration = '00:42:15'; // Realistic charging duration
      final cost = '₹127.85'; // Realistic cost
      final co2 = '3.8 kg'; // Realistic CO₂ saved

      final title = '🔋 Charging Active • $batteryPercent%';
      final body = 'Power: $power • Energy: $energy\n'
          'Duration: $duration • Cost: $cost\n'
          'CO₂ Saved: $co2';

      // Show a charging session test notification with realistic data
      final success = await localManager.showNotification(
        id: 1001,
        title: title,
        body: body,
        channelId: 'active_charging_session',
        payload:
            'charging_session_test_${DateTime.now().millisecondsSinceEpoch}',
        ongoing: true,
        autoCancel: false,
        progress: batteryPercent,
        maxProgress: 100,
      );

      if (success) {
        debugPrint('🧪 ✅ Charging session notification sent successfully!');
        debugPrint('🧪 📱 Check Android notification tray for "$title"');
        debugPrint('🧪 📊 Data format matches real charging session screen');
      } else {
        debugPrint('🧪 ❌ Charging session notification failed to send');
      }
    } catch (e) {
      debugPrint('🧪 ❌ Error testing charging session local notification: $e');
    }
  }

  /// Initialize both notification services
  static Future<void> initializeServices() async {
    try {
      debugPrint('🧪 Initializing notification services for testing...');

      await _welcomeService.initialize();
      await _chargingService.initialize();

      debugPrint('🧪 ✅ Notification services initialized for testing');
    } catch (e) {
      debugPrint('🧪 ❌ Error initializing notification services: $e');
    }
  }

  /// Initialize notification permissions only (production ready - no test notifications)
  static Future<void> initializePermissionsOnly() async {
    debugPrint('🔔 ===== INITIALIZING NOTIFICATION PERMISSIONS =====');
    debugPrint('🔔 📲 Requesting notification permissions for production use');

    try {
      // Initialize just the local notification manager to trigger permission request
      final localManager = LocalNotificationManager();
      await localManager.initialize();

      final isReady = localManager.isReady;
      debugPrint('🔔 Permission initialization completed');
      debugPrint('🔔 Notifications ready: $isReady');

      if (isReady) {
        debugPrint('✅ SUCCESS: Notification permissions granted');
        debugPrint('🔔 App is ready to send legitimate notifications');
      } else {
        debugPrint('⚠️ Notification permissions not granted');
        debugPrint(
            '💡 Users can enable notifications in Settings > Apps > EcoPlug > Notifications');
      }
    } catch (e) {
      debugPrint('❌ Error initializing notification permissions: $e');
    }
  }

  /// Test only permission request (shows Android permission dialog) - DEBUG MODE ONLY
  static Future<void> testPermissionRequest() async {
    if (!kDebugMode) {
      debugPrint('⚠️ Permission testing only available in debug mode');
      return;
    }

    debugPrint(
        '🧪 ===== TESTING ANDROID NOTIFICATION PERMISSION REQUEST =====');
    debugPrint('🧪 📲 This will show the native Android permission dialog');
    debugPrint('🧪 🔔 Please ALLOW notifications when prompted');

    try {
      // Initialize just the local notification manager to trigger permission request
      final localManager = LocalNotificationManager();
      await localManager.initialize();

      final isReady = localManager.isReady;
      debugPrint('🧪 Permission request completed');
      debugPrint('🧪 Notifications ready: $isReady');

      if (isReady) {
        debugPrint('🧪 ✅ SUCCESS: Android notification permissions granted!');
        debugPrint(
            '🧪 🔔 Notifications will now appear in Android notification tray');

        // Test a simple notification immediately - ONLY IN DEBUG MODE
        debugPrint('🧪 Testing simple notification...');
        await testSimpleNotification();
      } else {
        debugPrint('🧪 ❌ FAILED: Android notification permissions denied');
        debugPrint(
            '🧪 💡 Enable notifications in Android Settings > Apps > EcoPlug > Notifications');
      }
    } catch (e) {
      debugPrint('🧪 ❌ Error testing permission request: $e');
    }
  }

  /// Test a simple notification to verify basic functionality
  static Future<void> testSimpleNotification() async {
    try {
      debugPrint('🧪 ===== TESTING SIMPLE LOCAL NOTIFICATION =====');

      final localManager = LocalNotificationManager();

      // Show a simple test notification
      final success = await localManager.showNotification(
        id: 9999,
        title: '🧪 Test Notification',
        body:
            'This is a test notification to verify local notifications are working!',
        channelId: 'user_welcome', // Use welcome channel
        payload: 'test_notification_${DateTime.now().millisecondsSinceEpoch}',
      );

      if (success) {
        debugPrint('🧪 ✅ Simple notification sent successfully!');
        debugPrint(
            '🧪 📱 Check Android notification tray for "🧪 Test Notification"');
      } else {
        debugPrint('🧪 ❌ Simple notification failed to send');
      }
    } catch (e) {
      debugPrint('🧪 ❌ Error testing simple notification: $e');
    }
  }

  /// Test FCM service functionality
  static Future<void> testFCMService() async {
    if (!kDebugMode) {
      debugPrint('⚠️ FCM testing only available in debug mode');
      return;
    }

    debugPrint('🧪 ===== TESTING FCM SERVICE =====');
    try {
      final fcmService = FCMService();

      // Get FCM token
      final token = await fcmService.getToken();
      debugPrint('🧪 FCM Token: ${token ?? 'No token available'}');

      if (token != null) {
        debugPrint('🧪 ✅ FCM service is working - token generated');
        debugPrint('🧪 🔥 FCM push notifications should work');
        debugPrint(
            '🧪 📱 Token can be used to send test push notifications from Firebase Console');
      } else {
        debugPrint('🧪 ❌ FCM service issue - no token generated');
      }
    } catch (e) {
      debugPrint('🧪 ❌ Error testing FCM service: $e');
    }
  }

  /// Test both local and FCM notifications
  static Future<void> testAllNotificationSystems() async {
    if (!kDebugMode) {
      debugPrint('⚠️ Notification testing only available in debug mode');
      return;
    }

    debugPrint('🧪 ===== TESTING ALL NOTIFICATION SYSTEMS =====');
    debugPrint('🧪 This will test local notifications AND FCM');

    try {
      // Test local notifications
      debugPrint('🧪 1. Testing local notifications...');
      await testAllNotifications();

      await Future.delayed(const Duration(seconds: 2));

      // Test FCM service
      debugPrint('🧪 2. Testing FCM service...');
      await testFCMService();

      debugPrint('🧪 ✅ All notification system tests completed');
      debugPrint(
          '🧪 📱 Check Android notification tray for local notifications');
      debugPrint('🧪 🔥 Use FCM token for push notification testing');
    } catch (e) {
      debugPrint('🧪 ❌ Error during notification system testing: $e');
    }
  }

  /// Check if both services are ready
  static bool get servicesReady {
    return _welcomeService.isInitialized && _chargingService.isInitialized;
  }
}
