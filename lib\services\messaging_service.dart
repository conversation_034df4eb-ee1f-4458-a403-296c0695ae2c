import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../core/api/api_service.dart';

/// Messaging Service following the Klizer tutorial pattern
/// Handles FCM token management, message receiving, and notification display
class MessagingService {
  final FirebaseMessaging _fcm = FirebaseMessaging.instance;
  final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();
  final ApiService _apiService = ApiService();

  bool _isInitialized = false;
  String? _fcmToken;

  /// Initialize the messaging service
  Future<void> init() async {
    if (_isInitialized) {
      debugPrint('🔥 Messaging Service already initialized');
      return;
    }

    try {
      debugPrint('🔥 ===== INITIALIZING MESSAGING SERVICE =====');

      // Request notification permissions
      await _requestPermissions();

      // Initialize local notifications
      await _initializeLocalNotifications();

      // Get FCM token
      await _getFCMToken();

      // Set up message handlers
      _setupMessageHandlers();

      // Listen for token refresh
      _fcm.onTokenRefresh.listen(_onTokenRefresh);

      _isInitialized = true;
      debugPrint('✅ Messaging Service initialized successfully');
    } catch (e) {
      debugPrint('❌ Error initializing Messaging Service: $e');
      rethrow;
    }
  }

  /// Request notification permissions
  Future<void> _requestPermissions() async {
    try {
      debugPrint('🔔 Requesting notification permissions...');

      final settings = await _fcm.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      debugPrint('🔔 Permission status: ${settings.authorizationStatus}');

      if (settings.authorizationStatus == AuthorizationStatus.authorized) {
        debugPrint('✅ Notification permissions granted');
      } else if (settings.authorizationStatus ==
          AuthorizationStatus.provisional) {
        debugPrint('⚠️ Provisional notification permissions granted');
      } else {
        debugPrint('❌ Notification permissions denied');
      }
    } catch (e) {
      debugPrint('❌ Error requesting permissions: $e');
    }
  }

  /// Initialize local notifications
  Future<void> _initializeLocalNotifications() async {
    try {
      debugPrint('🔔 Initializing local notifications...');

      // Android initialization settings
      const AndroidInitializationSettings initializationSettingsAndroid =
          AndroidInitializationSettings('@drawable/ic_ecoplug_notification');

      // iOS initialization settings
      const DarwinInitializationSettings initializationSettingsIOS =
          DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
      );

      const InitializationSettings initializationSettings =
          InitializationSettings(
        android: initializationSettingsAndroid,
        iOS: initializationSettingsIOS,
      );

      await _localNotifications.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse: _onNotificationTapped,
      );

      // Create notification channel for Android
      await _createNotificationChannel();

      debugPrint('✅ Local notifications initialized');
    } catch (e) {
      debugPrint('❌ Error initializing local notifications: $e');
    }
  }

  /// Create notification channel for Android
  Future<void> _createNotificationChannel() async {
    try {
      const AndroidNotificationChannel channel = AndroidNotificationChannel(
        'ecoplug_fcm_channel', // Channel ID
        'EcoPlug Notifications', // Channel name
        description: 'Notifications for EcoPlug charging sessions and updates',
        importance: Importance.high,
        enableVibration: true,
        enableLights: true,
        ledColor: Color(0xFF00BCD4), // EcoPlug brand color
      );

      await _localNotifications
          .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>()
          ?.createNotificationChannel(channel);

      debugPrint('✅ Notification channel created: ${channel.id}');
    } catch (e) {
      debugPrint('❌ Error creating notification channel: $e');
    }
  }

  /// Get FCM token
  Future<void> _getFCMToken() async {
    try {
      _fcmToken = await _fcm.getToken();
      if (_fcmToken != null) {
        debugPrint('🔔 FCM Token obtained: ${_fcmToken!.substring(0, 20)}...');
        await _storeFCMToken(_fcmToken!);
        await _sendTokenToServer(_fcmToken!);
      } else {
        debugPrint('❌ Failed to get FCM token');
      }
    } catch (e) {
      debugPrint('❌ Error getting FCM token: $e');
    }
  }

  /// Store FCM token locally
  Future<void> _storeFCMToken(String token) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('fcm_token', token);
      debugPrint('💾 FCM token stored locally');
    } catch (e) {
      debugPrint('❌ Error storing FCM token: $e');
    }
  }

  /// Send FCM token to server
  Future<void> _sendTokenToServer(String token) async {
    try {
      debugPrint('📤 Sending FCM token to server...');

      final tokenData = {
        'fcm_token': token,
        'platform': 'android', // or 'ios' based on platform
        'app_version': '1.0.0',
        'device_info': {
          'timestamp': DateTime.now().toIso8601String(),
        }
      };

      final response = await _apiService.post(
        '/user/notifications/update-token',
        data: tokenData,
      );

      if (response != null && response['success'] == true) {
        debugPrint('✅ FCM token sent to server successfully');
      } else {
        debugPrint('❌ Server returned error for FCM token update');
        debugPrint('❌ Response: $response');
      }
    } catch (e) {
      debugPrint('❌ Error sending FCM token to server: $e');
    }
  }

  /// Handle token refresh
  void _onTokenRefresh(String token) {
    debugPrint('🔄 FCM token refreshed: ${token.substring(0, 20)}...');
    _fcmToken = token;
    _storeFCMToken(token);
    _sendTokenToServer(token);
  }

  /// Set up message handlers
  void _setupMessageHandlers() {
    // Handle messages when app is in foreground
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      debugPrint('🔔 ===== FCM MESSAGE RECEIVED (FOREGROUND) =====');
      debugPrint('🔔 From: ${message.from}');
      debugPrint('🔔 Message ID: ${message.messageId}');
      debugPrint('🔔 Data: ${message.data}');

      if (message.notification != null) {
        debugPrint('🔔 Title: ${message.notification!.title}');
        debugPrint('🔔 Body: ${message.notification!.body}');

        // Show local notification when app is in foreground
        _showLocalNotification(message);
      }
    });

    // Handle messages when app is opened from notification tap
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      debugPrint('🔔 ===== FCM MESSAGE OPENED APP =====');
      debugPrint('🔔 From: ${message.from}');
      debugPrint('🔔 Message ID: ${message.messageId}');
      debugPrint('🔔 Data: ${message.data}');

      // Handle navigation based on message data
      _handleMessageNavigation(message);
    });
  }

  /// Show local notification
  Future<void> _showLocalNotification(RemoteMessage message) async {
    try {
      final notification = message.notification;
      if (notification == null) return;

      const AndroidNotificationDetails androidPlatformChannelSpecifics =
          AndroidNotificationDetails(
        'ecoplug_fcm_channel',
        'EcoPlug Notifications',
        channelDescription:
            'Notifications for EcoPlug charging sessions and updates',
        importance: Importance.high,
        priority: Priority.high,
        icon: '@mipmap/ic_launcher',
        largeIcon: DrawableResourceAndroidBitmap('@mipmap/ic_launcher'),
        enableVibration: true,
        enableLights: true,
        ledColor: Color(0xFF00BCD4),
      );

      const DarwinNotificationDetails iOSPlatformChannelSpecifics =
          DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const NotificationDetails platformChannelSpecifics = NotificationDetails(
        android: androidPlatformChannelSpecifics,
        iOS: iOSPlatformChannelSpecifics,
      );

      await _localNotifications.show(
        message.hashCode,
        notification.title ?? 'EcoPlug',
        notification.body ?? 'You have a new notification',
        platformChannelSpecifics,
        payload: message.data.toString(),
      );

      debugPrint('✅ Local notification shown');
    } catch (e) {
      debugPrint('❌ Error showing local notification: $e');
    }
  }

  /// Handle notification tap
  void _onNotificationTapped(NotificationResponse response) {
    debugPrint('🔔 ===== NOTIFICATION TAPPED =====');
    debugPrint('🔔 Payload: ${response.payload}');

    // Handle navigation based on payload
    if (response.payload != null) {
      _handleNotificationPayload(response.payload!);
    }
  }

  /// Handle message navigation
  void _handleMessageNavigation(RemoteMessage message) {
    debugPrint('🔗 Handling message navigation...');

    // Check message data for navigation instructions
    final data = message.data;

    if (data.containsKey('type')) {
      switch (data['type']) {
        case 'charging':
          debugPrint('🔗 Navigating to charging session');
          // Navigate to charging session screen
          break;
        case 'station':
          debugPrint('🔗 Navigating to station details');
          // Navigate to station details
          break;
        case 'wallet':
          debugPrint('🔗 Navigating to wallet');
          // Navigate to wallet screen
          break;
        default:
          debugPrint('🔗 Unknown navigation type: ${data['type']}');
      }
    }
  }

  /// Handle notification payload
  void _handleNotificationPayload(String payload) {
    debugPrint('🔗 Handling notification payload: $payload');

    // Parse payload and handle navigation
    // This can be customized based on your app's navigation requirements
  }

  /// Get current FCM token
  String? get fcmToken => _fcmToken;

  /// Check if service is initialized
  bool get isInitialized => _isInitialized;

  /// Subscribe to topic
  Future<void> subscribeToTopic(String topic) async {
    try {
      await _fcm.subscribeToTopic(topic);
      debugPrint('✅ Subscribed to topic: $topic');
    } catch (e) {
      debugPrint('❌ Error subscribing to topic $topic: $e');
    }
  }

  /// Unsubscribe from topic
  Future<void> unsubscribeFromTopic(String topic) async {
    try {
      await _fcm.unsubscribeFromTopic(topic);
      debugPrint('✅ Unsubscribed from topic: $topic');
    } catch (e) {
      debugPrint('❌ Error unsubscribing from topic $topic: $e');
    }
  }
}
