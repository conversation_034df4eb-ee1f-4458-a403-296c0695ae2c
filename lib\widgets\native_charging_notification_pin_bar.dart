import 'package:flutter/material.dart';
import '../services/pin_bar_notification_integration_service.dart';

/// A replacement widget for ChargingNotificationPinBar that shows native Android notifications
/// instead of in-app widgets. This provides the exact same visual design but in the system
/// notification bar for better user experience and system integration.
class NativeChargingNotificationPinBar extends StatefulWidget {
  final bool isCharging;
  final double chargePercentage;
  final String currentPower;
  final String energyDelivered;
  final String currentPrice;
  final double carbonSavings;
  final double energySourcePercentage;
  final String chargingTimer;
  final VoidCallback? onClose;
  final bool isVisible;
  final VoidCallback? onTap;
  final String? chargingMode; // 'direct' or 'normal'

  const NativeChargingNotificationPinBar({
    super.key,
    required this.isCharging,
    required this.chargePercentage,
    required this.currentPower,
    required this.energyDelivered,
    required this.currentPrice,
    required this.carbonSavings,
    required this.energySourcePercentage,
    required this.chargingTimer,
    this.onClose,
    this.isVisible = true,
    this.onTap,
    this.chargingMode,
  });

  @override
  State<NativeChargingNotificationPinBar> createState() =>
      _NativeChargingNotificationPinBarState();
}

class _NativeChargingNotificationPinBarState extends State<NativeChargingNotificationPinBar> {
  final PinBarNotificationIntegrationService _notificationService =
      PinBarNotificationIntegrationService();

  bool _isInitialized = false;
  bool _wasVisible = false;

  @override
  void initState() {
    super.initState();
    _initializeService();
  }

  @override
  void didUpdateWidget(NativeChargingNotificationPinBar oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Handle visibility changes
    if (widget.isVisible != oldWidget.isVisible) {
      if (widget.isVisible) {
        _showNotification();
      } else {
        _hideNotification();
      }
    }

    // Handle data updates when visible
    else if (widget.isVisible && _hasDataChanged(oldWidget)) {
      _updateNotification();
    }
  }

  @override
  void dispose() {
    _hideNotification();
    super.dispose();
  }

  Future<void> _initializeService() async {
    try {
      debugPrint('🔔 Initializing Native Pin Bar Notification Service...');
      await _notificationService.initialize();

      setState(() {
        _isInitialized = true;
      });

      // Show notification if widget should be visible
      if (widget.isVisible) {
        await _showNotification();
      }

      debugPrint('✅ Native Pin Bar Notification Service initialized');
    } catch (e) {
      debugPrint('❌ Error initializing native pin bar notification service: $e');
    }
  }

  Future<void> _showNotification() async {
    if (!_isInitialized) return;

    try {
      debugPrint('🔔 Showing native pin bar notification...');

      await _notificationService.showPinBarNotification(
        isCharging: widget.isCharging,
        chargePercentage: widget.chargePercentage,
        currentPower: widget.currentPower,
        energyDelivered: widget.energyDelivered,
        currentPrice: widget.currentPrice,
        carbonSavings: widget.carbonSavings,
        energySourcePercentage: widget.energySourcePercentage,
        chargingTimer: widget.chargingTimer,
        chargingMode: widget.chargingMode,
        onClose: widget.onClose,
        onTap: widget.onTap,
      );

      _wasVisible = true;
      debugPrint('✅ Native pin bar notification shown');
    } catch (e) {
      debugPrint('❌ Error showing native pin bar notification: $e');
    }
  }

  Future<void> _updateNotification() async {
    if (!_isInitialized || !_wasVisible) return;

    try {
      debugPrint('🔔 Updating native pin bar notification...');

      await _notificationService.updatePinBarNotification(
        isCharging: widget.isCharging,
        chargePercentage: widget.chargePercentage,
        currentPower: widget.currentPower,
        energyDelivered: widget.energyDelivered,
        currentPrice: widget.currentPrice,
        carbonSavings: widget.carbonSavings,
        energySourcePercentage: widget.energySourcePercentage,
        chargingTimer: widget.chargingTimer,
        chargingMode: widget.chargingMode,
      );

      debugPrint('✅ Native pin bar notification updated');
    } catch (e) {
      debugPrint('❌ Error updating native pin bar notification: $e');
    }
  }

  Future<void> _hideNotification() async {
    if (!_isInitialized || !_wasVisible) return;

    try {
      debugPrint('🔔 Hiding native pin bar notification...');

      await _notificationService.hidePinBarNotification();
      _wasVisible = false;

      debugPrint('✅ Native pin bar notification hidden');
    } catch (e) {
      debugPrint('❌ Error hiding native pin bar notification: $e');
    }
  }

  bool _hasDataChanged(NativeChargingNotificationPinBar oldWidget) {
    return widget.isCharging != oldWidget.isCharging ||
           widget.chargePercentage != oldWidget.chargePercentage ||
           widget.currentPower != oldWidget.currentPower ||
           widget.energyDelivered != oldWidget.energyDelivered ||
           widget.currentPrice != oldWidget.currentPrice ||
           widget.carbonSavings != oldWidget.carbonSavings ||
           widget.energySourcePercentage != oldWidget.energySourcePercentage ||
           widget.chargingTimer != oldWidget.chargingTimer ||
           widget.chargingMode != oldWidget.chargingMode;
  }

  @override
  Widget build(BuildContext context) {
    // This widget renders nothing in the UI since the notification is shown natively
    // We return an empty container to maintain the widget tree structure
    return const SizedBox.shrink();
  }
}

/// Extension methods to make migration from ChargingNotificationPinBar easier
extension NativeChargingNotificationPinBarExtensions on NativeChargingNotificationPinBar {
  /// Create a NativeChargingNotificationPinBar from ChargingNotificationPinBar parameters
  static NativeChargingNotificationPinBar fromPinBarWidget({
    required bool isCharging,
    required double chargePercentage,
    required String currentPower,
    required String energyDelivered,
    required String currentPrice,
    required double carbonSavings,
    required double energySourcePercentage,
    required String chargingTimer,
    VoidCallback? onClose,
    bool isVisible = true,
    VoidCallback? onTap,
    String? chargingMode,
  }) {
    return NativeChargingNotificationPinBar(
      isCharging: isCharging,
      chargePercentage: chargePercentage,
      currentPower: currentPower,
      energyDelivered: energyDelivered,
      currentPrice: currentPrice,
      carbonSavings: carbonSavings,
      energySourcePercentage: energySourcePercentage,
      chargingTimer: chargingTimer,
      onClose: onClose,
      isVisible: isVisible,
      onTap: onTap,
      chargingMode: chargingMode,
    );
  }
}

/// Utility class for managing the transition from in-app widgets to native notifications
class PinBarNotificationMigrationHelper {
  static bool _useNativeNotifications = true;

  /// Set whether to use native notifications or fall back to in-app widgets
  static void setUseNativeNotifications(bool useNative) {
    _useNativeNotifications = useNative;
    debugPrint('🔔 Pin bar notification mode: ${useNative ? "Native" : "In-app widget"}');
  }

  /// Get current notification mode
  static bool get useNativeNotifications => _useNativeNotifications;

  /// Check if native notifications are supported on this platform
  static Future<bool> isNativeNotificationSupported() async {
    final service = PinBarNotificationIntegrationService();
    return await service.isSupported();
  }

  /// Initialize native notification support and set mode accordingly
  static Future<void> initializeAndSetMode() async {
    try {
      final isSupported = await isNativeNotificationSupported();
      setUseNativeNotifications(isSupported);

      if (isSupported) {
        final service = PinBarNotificationIntegrationService();
        await service.initialize();
        debugPrint('✅ Native pin bar notifications initialized and enabled');
      } else {
        debugPrint('⚠️ Native pin bar notifications not supported, using in-app widgets');
      }
    } catch (e) {
      debugPrint('❌ Error initializing native notifications, falling back to in-app widgets: $e');
      setUseNativeNotifications(false);
    }
  }
}
