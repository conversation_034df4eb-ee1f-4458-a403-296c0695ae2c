package com.eeil.ecoplug

import android.app.*
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.IBinder
import android.util.Log
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.embedding.engine.dart.DartExecutor
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import kotlinx.coroutines.*
import java.util.concurrent.atomic.AtomicBoolean

/**
 * Android Foreground Service for Persistent Charging Session Management
 *
 * This service ensures that charging session notifications and data polling
 * continue to work even when the app is in background or terminated state.
 *
 * Key Features:
 * - Persistent foreground notification
 * - Background API data polling
 * - Real-time notification updates
 * - Deep linking support for notification taps
 * - Automatic service lifecycle management
 */
class ChargingBackgroundService : Service() {

    companion object {
        private const val TAG = "ChargingBgService"
        private const val SERVICE_ID = 1001
        private const val CHANNEL_ID = "charging_background_service"
        private const val CHANNEL_NAME = "Charging Background Service"
        private const val NOTIFICATION_CHANNEL = "charging_session_persistent"

        // Service state tracking
        private val isServiceRunning = AtomicBoolean(false)

        // Intent actions
        const val ACTION_START_CHARGING = "com.eeil.ecoplug.START_CHARGING"
        const val ACTION_STOP_CHARGING = "com.eeil.ecoplug.STOP_CHARGING"
        const val ACTION_UPDATE_CHARGING = "com.eeil.ecoplug.UPDATE_CHARGING"

        // Intent extras
        const val EXTRA_STATION_UID = "station_uid"
        const val EXTRA_CONNECTOR_ID = "connector_id"
        const val EXTRA_TRANSACTION_ID = "transaction_id"
        const val EXTRA_AUTH_REFERENCE = "auth_reference"
        const val EXTRA_CHARGE_PERCENTAGE = "charge_percentage"
        const val EXTRA_CURRENT_POWER = "current_power"
        const val EXTRA_ENERGY_DELIVERED = "energy_delivered"
        const val EXTRA_COST = "cost"
        const val EXTRA_CO2_SAVED = "co2_saved"
        const val EXTRA_CHARGING_TIMER = "charging_timer"

        /**
         * Start the charging background service
         */
        fun startChargingService(
            context: Context,
            stationUid: String,
            connectorId: String,
            transactionId: String,
            authReference: String
        ) {
            val intent = Intent(context, ChargingBackgroundService::class.java).apply {
                action = ACTION_START_CHARGING
                putExtra(EXTRA_STATION_UID, stationUid)
                putExtra(EXTRA_CONNECTOR_ID, connectorId)
                putExtra(EXTRA_TRANSACTION_ID, transactionId)
                putExtra(EXTRA_AUTH_REFERENCE, authReference)
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }

        /**
         * Stop the charging background service
         */
        fun stopChargingService(context: Context) {
            val intent = Intent(context, ChargingBackgroundService::class.java).apply {
                action = ACTION_STOP_CHARGING
            }
            context.stopService(intent)
        }

        /**
         * Update charging data in the service
         */
        fun updateChargingData(
            context: Context,
            chargePercentage: Double,
            currentPower: String,
            energyDelivered: String,
            cost: String,
            co2Saved: String,
            chargingTimer: String
        ) {
            val intent = Intent(context, ChargingBackgroundService::class.java).apply {
                action = ACTION_UPDATE_CHARGING
                putExtra(EXTRA_CHARGE_PERCENTAGE, chargePercentage)
                putExtra(EXTRA_CURRENT_POWER, currentPower)
                putExtra(EXTRA_ENERGY_DELIVERED, energyDelivered)
                putExtra(EXTRA_COST, cost)
                putExtra(EXTRA_CO2_SAVED, co2Saved)
                putExtra(EXTRA_CHARGING_TIMER, chargingTimer)
            }

            if (isServiceRunning.get()) {
                context.startService(intent)
            }
        }

        /**
         * Check if the service is currently running
         */
        fun isRunning(): Boolean = isServiceRunning.get()
    }

    // Service components
    private var serviceScope: CoroutineScope? = null
    private var pollingJob: Job? = null
    private var flutterEngine: FlutterEngine? = null
    private var methodChannel: MethodChannel? = null

    // Charging session data
    private var stationUid: String? = null
    private var connectorId: String? = null
    private var transactionId: String? = null
    private var authReference: String? = null
    private var chargePercentage: Double = 0.0
    private var currentPower: String = "0.0 kW"
    private var energyDelivered: String = "0.00 kWh"
    private var cost: String = "₹0.00"
    private var co2Saved: String = "0.0 kg"
    private var chargingTimer: String = "00:00:00"

    // Previous values for change detection (prevent notification spam)
    private var lastNotificationChargePercentage: Double = -1.0
    private var lastNotificationPower: String = ""
    private var lastNotificationEnergy: String = ""
    private var lastNotificationCost: String = ""
    private var lastNotificationTime: Long = 0

    // Notification components
    private lateinit var notificationManager: NotificationManagerCompat
    private var customNotificationHandler: CustomChargingNotificationHandler? = null

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "🔋 Charging Background Service Created")

        // Initialize notification manager
        notificationManager = NotificationManagerCompat.from(this)
        customNotificationHandler = CustomChargingNotificationHandler(this)

        // Create notification channels
        createNotificationChannels()

        // Initialize coroutine scope
        serviceScope = CoroutineScope(Dispatchers.Main + SupervisorJob())

        Log.d(TAG, "✅ Service initialization complete")
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "🔋 Service onStartCommand: ${intent?.action}")

        when (intent?.action) {
            ACTION_START_CHARGING -> {
                handleStartCharging(intent)
            }
            ACTION_STOP_CHARGING -> {
                handleStopCharging()
            }
            ACTION_UPDATE_CHARGING -> {
                handleUpdateCharging(intent)
            }
        }

        // Return START_STICKY to restart service if killed by system
        return START_STICKY
    }

    override fun onBind(intent: Intent?): IBinder? = null

    override fun onDestroy() {
        Log.d(TAG, "🔋 Service onDestroy called")

        // Clean up resources
        stopPolling()
        serviceScope?.cancel()
        flutterEngine?.destroy()

        isServiceRunning.set(false)

        Log.d(TAG, "✅ Service destroyed and cleaned up")
        super.onDestroy()
    }

    /**
     * Handle start charging action
     */
    private fun handleStartCharging(intent: Intent) {
        Log.d(TAG, "🔋 ===== STARTING CHARGING BACKGROUND SERVICE =====")

        // Extract charging session data
        stationUid = intent.getStringExtra(EXTRA_STATION_UID)
        connectorId = intent.getStringExtra(EXTRA_CONNECTOR_ID)
        transactionId = intent.getStringExtra(EXTRA_TRANSACTION_ID)
        authReference = intent.getStringExtra(EXTRA_AUTH_REFERENCE)

        Log.d(TAG, "🔋 Station: $stationUid")
        Log.d(TAG, "🔋 Connector: $connectorId")
        Log.d(TAG, "🔋 Transaction ID: $transactionId")
        Log.d(TAG, "🔋 Auth Reference: $authReference")

        // Start foreground service with notification
        startForegroundWithNotification()

        // Start background data polling
        startPolling()

        // Mark service as running
        isServiceRunning.set(true)

        Log.d(TAG, "✅ Charging background service started successfully")
    }

    /**
     * Handle stop charging action
     */
    private fun handleStopCharging() {
        Log.d(TAG, "🔋 ===== STOPPING CHARGING BACKGROUND SERVICE =====")

        // Stop polling
        stopPolling()

        // Stop foreground service
        stopForeground(true)
        stopSelf()

        Log.d(TAG, "✅ Charging background service stopped")
    }

    /**
     * Handle update charging data action
     */
    private fun handleUpdateCharging(intent: Intent) {
        Log.d(TAG, "🔋 Updating charging data in background service")

        // Update charging data
        chargePercentage = intent.getDoubleExtra(EXTRA_CHARGE_PERCENTAGE, chargePercentage)
        currentPower = intent.getStringExtra(EXTRA_CURRENT_POWER) ?: currentPower
        energyDelivered = intent.getStringExtra(EXTRA_ENERGY_DELIVERED) ?: energyDelivered
        cost = intent.getStringExtra(EXTRA_COST) ?: cost
        co2Saved = intent.getStringExtra(EXTRA_CO2_SAVED) ?: co2Saved
        chargingTimer = intent.getStringExtra(EXTRA_CHARGING_TIMER) ?: chargingTimer

        // Update notification with new data
        updateNotification()

        Log.d(TAG, "✅ Charging data updated: ${(chargePercentage * 100).toInt()}%")
    }

    /**
     * Start foreground service with persistent notification
     */
    private fun startForegroundWithNotification() {
        Log.d(TAG, "🔋 Starting foreground service with notification")

        val notification = createServiceNotification()
        startForeground(SERVICE_ID, notification)

        // Also show the charging notification
        showChargingNotification()

        Log.d(TAG, "✅ Foreground service started")
    }

    /**
     * Create service notification for foreground service
     */
    private fun createServiceNotification(): Notification {
        val intent = Intent(this, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            putExtra("open_charging_session", true)
        }

        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("EcoPlug Charging Service")
            .setContentText("Managing your charging session in background")
            .setSmallIcon(R.drawable.ic_launcher) // Use official app launcher icon for better recognition
            .setLargeIcon(android.graphics.BitmapFactory.decodeResource(resources, R.drawable.ic_launcher))
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setCategory(NotificationCompat.CATEGORY_SERVICE)
            .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
            .build()
    }

    /**
     * Show/update charging notification (only when data changes significantly)
     */
    private fun showChargingNotification() {
        customNotificationHandler?.let { handler ->
            try {
                // Check if notification should be updated (prevent spam)
                if (!shouldUpdateNotification()) {
                    Log.d(TAG, "🔋 Skipping notification update - no significant changes")
                    return
                }

                // Create method call data for notification
                val arguments = mapOf(
                    "isCharging" to true,
                    "chargePercentage" to chargePercentage,
                    "currentPower" to currentPower,
                    "energyDelivered" to energyDelivered,
                    "currentPrice" to cost,
                    "co2Saved" to co2Saved,
                    "chargingTimer" to chargingTimer,
                    "percentageText" to "${(chargePercentage * 100).toInt()}%",
                    "powerValue" to currentPower,
                    "energyValue" to energyDelivered,
                    "co2Value" to co2Saved,
                    "renewableValue" to "85",
                    "statusText" to "CHARGING",
                    "chargingMode" to "normal"
                )

                // Create a mock method call for the notification handler
                val mockCall = MethodCall("showCustomNotification", arguments)
                val mockResult = object : MethodChannel.Result {
                    override fun success(result: Any?) {
                        Log.d(TAG, "✅ Charging notification updated successfully")
                    }
                    override fun error(errorCode: String, errorMessage: String?, errorDetails: Any?) {
                        Log.e(TAG, "❌ Error updating charging notification: $errorMessage")
                    }
                    override fun notImplemented() {
                        Log.w(TAG, "⚠️ Charging notification update not implemented")
                    }
                }

                handler.onMethodCall(mockCall, mockResult)

                // Update last notification tracking
                lastNotificationChargePercentage = chargePercentage
                lastNotificationPower = currentPower
                lastNotificationEnergy = energyDelivered
                lastNotificationCost = cost
                lastNotificationTime = System.currentTimeMillis()

            } catch (e: Exception) {
                Log.e(TAG, "❌ Error showing charging notification: ${e.message}")
            }
        }
    }

    /**
     * Update notification with current charging data
     */
    private fun updateNotification() {
        showChargingNotification()
    }

    /**
     * Check if notification should be updated to prevent spam
     * Only update if there are significant changes or enough time has passed
     */
    private fun shouldUpdateNotification(): Boolean {
        val currentTime = System.currentTimeMillis()
        val timeSinceLastNotification = currentTime - lastNotificationTime

        // Always update if this is the first notification
        if (lastNotificationTime == 0L) {
            return true
        }

        // Don't update more frequently than every 30 seconds
        if (timeSinceLastNotification < 30000) {
            return false
        }

        // Check for significant changes
        val chargePercentageChanged = Math.abs(chargePercentage - lastNotificationChargePercentage) >= 0.01 // 1% change
        val powerChanged = currentPower != lastNotificationPower
        val energyChanged = energyDelivered != lastNotificationEnergy
        val costChanged = cost != lastNotificationCost

        // Update if there are significant changes or it's been more than 2 minutes
        return chargePercentageChanged || powerChanged || energyChanged || costChanged || timeSinceLastNotification > 120000
    }

    /**
     * Start background data polling
     */
    private fun startPolling() {
        if (authReference == null || transactionId == null) {
            Log.e(TAG, "❌ Cannot start polling: missing auth reference or transaction ID")
            return
        }

        Log.d(TAG, "🔋 Starting background data polling")

        pollingJob = serviceScope?.launch {
            while (isActive) {
                try {
                    // Poll charging data from API
                    pollChargingData()

                    // Wait 15 seconds before next poll (matching frontend logic)
                    delay(15000)

                } catch (e: Exception) {
                    Log.e(TAG, "❌ Error in polling loop: ${e.message}")

                    // Wait before retrying on error
                    delay(30000)
                }
            }
        }

        Log.d(TAG, "✅ Background polling started")
    }

    /**
     * Stop background data polling
     */
    private fun stopPolling() {
        Log.d(TAG, "🔋 Stopping background data polling")

        pollingJob?.cancel()
        pollingJob = null

        Log.d(TAG, "✅ Background polling stopped")
    }

    /**
     * Poll charging data from API
     */
    private suspend fun pollChargingData() {
        try {
            Log.d(TAG, "📊 Polling charging data...")

            // Initialize Flutter engine if needed for API calls
            initializeFlutterEngineIfNeeded()

            // Call Flutter method to get charging data
            methodChannel?.invokeMethod(
                "pollChargingData",
                mapOf(
                    "authReference" to authReference,
                    "transactionId" to transactionId
                ),
                object : MethodChannel.Result {
                    override fun success(result: Any?) {
                        when (result) {
                            is Map<*, *> -> {
                                handlePollingResult(result as Map<String, Any>)
                            }
                            else -> {
                                Log.w(TAG, "⚠️ Unexpected polling result: $result")
                            }
                        }
                    }
                    override fun error(errorCode: String, errorMessage: String?, errorDetails: Any?) {
                        Log.e(TAG, "❌ Error polling charging data: $errorMessage")
                    }
                    override fun notImplemented() {
                        Log.w(TAG, "⚠️ Polling method not implemented")
                    }
                }
            )

        } catch (e: Exception) {
            Log.e(TAG, "❌ Error polling charging data: ${e.message}")
        }
    }

    /**
     * Handle polling result from Flutter
     */
    private fun handlePollingResult(result: Map<String, Any>) {
        try {
            Log.d(TAG, "📊 Received polling result: $result")

            // Extract charging data from result
            val newChargePercentage = (result["chargePercentage"] as? Number)?.toDouble() ?: chargePercentage
            val newCurrentPower = result["currentPower"] as? String ?: currentPower
            val newEnergyDelivered = result["energyDelivered"] as? String ?: energyDelivered
            val newCost = result["cost"] as? String ?: cost
            val newCo2Saved = result["co2Saved"] as? String ?: co2Saved
            val newChargingTimer = result["chargingTimer"] as? String ?: chargingTimer

            // Update local data
            chargePercentage = newChargePercentage
            currentPower = newCurrentPower
            energyDelivered = newEnergyDelivered
            cost = newCost
            co2Saved = newCo2Saved
            chargingTimer = newChargingTimer

            // Update notification
            updateNotification()

            // Check if charging is complete
            val isComplete = result["isComplete"] as? Boolean ?: false
            if (isComplete) {
                Log.d(TAG, "🔋 Charging session completed")
                handleChargingComplete()
            }

        } catch (e: Exception) {
            Log.e(TAG, "❌ Error handling polling result: ${e.message}")
        }
    }

    /**
     * Handle charging completion
     */
    private fun handleChargingComplete() {
        Log.d(TAG, "🔋 ===== CHARGING SESSION COMPLETED =====")

        // Update notification to show completion
        chargePercentage = 1.0 // 100%
        updateNotification()

        // Stop the service after a delay to allow user to see completion
        serviceScope?.launch {
            delay(30000) // Wait 30 seconds
            handleStopCharging()
        }
    }

    /**
     * Initialize Flutter engine for API communication
     */
    private fun initializeFlutterEngineIfNeeded() {
        if (flutterEngine == null) {
            Log.d(TAG, "🔋 Initializing Flutter engine for background service")

            try {
                flutterEngine = FlutterEngine(this)
                flutterEngine?.dartExecutor?.executeDartEntrypoint(
                    DartExecutor.DartEntrypoint.createDefault()
                )

                // Set up method channel for communication
                methodChannel = MethodChannel(
                    flutterEngine!!.dartExecutor.binaryMessenger,
                    "charging_background_service"
                )

                Log.d(TAG, "✅ Flutter engine initialized for background service")

            } catch (e: Exception) {
                Log.e(TAG, "❌ Error initializing Flutter engine: ${e.message}")
            }
        }
    }

    /**
     * Create notification channels using shared NotificationHelper
     */
    private fun createNotificationChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            Log.d(TAG, "🔔 Creating notification channels using shared utility")

            // Use shared NotificationHelper for consistent channel creation
            val notificationHelper = NotificationHelper(this)

            // Create service notification channel (low importance for background service)
            val serviceChannel = NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Background service for charging session management"
                setShowBadge(false)
                enableLights(false)
                enableVibration(false)
                setSound(null, null)
            }

            // Create charging notification channel using shared utility
            notificationHelper.createNotificationChannel(
                NOTIFICATION_CHANNEL,
                "Persistent Charging Sessions",
                "Persistent notifications for active charging sessions",
                isChargingChannel = true
            )

            // Create the service channel manually (since it has different requirements)
            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(serviceChannel)

            Log.d(TAG, "✅ Notification channels created using shared utility")
        }
    }
}
