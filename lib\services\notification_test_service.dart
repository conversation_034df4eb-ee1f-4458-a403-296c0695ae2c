import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'dart:io';

/// Test service to verify notification functionality
/// This service provides simple methods to test if notifications are working correctly
class NotificationTestService {
  static final NotificationTestService _instance =
      NotificationTestService._internal();
  factory NotificationTestService() => _instance;
  NotificationTestService._internal();

  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  bool _isInitialized = false;
  static const int _testNotificationId = 9999;

  /// Initialize the test notification service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint('🧪 ===== INITIALIZING NOTIFICATION TEST SERVICE =====');
      debugPrint('🧪 Platform: ${Platform.isAndroid ? "Android" : "Other"}');

      // Create notification channel for Android 8.0+ (API level 26+)
      if (Platform.isAndroid) {
        await _createTestNotificationChannel();
      }

      // Android initialization settings - BRANDING: EcoPlug launcher icon
      const AndroidInitializationSettings initializationSettingsAndroid =
          AndroidInitializationSettings('@mipmap/ic_launcher');

      // iOS initialization settings (optional, for future use)
      const DarwinInitializationSettings initializationSettingsIOS =
          DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
      );

      const InitializationSettings initializationSettings =
          InitializationSettings(
        android: initializationSettingsAndroid,
        iOS: initializationSettingsIOS,
      );

      debugPrint('🧪 Initializing flutter_local_notifications plugin...');
      await _flutterLocalNotificationsPlugin.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse: _onNotificationTapped,
      );

      // Request notification permissions for Android 13+
      if (Platform.isAndroid) {
        debugPrint('🧪 Requesting notification permissions...');
        final permissionGranted = await _flutterLocalNotificationsPlugin
            .resolvePlatformSpecificImplementation<
                AndroidFlutterLocalNotificationsPlugin>()
            ?.requestNotificationsPermission();
        debugPrint('🧪 Permission granted: $permissionGranted');

        // Check if notifications are enabled
        final areEnabled = await areNotificationsEnabled();
        debugPrint('🧪 Notifications enabled: $areEnabled');
      }

      _isInitialized = true;
      debugPrint('✅ Notification Test Service initialized successfully');
    } catch (e) {
      debugPrint('❌ Error initializing Notification Test Service: $e');
      debugPrint('❌ Stack trace: ${StackTrace.current}');
      rethrow;
    }
  }

  /// Create test notification channel for Android 8.0+ (API level 26+)
  Future<void> _createTestNotificationChannel() async {
    if (!Platform.isAndroid) return;

    try {
      debugPrint('🧪 Creating test notification channel for Android...');

      const AndroidNotificationChannel channel = AndroidNotificationChannel(
        'notification_test', // Channel ID
        'Notification Test', // Channel name
        description: 'Test notifications to verify notification functionality',
        importance: Importance.high,
        playSound: true,
        enableVibration: true,
        showBadge: true,
        enableLights: true,
        ledColor: Color(0xFF2196F3),
      );

      final androidImplementation = _flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>();

      if (androidImplementation != null) {
        await androidImplementation.createNotificationChannel(channel);
        debugPrint('✅ Test notification channel created successfully');
      } else {
        debugPrint('❌ Failed to get Android implementation');
      }
    } catch (e) {
      debugPrint('❌ Error creating test notification channel: $e');
      rethrow;
    }
  }

  /// Handle notification tap events
  void _onNotificationTapped(NotificationResponse notificationResponse) {
    debugPrint('🧪 Test notification tapped: ${notificationResponse.payload}');
  }

  /// Show a simple test notification
  Future<void> showTestNotification() async {
    try {
      debugPrint('🧪 ===== SHOWING TEST NOTIFICATION =====');

      if (!_isInitialized) {
        debugPrint('🧪 Service not initialized, initializing now...');
        await initialize();
      }

      // Check if notifications are enabled
      final areEnabled = await areNotificationsEnabled();
      debugPrint('🧪 Notifications enabled: $areEnabled');
      if (!areEnabled) {
        debugPrint('❌ Notifications are disabled by user');
        throw Exception('Notifications are disabled by user');
      }

      const title = '🧪 Notification Test';
      const body = 'This is a test notification to verify functionality';

      // Android notification details
      const androidNotificationDetails = AndroidNotificationDetails(
        'notification_test',
        'Notification Test',
        channelDescription:
            'Test notifications to verify notification functionality',
        importance: Importance.high,
        priority: Priority.high,
        icon: '@mipmap/launcher_icon',
        color: Color(0xFF2196F3),
        colorized: true,
        enableVibration: true,
        playSound: true,
        ticker: 'Test notification',
      );

      const NotificationDetails notificationDetails = NotificationDetails(
        android: androidNotificationDetails,
      );

      debugPrint('🧪 Calling _flutterLocalNotificationsPlugin.show()...');

      // Show the test notification
      await _flutterLocalNotificationsPlugin.show(
        _testNotificationId,
        title,
        body,
        notificationDetails,
        payload: 'test_notification_${DateTime.now().millisecondsSinceEpoch}',
      );

      debugPrint('✅ Test notification shown successfully');
    } catch (e) {
      debugPrint('❌ Error showing test notification: $e');
      debugPrint('❌ Error type: ${e.runtimeType}');
      debugPrint('❌ Stack trace: ${StackTrace.current}');
      rethrow;
    }
  }

  /// Check if notifications are enabled
  Future<bool> areNotificationsEnabled() async {
    if (Platform.isAndroid) {
      final androidImplementation = _flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>();
      return await androidImplementation?.areNotificationsEnabled() ?? false;
    }
    return true; // Assume enabled for other platforms
  }

  /// Request notification permissions
  Future<bool> requestPermissions() async {
    if (Platform.isAndroid) {
      final androidImplementation = _flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>();
      return await androidImplementation?.requestNotificationsPermission() ??
          false;
    }
    return true; // Assume granted for other platforms
  }

  /// Hide the test notification
  Future<void> hideTestNotification() async {
    try {
      await _flutterLocalNotificationsPlugin.cancel(_testNotificationId);
      debugPrint('🧪 Test notification hidden');
    } catch (e) {
      debugPrint('❌ Error hiding test notification: $e');
    }
  }

  /// Get detailed notification status for debugging (Android 11/12 compatible)
  Future<Map<String, dynamic>> getNotificationStatus() async {
    final status = <String, dynamic>{};

    try {
      status['initialized'] = _isInitialized;
      status['platform'] = Platform.isAndroid ? 'Android' : 'Other';

      if (Platform.isAndroid) {
        final androidImplementation = _flutterLocalNotificationsPlugin
            .resolvePlatformSpecificImplementation<
                AndroidFlutterLocalNotificationsPlugin>();

        if (androidImplementation != null) {
          status['android_implementation_available'] = true;

          // Basic notification status
          status['notifications_enabled'] =
              await androidImplementation.areNotificationsEnabled();

          // Android 12+ specific checks
          try {
            // Check if exact alarms are available (Android 12+)
            final canScheduleExactAlarms =
                await androidImplementation.canScheduleExactNotifications();
            status['can_schedule_exact_alarms'] = canScheduleExactAlarms;
          } catch (e) {
            status['exact_alarms_check_error'] = e.toString();
          }

          // Additional Android version info
          status['android_sdk_version'] =
              'Available through device_info_plus if needed';
        } else {
          status['android_implementation_available'] = false;
        }
      }

      debugPrint('🧪 ===== DETAILED NOTIFICATION STATUS =====');
      debugPrint('🧪 Initialized: ${status['initialized']}');
      debugPrint('🧪 Platform: ${status['platform']}');
      debugPrint(
          '🧪 Notifications Enabled: ${status['notifications_enabled']}');
      debugPrint(
          '🧪 Android Implementation: ${status['android_implementation_available']}');
      debugPrint(
          '🧪 Can Schedule Exact Alarms: ${status['can_schedule_exact_alarms']}');
      debugPrint('🧪 Full Status: $status');
    } catch (e) {
      status['error'] = e.toString();
      debugPrint('❌ Error getting notification status: $e');
    }

    return status;
  }
}
