import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:line_icons/line_icons.dart';
import '../../../features/profile/application/profile_notifier_riverpod.dart';
import '../../../providers/theme_provider.dart';
import '../../../utils/app_themes.dart';
import '../../wallet/wallet_screen.dart';
import '../ManageVehicles/manage_vehicles_page.dart';
import '../RFID_PAGE/RFIDPAGE.dart';
import '../Notification/Notification_Page.dart';
import '../Privacy_Settings/Privacy_settings_page.dart';
import '../FAQ/faq_page.dart';
import '../FAQ/HELP&SUPPORT/support_center_page.dart';
import '../EditProfile/edit_profile_page.dart';
import '../SavedStations/saved_stations_page.dart';
import '../../../models/user_model.dart';

import '../../../services/logout_service.dart';
import '../../../widgets/custom_switch.dart';
import '../../../widgets/liquid_glass_switch.dart';
import '../../charging/charging_history_page.dart';

/// A Riverpod-powered profile screen
class ProfileScreenRiverpod extends ConsumerWidget {
  const ProfileScreenRiverpod({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // Watch the profile state
    final AsyncValue<ProfileState> profileState = ref.watch(profileProvider);

    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        centerTitle: true,
        title: Text(
          'Profile',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: isDarkMode ? Colors.white : Colors.black87,
          ),
        ),
        backgroundColor: isDarkMode ? AppThemes.darkSurface : Colors.white,
      ),
      body: profileState.when(
        loading: () => const Center(
          child: CircularProgressIndicator(),
        ),
        error: (error, stackTrace) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, color: Colors.red, size: 48),
              const SizedBox(height: 16),
              Text(
                'Error loading profile',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 8),
              Text(
                error.toString(),
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () {
                  ref.read(profileProvider.notifier).loadUserData();
                },
                child: const Text('Try Again'),
              ),
            ],
          ),
        ),
        data: (profile) => SingleChildScrollView(
          child: Column(
            children: [
              // User header section
              _buildUserHeader(context, ref, profile),

              const SizedBox(height: 16),

              // Stats section
              _buildStatsSection(context, ref, profile),

              const SizedBox(height: 16),

              // Settings section
              _buildSettingsSection(context, ref, profile),

              const SizedBox(height: 16),

              // Menu section
              _buildMenuSection(context, ref, profile),

              const SizedBox(height: 16),

              // Logout button
              _buildLogoutButton(context, ref, profile),

              // Additional bottom spacing to prevent navigation bar overlay
              const SizedBox(height: 120),
            ],
          ),
        ),
      ),
    );
  }

  // User header section
  Widget _buildUserHeader(
      BuildContext context, WidgetRef ref, ProfileState profile) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          // Profile picture and edit button
          Stack(
            alignment: Alignment.center,
            children: [
              CircleAvatar(
                radius: 50,
                backgroundColor:
                    isDarkMode ? Colors.grey[800] : Colors.grey[200],
                child: Text(
                  profile.userName.isNotEmpty
                      ? profile.userName[0].toUpperCase()
                      : '?',
                  style: TextStyle(
                    fontSize: 36,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode ? Colors.white : Colors.black87,
                  ),
                ),
              ),
              Positioned(
                right: 0,
                bottom: 0,
                child: CircleAvatar(
                  radius: 18,
                  backgroundColor: isDarkMode ? Colors.grey[700] : Colors.white,
                  child: IconButton(
                    icon: Icon(
                      Icons.edit,
                      size: 18,
                      color: isDarkMode ? Colors.white : Colors.black87,
                    ),
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => EditProfilePage(
                            userProfile: UserProfile(
                              name: profile.userName,
                              email: profile.userEmail,
                              phone: profile.userPhone,
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // User name
          Text(
            profile.userName,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: isDarkMode ? Colors.white : Colors.black87,
            ),
          ),

          const SizedBox(height: 4),

          // User email
          Text(
            profile.userEmail,
            style: TextStyle(
              fontSize: 14,
              color: isDarkMode
                  ? AppThemes.darkTextSecondary
                  : Colors.grey.shade700,
            ),
          ),

          const SizedBox(height: 2),

          // User phone
          Text(
            profile.userPhone,
            style: TextStyle(
              fontSize: 14,
              color: isDarkMode
                  ? AppThemes.darkTextSecondary
                  : Colors.grey.shade700,
            ),
          ),
        ],
      ),
    );
  }

  // Stats section
  Widget _buildStatsSection(
      BuildContext context, WidgetRef ref, ProfileState profile) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Row(
        children: [
          // Wallet balance card
          Expanded(
            child: _buildStatCard(
              context,
              icon: LineIcons.wallet,
              iconColor: Colors.blue,
              title: 'Balance',
              value: '₹${profile.walletBalance.toStringAsFixed(2)}',
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => const WalletPage()),
                );
              },
            ),
          ),

          const SizedBox(width: 12),

          // Vehicles card
          Expanded(
            child: _buildStatCard(
              context,
              icon: Icons.directions_car_filled_outlined,
              iconColor: Colors.green,
              title: 'Vehicles',
              value: '2', // Will be updated with real data in future
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const ManageVehiclesPage(),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  // Settings section
  Widget _buildSettingsSection(
      BuildContext context, WidgetRef ref, ProfileState profile) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Container(
        decoration: BoxDecoration(
          color: isDarkMode ? AppThemes.darkSurface : Colors.white,
          borderRadius: BorderRadius.circular(16), // Increased radius
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(13),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          children: [
            // Instant charging switch
            _buildInstantChargingSwitchTile(
              context,
              ref,
              profile,
            ),

            Divider(
              height: 1,
              thickness: 1,
              indent: 16,
              endIndent: 16,
              color: isDarkMode ? Colors.grey[800] : Colors.grey[200],
            ),

            // Auto charge switch with liquid glass effect
            _buildLiquidGlassSwitchTile(
              context,
              ref,
              icon: Icons.autorenew,
              iconColor: Colors.blue,
              label: 'Auto Charge',
              value: profile.isAutoChargeOn,
              activeColor: Colors.blue,
              activeTrackColor: const Color(0xFFBBDEFB), // Light blue for track
              onChanged: (val) {
                ref.read(profileProvider.notifier).toggleAutoCharge(val);
              },
            ),
          ],
        ),
      ),
    );
  }

  // Helper method to build instant charging switch tile with loading and error handling
  Widget _buildInstantChargingSwitchTile(
    BuildContext context,
    WidgetRef ref,
    ProfileState profile,
  ) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    const iconColor = Color(0xFFFFA000); // Amber color for lightning icon
    const activeColor = Color(0xFFFFA000); // Amber color for switch
    const activeTrackColor = Color(0xFFFFECB3); // Light amber for track

    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
          child: Row(
            children: [
              // Icon with colored background
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: profile.isInstantChargingOn
                      ? iconColor.withAlpha(51) // 0.2 opacity = 51 alpha
                      : Colors.grey.withAlpha(25), // 0.1 opacity = 25 alpha
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.bolt,
                  color: profile.isInstantChargingOn
                      ? iconColor
                      : isDarkMode
                          ? Colors.white70
                          : Colors.grey,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              // Label
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Instant Charging',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: isDarkMode ? Colors.white : Colors.black87,
                      ),
                    ),
                  ],
                ),
              ),
              // Liquid Glass Switch for premium feel
              LiquidGlassSwitch(
                value: profile.isInstantChargingOn,
                onChanged: (val) {
                  ref.read(profileProvider.notifier).toggleInstantCharging(val);
                },
                activeColor: activeColor,
                activeTrackColor: activeTrackColor,
                inactiveColor: Colors.white,
                inactiveTrackColor:
                    isDarkMode ? Colors.grey.shade800 : Colors.grey.shade300,
                width: 54.0, // Slightly larger for premium feel
                height: 30.0,
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Helper method to build a liquid glass switch tile
  Widget _buildLiquidGlassSwitchTile(
    BuildContext context,
    WidgetRef ref, {
    required IconData icon,
    required Color iconColor,
    required String label,
    required bool value,
    required Color activeColor,
    required Color activeTrackColor,
    required ValueChanged<bool> onChanged,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
      child: Row(
        children: [
          // Icon with colored background
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: value
                  ? iconColor.withAlpha(51) // 0.2 opacity = 51 alpha
                  : Colors.grey.withAlpha(25), // 0.1 opacity = 25 alpha
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: value
                  ? iconColor
                  : isDarkMode
                      ? Colors.white70
                      : Colors.grey,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          // Label
          Expanded(
            child: Text(
              label,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: isDarkMode ? Colors.white : Colors.black87,
              ),
            ),
          ),
          const Spacer(),
          // Liquid Glass Switch
          LiquidGlassSwitch(
            value: value,
            onChanged: onChanged,
            activeColor: activeColor,
            activeTrackColor: activeTrackColor,
            inactiveColor: Colors.white,
            inactiveTrackColor:
                isDarkMode ? Colors.grey.shade800 : Colors.grey.shade300,
            width: 54.0, // Premium size
            height: 30.0,
          ),
        ],
      ),
    );
  }

  // Helper method to build a custom switch tile
  Widget _buildCustomSwitchTile(
    BuildContext context, {
    required IconData icon,
    required Color iconColor,
    required String label,
    required bool value,
    required Color activeColor,
    required Color activeTrackColor,
    required ValueChanged<bool> onChanged,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
      child: Row(
        children: [
          // Icon with colored background
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: value
                  ? iconColor.withAlpha(51) // 0.2 opacity = 51 alpha
                  : Colors.grey.withAlpha(25), // 0.1 opacity = 25 alpha
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: value
                  ? iconColor
                  : isDarkMode
                      ? Colors.white70
                      : Colors.grey,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          // Label
          Text(
            label,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: isDarkMode ? Colors.white : Colors.black87,
            ),
          ),
          const Spacer(),
          // Custom switch
          CustomSwitch(
            value: value,
            onChanged: onChanged,
            activeColor: activeColor,
            activeTrackColor: activeTrackColor,
            inactiveColor: Colors.white,
            inactiveTrackColor:
                isDarkMode ? Colors.grey.shade800 : Colors.grey.shade300,
          ),
        ],
      ),
    );
  }

  // Menu section
  Widget _buildMenuSection(
      BuildContext context, WidgetRef ref, ProfileState profile) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Container(
        decoration: BoxDecoration(
          color: isDarkMode ? AppThemes.darkSurface : Colors.white,
          borderRadius: BorderRadius.circular(20), // More curved radius
          border: Border.all(
            color: isDarkMode ? Colors.grey.shade800 : Colors.grey.shade200,
            width: 1.5,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(13),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          children: [
            // Saved Stations - Most frequently used for trip planning
            _buildMenuItem(
              context,
              icon: Icons.bookmark,
              title: 'Saved Stations',
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (context) => const SavedStationsPage()),
                );
              },
            ),

            _buildDivider(context),

            // Charging History - Important for tracking usage
            _buildMenuItem(
              context,
              icon: Icons.history,
              title: 'Charging History',
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const ChargingHistoryPage(),
                  ),
                );
              },
            ),

            _buildDivider(context),

            // RFID Cards - Payment and access management
            _buildMenuItem(
              context,
              icon: Icons.credit_card,
              title: 'RFID Cards',
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => const RFIDPage()),
                );
              },
            ),

            _buildDivider(context),

            // Manage Vehicles - Vehicle configuration
            _buildMenuItem(
              context,
              icon: Icons.directions_car_filled_outlined,
              title: 'Manage Vehicles',
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const ManageVehiclesPage(),
                  ),
                );
              },
            ),

            _buildDivider(context),

            // Notifications - User preferences
            _buildMenuItem(
              context,
              icon: Icons.notifications_outlined,
              title: 'Notifications',
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (context) => const NotificationPage()),
                );
              },
            ),

            _buildDivider(context),

            // Privacy Settings - Security and privacy controls
            _buildMenuItem(
              context,
              icon: Icons.lock_outline_rounded,
              title: 'Privacy Settings',
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (context) => const PrivacySettingsPage()),
                );
              },
            ),

            _buildDivider(context),

            // FAQs - Self-service help
            _buildMenuItem(
              context,
              icon: Icons.question_answer_outlined,
              title: 'FAQs',
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => const FAQPage()),
                );
              },
            ),

            _buildDivider(context),

            // Help & Support - Customer service
            _buildMenuItem(
              context,
              icon: Icons.support_agent,
              title: 'Help & Support',
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (context) => const SupportCenterPage()),
                );
              },
            ),

            _buildDivider(context),

            // Theme Settings
            Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(16),
                  onTap: () {
                    // Toggle theme on tap of the entire row
                    ref.read(themeNotifierProvider.notifier).toggleTheme();
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 4.0),
                    child: ListTile(
                      leading: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: isDarkMode
                              ? Colors.grey.withAlpha(30)
                              : Colors.grey.withAlpha(15),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          isDarkMode ? Icons.dark_mode : Icons.light_mode,
                          color: isDarkMode ? Colors.white70 : Colors.black54,
                        ),
                      ),
                      title: Text(
                        'Dark Mode',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: isDarkMode ? Colors.white : Colors.black87,
                        ),
                      ),
                      trailing: LiquidGlassSwitch(
                        value: isDarkMode,
                        onChanged: (value) {
                          ref
                              .read(themeNotifierProvider.notifier)
                              .toggleTheme();
                        },
                        activeColor: Colors.green,
                        activeTrackColor: const Color(0xFFBBDEFB),
                        inactiveColor: Colors.white,
                        inactiveTrackColor: isDarkMode
                            ? Colors.grey.shade800
                            : Colors.grey.shade300,
                        width: 54.0, // Premium size
                        height: 30.0,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Logout button
  Widget _buildLogoutButton(
      BuildContext context, WidgetRef ref, ProfileState profile) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          color: isDarkMode ? Colors.red.shade900 : Colors.red.shade50,
          borderRadius: BorderRadius.circular(12),
        ),
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () => _showLogoutConfirmationDialog(context, ref),
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.logout_rounded,
                  color: isDarkMode ? Colors.white : Colors.red,
                ),
                const SizedBox(width: 8),
                Text(
                  'Logout',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: isDarkMode ? Colors.white : Colors.red,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Helper method to build a stat card
  Widget _buildStatCard(
    BuildContext context, {
    required IconData icon,
    required Color iconColor,
    required String title,
    required String value,
    required VoidCallback onTap,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      decoration: BoxDecoration(
        color: isDarkMode ? AppThemes.darkSurface : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: onTap,
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: iconColor.withAlpha(25),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(icon, color: iconColor),
                    ),
                    const Spacer(),
                    Icon(
                      Icons.arrow_forward_ios,
                      size: 14,
                      color: isDarkMode ? Colors.white54 : Colors.black38,
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    color: isDarkMode ? Colors.white70 : Colors.black54,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode ? Colors.white : Colors.black87,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Helper method to build a menu item
  Widget _buildMenuItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: onTap,
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 4.0),
            child: ListTile(
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: isDarkMode
                      ? Colors.grey.withAlpha(30)
                      : Colors.grey.withAlpha(15),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  icon,
                  color: isDarkMode ? Colors.white70 : Colors.black54,
                ),
              ),
              title: Text(
                title,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: isDarkMode ? Colors.white : Colors.black87,
                ),
              ),
              trailing: Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: isDarkMode ? Colors.white54 : Colors.black38,
              ),
            ),
          ),
        ),
      ),
    );
  }

  // Helper method to build a divider
  Widget _buildDivider(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Divider(
      height: 1,
      thickness: 1,
      indent: 16,
      endIndent: 16,
      color: isDarkMode ? Colors.grey[800] : Colors.grey[200],
    );
  }

  // Logout confirmation dialog
  void _showLogoutConfirmationDialog(BuildContext context, WidgetRef ref) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          backgroundColor: isDarkMode ? AppThemes.darkSurface : Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Text(
            'Confirm Logout',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: isDarkMode ? AppThemes.darkTextPrimary : Colors.black87,
            ),
          ),
          content: Text(
            'Are you sure you want to logout?',
            style: TextStyle(
              fontSize: 16,
              color: isDarkMode ? AppThemes.darkTextSecondary : Colors.black54,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(dialogContext).pop(); // Dismiss dialog
              },
              child: const Text(
                'No',
                style: TextStyle(
                  color: Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.of(dialogContext).pop(); // Dismiss dialog

                // Show loading indicator
                showDialog(
                  context: context,
                  barrierDismissible: false,
                  builder: (BuildContext context) {
                    return const Center(
                      child: CircularProgressIndicator(),
                    );
                  },
                );

                try {
                  // CRITICAL: Use comprehensive logout service
                  final logoutService = LogoutService();
                  final logoutSuccess =
                      await logoutService.performCompleteLogout();

                  // Close loading indicator
                  if (context.mounted) Navigator.of(context).pop();

                  if (logoutSuccess) {
                    // Validate logout was successful
                    final validationSuccess =
                        await logoutService.validateLogoutSuccess();

                    if (validationSuccess) {
                      // CRITICAL: Invalidate all Riverpod providers to force refresh
                      ref.invalidate(profileProvider);

                      // Navigate to auth screen and clear navigation stack
                      if (context.mounted) {
                        Navigator.of(context)
                            .pushNamedAndRemoveUntil('/auth', (route) => false);
                      }
                    } else {
                      // Show validation error
                      if (context.mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text(
                                'Logout validation failed. Some data may persist.'),
                            backgroundColor: Colors.orange,
                          ),
                        );
                      }
                    }
                  } else {
                    // Show logout error
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content:
                              Text('Error during logout. Please try again.'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  }
                } catch (e) {
                  // Close loading indicator
                  if (context.mounted) Navigator.of(context).pop();

                  // Show error message
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Critical error during logout: $e'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              child: const Text(
                'Yes',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
