import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'dart:convert'; // Required for jsonEncode if used

import 'api_config.dart';
import 'api_exception.dart';
import '../../models/nearest_station_response.dart'; // Import the model
import '../../models/station/station_search_response.dart'; // Import the model
import '../../models/station/paginated_station_response.dart'; // Import the paginated model
import '../../services/auth_manager.dart'; // Import AuthManager for consistent token handling

/// Unified API service for making HTTP requests to the Ecoplug API
class ApiService {
  // Dio client for making requests
  late final Dio _dio;

  // Device info for debugging
  Map<String, dynamic> _deviceInfo = {};
  String _appVersion = '';
  bool _deviceInfoLoaded = false;

  // Singleton pattern
  static final ApiService _instance = ApiService._internal();
  factory ApiService() => _instance;

  ApiService._internal() {
    _initDio();
    _loadDeviceInfo();
  }

  /// Initialize Dio with robust network configuration for OTP reliability
  void _initDio() {
    final options = BaseOptions(
      baseUrl: ApiConfig.apiUrl,
      connectTimeout: const Duration(seconds: 15), // Optimized for OTP
      receiveTimeout: const Duration(seconds: 15), // Optimized for OTP
      sendTimeout: const Duration(seconds: 15), // Optimized for OTP
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Connection': 'close', // Prevent connection reset issues
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      },
      persistentConnection: false, // Prevent connection pooling issues
      validateStatus: (status) =>
          status != null && status >= 200 && status < 500,
      followRedirects: true,
      maxRedirects: 3,
    );

    _dio = Dio(options);

    // Add interceptors for logging and authentication
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        // Log request details in debug mode
        if (kDebugMode) {
          debugPrint('--> ${options.method} ${options.uri}');
        }

        // Add auth token if not a public request
        if (!ApiConfig.publicEndpoints.contains(options.path)) {
          // Use ApiConfig for public endpoints
          final token = await _getToken();
          if (token != null) {
            options.headers['Authorization'] = 'Bearer $token';
            if (kDebugMode) {
              debugPrint('Authorization Header Added (token value hidden)');
            }
          }
        }

        return handler.next(options);
      },
      onResponse: (response, handler) async {
        // Only log non-sensitive information in debug mode
        if (kDebugMode) {
          debugPrint(
              '<-- ${response.statusCode} ${response.requestOptions.method} ${response.requestOptions.uri}');
        }
        return handler.next(response);
      },
      onError: (DioException e, handler) async {
        // Log error details in debug mode
        if (kDebugMode) {
          debugPrint(
              '<-- Error: ${e.response?.statusCode} ${e.requestOptions.method} ${e.requestOptions.uri}');
          debugPrint('Error Message: ${e.message}');
          debugPrint('Error Type: ${e.type}');
        }

        // Handle authentication errors (401/403) - server-side token validation
        if (e.response?.statusCode == 401 || e.response?.statusCode == 403) {
          debugPrint(
              '🔐 Authentication error detected: ${e.response?.statusCode}');
          debugPrint('🔐 Server indicates token is invalid or expired');

          // Clear invalid token using AuthManager
          try {
            final authManager = AuthManager();
            await authManager.logout();
            debugPrint('🔐 Invalid token cleared due to server response');
          } catch (clearError) {
            debugPrint('🔐 Error clearing invalid token: $clearError');
          }
        }

        // Implement retry logic for network errors (but not auth errors)
        if (_shouldRetryRequest(e)) {
          final retryCount = e.requestOptions.extra['retryCount'] ?? 0;
          if (retryCount < 2) {
            // Max 2 retries for critical requests
            e.requestOptions.extra['retryCount'] = retryCount + 1;

            // Wait before retry with exponential backoff
            final delay =
                Duration(milliseconds: (500 * (retryCount + 1)).toInt());
            await Future.delayed(delay);

            if (kDebugMode) {
              debugPrint(
                  '🔄 Retrying request (attempt ${retryCount + 1}/2) after ${delay.inMilliseconds}ms');
            }

            try {
              final response = await _dio.fetch(e.requestOptions);
              return handler.resolve(response);
            } catch (retryError) {
              if (kDebugMode) {
                debugPrint('❌ Retry failed: $retryError');
              }
              // Continue with original error if retry fails
            }
          }
        }

        return handler.next(e);
      },
    ));
  }

  /// Determine if a request should be retried based on the error type
  bool _shouldRetryRequest(DioException e) {
    // Don't retry authentication errors - these need to be handled by clearing tokens
    final statusCode = e.response?.statusCode;
    if (statusCode == 401 || statusCode == 403) {
      return false;
    }

    // Retry on connection errors, timeouts, and server errors
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.receiveTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.connectionError:
        return true;
      case DioExceptionType.badResponse:
        // Retry on 5xx server errors (but not 4xx client errors)
        return statusCode != null && statusCode >= 500 && statusCode < 600;
      default:
        return false;
    }
  }

  /// Load device info for debugging
  Future<void> _loadDeviceInfo() async {
    try {
      final deviceInfoPlugin = DeviceInfoPlugin();
      final packageInfo = await PackageInfo.fromPlatform();
      _appVersion = packageInfo.version;

      if (Platform.isAndroid) {
        final androidInfo = await deviceInfoPlugin.androidInfo;
        _deviceInfo = {
          'device': androidInfo.device,
          'manufacturer': androidInfo.manufacturer,
          'model': androidInfo.model,
          'version': androidInfo.version.release,
          'sdk': androidInfo.version.sdkInt,
        };
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfoPlugin.iosInfo;
        _deviceInfo = {
          'name': iosInfo.name,
          'model': iosInfo.model,
          'systemName': iosInfo.systemName,
          'systemVersion': iosInfo.systemVersion,
        };
      }

      _deviceInfoLoaded = true;
      debugPrint('Device info loaded: $_deviceInfo');
      debugPrint('App version: $_appVersion');
    } catch (e) {
      debugPrint('Error loading device info: $e');
    }
  }

  /// Helper to determine appropriate timeout for different endpoints
  Duration _getTimeoutForEndpoint(String endpoint) {
    // Wallet and payment endpoints need longer timeouts
    if (endpoint.contains('wallet') ||
        endpoint.contains('payment') ||
        endpoint.contains('transaction')) {
      return const Duration(
          seconds: 30); // Extended timeout for financial transactions
    }

    // Authentication endpoints - optimized for OTP reliability
    if (endpoint.contains('auth') ||
        endpoint.contains('login') ||
        endpoint.contains('verify-otp') ||
        endpoint.contains('user/login') ||
        endpoint.contains('user/verify-otp')) {
      return const Duration(seconds: 12); // Optimized timeout for OTP
    }

    // Charging session endpoints need extended timeouts for OCCP protocol
    if (endpoint.contains('sessions') ||
        endpoint.contains('charging') ||
        endpoint.contains('transaction') ||
        endpoint.contains('remote-stop') ||
        endpoint.contains('on-going')) {
      return const Duration(
          seconds: 45); // Extended timeout for OCCP charging operations
    }

    // Station-related endpoints need longer timeouts due to complex processing
    if (endpoint.contains('stations') ||
        endpoint.contains('station') ||
        endpoint.contains('nearest') ||
        endpoint.contains('search') ||
        endpoint.contains('paginate')) {
      return const Duration(seconds: 60); // Extended timeout for station data
    }

    // For other endpoints, use standard timeout
    return const Duration(seconds: 15); // Default timeout
  }

  /// Make a GET request to the specified endpoint with authentication
  Future<dynamic> get(String endpoint,
      {Map<String, dynamic>? queryParams}) async {
    // Determine if this is a critical endpoint that needs longer timeout
    final Duration timeoutDuration = _getTimeoutForEndpoint(endpoint);

    // Ensure endpoint has the base URL if it doesn't start with http
    String fullEndpoint = endpoint;
    if (!endpoint.startsWith('http')) {
      // Handle endpoints with or without leading slash
      if (!endpoint.startsWith('/')) {
        fullEndpoint = '/$endpoint';
      } else {
        fullEndpoint = endpoint;
      }

      // Always prepend the API URL to non-absolute URLs
      fullEndpoint = '${ApiConfig.apiUrl}$fullEndpoint';
    }

    debugPrint('Making GET request to endpoint: $fullEndpoint');

    try {
      final response = await _dio.get(
        fullEndpoint,
        queryParameters: queryParams,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Connection': 'close',
          },
          extra: {'fresh': true},
          receiveTimeout: timeoutDuration,
          sendTimeout: timeoutDuration,
        ),
      );
      return _handleResponse(response);
    } on DioException catch (e) {
      final errorMessage = _handleDioError(e, endpoint);
      final errorCode = _getErrorCode(e);
      throw ApiException(errorMessage, code: errorCode);
    } catch (e) {
      debugPrint('Error during GET request: $e');
      throw ApiException('Failed to complete request: $e');
    }
  }

  /// Download binary data (like PDF) from the specified endpoint with authentication
  Future<Uint8List> downloadBinary(String endpoint,
      {Map<String, dynamic>? queryParams}) async {
    // Determine if this is a critical endpoint that needs longer timeout
    final Duration timeoutDuration = _getTimeoutForEndpoint(endpoint);

    // Ensure endpoint has the base URL if it doesn't start with http
    String fullEndpoint = endpoint;
    if (!endpoint.startsWith('http')) {
      // Handle endpoints with or without leading slash
      if (!endpoint.startsWith('/')) {
        fullEndpoint = '/$endpoint';
      } else {
        fullEndpoint = endpoint;
      }

      // Always prepend the API URL to non-absolute URLs
      fullEndpoint = '${ApiConfig.apiUrl}$fullEndpoint';
    }

    debugPrint('\n🔍 ===== BINARY DOWNLOAD REQUEST =====');
    debugPrint('🌐 Full URL: $fullEndpoint');
    debugPrint('📱 Endpoint: $endpoint');
    debugPrint('🔗 Query Params: $queryParams');
    debugPrint('⏰ Timeout: ${timeoutDuration.inSeconds} seconds');

    try {
      debugPrint('🚀 Starting binary download request...');
      final startTime = DateTime.now();

      final response = await _dio.get(
        fullEndpoint,
        queryParameters: queryParams,
        options: Options(
          headers: {
            'Accept': 'application/pdf',
            'Content-Type': 'application/json',
          },
          receiveTimeout: timeoutDuration,
          sendTimeout: timeoutDuration,
          responseType: ResponseType.bytes, // Important for binary data
          validateStatus: (status) {
            debugPrint('📊 Response Status Code: $status');
            return status != null && status >= 200 && status < 300;
          },
        ),
      );

      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);
      debugPrint('⏱️ Request completed in ${duration.inMilliseconds}ms');
      debugPrint('📊 Response Status: ${response.statusCode}');
      debugPrint(
          '📄 Response Data Length: ${response.data?.length ?? 0} bytes');

      if (response.statusCode == 200 && response.data != null) {
        debugPrint('✅ Binary download successful');
        return Uint8List.fromList(response.data);
      } else {
        debugPrint('❌ Binary download failed: ${response.statusCode}');
        throw ApiException('Download failed: ${response.statusCode}');
      }
    } on DioException catch (e) {
      debugPrint('❌ DioException in binary download: ${e.message}');
      debugPrint('❌ Error Type: ${e.type}');
      debugPrint('❌ Response: ${e.response?.data}');
      final errorMessage = _handleDioError(e, endpoint);
      final errorCode = _getErrorCode(e);
      throw ApiException(errorMessage, code: errorCode);
    } catch (e) {
      debugPrint('❌ Unexpected error in binary download: $e');
      throw ApiException('Unexpected error: $e');
    }
  }

  /// Make a POST request to the specified endpoint with authentication
  Future<dynamic> post(String endpoint, {Map<String, dynamic>? data}) async {
    // Determine if this is a critical endpoint that needs longer timeout
    final Duration timeoutDuration = _getTimeoutForEndpoint(endpoint);

    // Ensure endpoint has the base URL if it doesn't start with http
    String fullEndpoint = endpoint;
    if (!endpoint.startsWith('http')) {
      // Handle endpoints with or without leading slash
      if (!endpoint.startsWith('/')) {
        fullEndpoint = '/$endpoint';
      } else {
        fullEndpoint = endpoint;
      }

      // Always prepend the API URL to non-absolute URLs
      fullEndpoint = '${ApiConfig.apiUrl}$fullEndpoint';
    }

    // COMPREHENSIVE POST REQUEST DEBUGGING - SHOW ALL DATA
    if (kDebugMode && data != null) {
      debugPrint('\n🚀 === COMPLETE POST REQUEST DEBUG ===');
      debugPrint('🌐 ENDPOINT: $endpoint');
      debugPrint('🌐 FULL URL: $fullEndpoint');
      debugPrint('📦 REQUEST METHOD: POST');
      debugPrint('⏰ TIMEOUT: ${timeoutDuration.inSeconds} seconds');
      debugPrint(
          '🔐 AUTHENTICATION: ${await getToken() != null ? 'YES' : 'NO'}');
      debugPrint('📦 REQUEST HEADERS:');
      debugPrint('   Content-Type: application/json');
      debugPrint('   Accept: application/json');
      debugPrint(
          '   Authorization: Bearer ${await getToken() != null ? '[TOKEN_SET]' : '[NO_TOKEN]'}');
      debugPrint('📦 REQUEST PAYLOAD TYPE: ${data.runtimeType}');
      debugPrint('📦 REQUEST PAYLOAD KEYS: ${data.keys.toList()}');
      debugPrint('📦 REQUEST PAYLOAD (JSON):');
      try {
        const encoder = JsonEncoder.withIndent('  ');
        debugPrint(encoder.convert(data));
      } catch (e) {
        debugPrint('   [ERROR ENCODING JSON: $e]');
        debugPrint('   RAW DATA: $data');
      }
      debugPrint('🚀 === END POST REQUEST DEBUG ===\n');
    }

    debugPrint('Making POST request to endpoint: $fullEndpoint');

    try {
      final response = await _dio.post(
        fullEndpoint,
        data: data,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Connection': 'close',
          },
          extra: {'fresh': true},
          receiveTimeout: timeoutDuration,
          sendTimeout: timeoutDuration,
        ),
      );
      return _handleResponse(response);
    } on DioException catch (e) {
      final errorMessage = _handleDioError(e, endpoint);
      final errorCode = _getErrorCode(e);
      throw ApiException(errorMessage, code: errorCode);
    } catch (e) {
      debugPrint('Error during POST request: $e');
      throw ApiException('Failed to complete request: $e');
    }
  }

  /// Make a POST request without authentication (e.g., for login/OTP) - OPTIMIZED FOR OTP
  Future<dynamic> publicPost(String endpoint, Map<String, dynamic> data) async {
    final fullUrl = '${ApiConfig.baseUrl}${ApiConfig.apiVersion}$endpoint';
    final timeout = _getTimeoutForEndpoint(endpoint);

    debugPrint('\n🔍 ===== OTP REQUEST OPTIMIZED =====');
    debugPrint('🌐 Full URL: $fullUrl');
    debugPrint('📱 Endpoint: $endpoint');
    debugPrint('📄 Request Data: $data');
    debugPrint('⏰ Timeout: ${timeout.inSeconds} seconds');

    try {
      debugPrint('🚀 Starting optimized POST request...');
      final startTime = DateTime.now();

      final response = await _dio.post(
        endpoint,
        data: data,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization':
                null, // Explicitly remove auth for public endpoints
            'User-Agent': 'ELP-Flutter-App/1.0',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0',
            'Connection': 'close', // Force connection close
          },
          receiveTimeout: timeout,
          sendTimeout: timeout,
          extra: {
            'retryCount': 0, // Initialize retry count
            'isOtpRequest': true, // Mark as OTP request for special handling
          },
          validateStatus: (status) {
            debugPrint('📊 Response Status Code: $status');
            // Accept 2xx and 4xx responses for debugging, but not 5xx
            return status != null && status >= 200 && status < 500;
          },
        ),
      );

      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);

      debugPrint('✅ ===== OTP REQUEST SUCCESSFUL =====');
      debugPrint('⏱️ Request Duration: ${duration.inMilliseconds}ms');
      debugPrint('📊 Status Code: ${response.statusCode}');
      debugPrint('📄 Response Data: ${response.data}');

      return _handleResponse(response);
    } on DioException catch (e) {
      debugPrint('❌ ===== OTP REQUEST FAILED =====');
      debugPrint('🔍 DioException Type: ${e.type}');
      debugPrint('🔍 DioException Message: ${e.message}');
      debugPrint('🔍 Response Status: ${e.response?.statusCode}');
      debugPrint('🔍 Response Data: ${e.response?.data}');

      // Convert DioException to a more user-friendly response for OTP
      return _handleOtpError(e, endpoint);
    } catch (e) {
      debugPrint('❌ ===== UNEXPECTED OTP ERROR =====');
      debugPrint('🔍 Error Type: ${e.runtimeType}');
      debugPrint('🔍 Error Message: $e');

      // Return error response instead of throwing for OTP requests
      return {
        'success': false,
        'message': 'Network error occurred. Please try again.',
        'error_code': 'NETWORK_ERROR',
        'details': e.toString(),
      };
    }
  }

  /// Handle OTP-specific errors and return user-friendly responses
  Map<String, dynamic> _handleOtpError(DioException e, String endpoint) {
    String message = 'Failed to process request. Please try again.';
    String errorCode = 'UNKNOWN_ERROR';

    if (e.response != null) {
      // Server responded with an error
      final statusCode = e.response!.statusCode;
      final responseData = e.response!.data;

      if (responseData is Map<String, dynamic>) {
        message = responseData['message']?.toString() ?? message;
        errorCode = responseData['error_code']?.toString() ?? 'SERVER_ERROR';
      }

      // Handle specific status codes
      switch (statusCode) {
        case 400:
          message = 'Invalid request. Please check your input.';
          errorCode = 'BAD_REQUEST';
          break;
        case 401:
          message = 'Invalid OTP. Please try again.';
          errorCode = 'INVALID_OTP';
          break;
        case 429:
          message = 'Too many requests. Please wait before trying again.';
          errorCode = 'RATE_LIMITED';
          break;
        case 500:
        case 502:
        case 503:
        case 504:
          message = 'Server error. Please try again in a moment.';
          errorCode = 'SERVER_ERROR';
          break;
      }
    } else {
      // Network error
      switch (e.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.receiveTimeout:
        case DioExceptionType.sendTimeout:
          message =
              'Request timed out. Please check your connection and try again.';
          errorCode = 'TIMEOUT';
          break;
        case DioExceptionType.connectionError:
          if (e.message?.contains('Connection reset by peer') ?? false) {
            message = 'Connection was reset. Please try again.';
            errorCode = 'CONNECTION_RESET';
          } else {
            message =
                'Unable to connect to server. Please check your internet connection.';
            errorCode = 'CONNECTION_ERROR';
          }
          break;
        default:
          message = 'Network error occurred. Please try again.';
          errorCode = 'NETWORK_ERROR';
      }
    }

    return {
      'success': false,
      'message': message,
      'error_code': errorCode,
      'status_code': e.response?.statusCode,
    };
  }

  /// Handle the Dio response
  dynamic _handleResponse(Response response) {
    // COMPREHENSIVE RESPONSE DEBUGGING - SHOW ALL RESPONSE DATA
    if (kDebugMode) {
      // Determine if this is the destination locations endpoint
      final isDestinationLocationsEndpoint =
          response.requestOptions.path.contains('getdestinationlocations');

      if (isDestinationLocationsEndpoint) {
        debugPrint(
            '\n🌐 === SERVER RESPONSE DEBUG FOR /user/getdestinationlocations ===');
      } else {
        debugPrint('\n📥 === COMPLETE API RESPONSE DEBUG ===');
      }

      // SERVER RESPONSE METADATA
      debugPrint('🌐 === SERVER RESPONSE METADATA ===');
      debugPrint('📊 HTTP STATUS CODE: ${response.statusCode}');
      debugPrint('📊 HTTP STATUS MESSAGE: ${response.statusMessage}');
      debugPrint('🌐 REQUEST URL: ${response.requestOptions.uri}');
      debugPrint('🌐 REQUEST METHOD: ${response.requestOptions.method}');
      debugPrint(
          '⏱️ RESPONSE TIME: ${response.extra['response_time'] ?? 'N/A'}');

      // RESPONSE HEADERS ANALYSIS
      debugPrint('\n📦 === RESPONSE HEADERS ANALYSIS ===');
      if (response.headers.map.isNotEmpty) {
        response.headers.forEach((key, values) {
          debugPrint('📦 HEADER: $key = ${values.join(', ')}');
        });

        // Analyze important headers
        final contentType = response.headers.value('content-type');
        final contentLength = response.headers.value('content-length');
        final server = response.headers.value('server');

        if (contentType != null) debugPrint('📦 CONTENT TYPE: $contentType');
        if (contentLength != null) {
          debugPrint('📦 CONTENT LENGTH: $contentLength bytes');
        }
        if (server != null) debugPrint('📦 SERVER: $server');
      } else {
        debugPrint('📦 NO RESPONSE HEADERS RECEIVED');
      }

      // RESPONSE DATA SIZE AND FORMAT
      debugPrint('\n📊 === RESPONSE DATA ANALYSIS ===');
      debugPrint('📊 RESPONSE DATA TYPE: ${response.data.runtimeType}');
      debugPrint('📊 RESPONSE IS NULL: ${response.data == null}');

      if (response.data != null) {
        try {
          final dataString = response.data.toString();
          debugPrint('📊 RESPONSE DATA SIZE: ${dataString.length} characters');

          if (response.data is Map) {
            final mapData = response.data as Map;
            debugPrint('📊 MAP KEYS COUNT: ${mapData.keys.length}');
            debugPrint('📊 MAP KEYS: ${mapData.keys.toList()}');
          } else if (response.data is List) {
            final listData = response.data as List;
            debugPrint('📊 ARRAY LENGTH: ${listData.length}');
          }
        } catch (e) {
          debugPrint('📊 ERROR ANALYZING DATA SIZE: $e');
        }
      }

      // ERROR AND STATUS INDICATORS
      if (response.data is Map) {
        final mapData = response.data as Map;
        debugPrint('\n⚠️ === STATUS AND ERROR INDICATORS ===');

        // Check for common status fields
        final statusFields = [
          'success',
          'status',
          'error',
          'message',
          'code',
          'errors'
        ];
        for (final field in statusFields) {
          if (mapData.containsKey(field)) {
            debugPrint('⚠️ $field: ${mapData[field]}');
          }
        }
      }

      if (isDestinationLocationsEndpoint) {
        debugPrint(
            '🌐 === END SERVER RESPONSE DEBUG FOR /user/getdestinationlocations ===\n');
      } else {
        debugPrint('📥 === END API RESPONSE DEBUG ===\n');
      }
    }

    if (response.statusCode != null &&
        response.statusCode! >= 200 &&
        response.statusCode! < 300) {
      return response.data;
    } else {
      debugPrint('API Error: ${response.statusCode} - ${response.data}');
      throw ApiException('API Error: ${response.statusCode}');
    }
  }

  /// Handle Dio errors and return a user-friendly error message
  String _handleDioError(DioException e, String endpoint) {
    String errorMessage = 'Network error occurred';

    if (e.response != null) {
      errorMessage = 'API Error: ${e.response?.statusCode}';
      if (e.response?.data is Map && e.response?.data['message'] != null) {
        errorMessage = e.response?.data['message']?.toString() ?? errorMessage;
      } else if (e.response?.data != null) {
        errorMessage = e.response?.data.toString() ?? errorMessage;
      }
      debugPrint('API Error Response: ${e.response?.data}');
    } else {
      if (e.type == DioExceptionType.connectionTimeout ||
          e.type == DioExceptionType.receiveTimeout ||
          e.type == DioExceptionType.sendTimeout) {
        errorMessage = 'Connection timed out. Please try again.';
      } else if (e.type == DioExceptionType.connectionError) {
        if (e.message?.contains('Connection reset by peer') ?? false) {
          errorMessage = 'Connection was reset. Please try again.';
        } else {
          errorMessage =
              'Failed to connect to the server. Please check your network connection.';
        }
      } else {
        errorMessage = 'Network error: ${e.message}';
      }
      debugPrint('Dio Error without response: ${e.message}');
    }

    _logErrorWithDeviceInfo(e, endpoint, errorMessage);
    return errorMessage;
  }

  /// Log error with device info for debugging
  void _logErrorWithDeviceInfo(
      DioException e, String endpoint, String errorMessage) {
    if (_deviceInfoLoaded) {
      debugPrint('\n=== API ERROR WITH DEVICE INFO ===');
      debugPrint('Endpoint: $endpoint');
      debugPrint('Error: $errorMessage');
      debugPrint(
          'Device: ${_deviceInfo['model']} (${_deviceInfo['manufacturer']})');
      debugPrint('OS: ${_deviceInfo['version']} (SDK: ${_deviceInfo['sdk']})');
      debugPrint('App Version: $_appVersion');
      debugPrint('Error Type: ${e.type}');
      debugPrint('================================\n');
    } else {
      debugPrint('\n=== API ERROR ===');
      debugPrint('Endpoint: $endpoint');
      debugPrint('Error: $errorMessage');
      debugPrint('Error Type: ${e.type}');
      debugPrint('================\n');
    }
  }

  /// Get error code from DioException
  String? _getErrorCode(DioException e) {
    if (e.response?.data is Map && e.response?.data['error_code'] != null) {
      return e.response?.data['error_code'];
    }

    if (e.type == DioExceptionType.connectionTimeout ||
        e.type == DioExceptionType.receiveTimeout ||
        e.type == DioExceptionType.sendTimeout) {
      return 'TIMEOUT';
    } else if (e.type == DioExceptionType.connectionError) {
      if (e.message?.contains('Connection reset by peer') ?? false) {
        return 'CONNECTION_RESET';
      }
      return 'CONNECTION_ERROR';
    }

    return null;
  }

  /// Get token using AuthManager for consistency
  Future<String?> _getToken() async {
    try {
      // Use AuthManager for consistent token retrieval
      final authManager = AuthManager();
      final token = await authManager.getToken();

      if (kDebugMode && token != null) {
        debugPrint('Fetched token from AuthManager (token value hidden)');
      }

      return token;
    } catch (e) {
      debugPrint('Error getting token from AuthManager: $e');
      // Fallback to direct SharedPreferences access
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString('auth_token');
    }
  }

  /// Get token (public method for other services)
  Future<String?> getToken() async {
    return _getToken();
  }

  /// Save token using consistent storage mechanism
  Future<void> saveToken(String token) async {
    try {
      // Save directly to SharedPreferences for consistency with AuthManager
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('auth_token', token);
      debugPrint('Token saved successfully via API service');
    } catch (e) {
      debugPrint('Error saving token: $e');
      rethrow;
    }
  }

  /// Clear token using AuthManager for consistency
  Future<void> clearToken() async {
    try {
      // Use AuthManager for consistent token clearing
      final authManager = AuthManager();
      await authManager.logout();
      debugPrint('Token cleared via AuthManager');
    } catch (e) {
      debugPrint('Error clearing token via AuthManager: $e');
      // Fallback to direct SharedPreferences access
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('auth_token');
    }
  }

  /// Check if user is logged in using AuthManager
  Future<bool> isLoggedIn() async {
    try {
      final authManager = AuthManager();
      return await authManager.isLoggedIn();
    } catch (e) {
      debugPrint('Error checking login status via AuthManager: $e');
      // Fallback to token check
      final token = await _getToken();
      return token != null && token.isNotEmpty;
    }
  }

  /// Save the last phone number used for login
  Future<void> saveLastPhoneNumber(String phoneNumber) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('last_phone_number', phoneNumber);
  }

  /// Get the last phone number used for login
  Future<String?> getLastPhoneNumber() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('last_phone_number');
  }

  /// Save user data to SharedPreferences
  Future<void> saveUserData(Map<String, dynamic> userData) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('user_data', jsonEncode(userData));
  }

  /// Get user data from SharedPreferences
  Future<Map<String, dynamic>?> getUserData() async {
    final prefs = await SharedPreferences.getInstance();
    final userDataString = prefs.getString('user_data');

    if (userDataString != null) {
      return jsonDecode(userDataString) as Map<String, dynamic>;
    }

    return null;
  }

  /// Check if the user is new (needs to complete profile)
  Future<bool> isNewUser() async {
    final userData = await getUserData();

    if (userData == null) return false;

    final name = userData['name'] as String?;
    final email = userData['email'] as String?;

    return name == null || name.isEmpty || email == null || email.isEmpty;
  }

  // Method to get current user
  Future<Map<String, dynamic>> getCurrentUser() async {
    try {
      final responseData = await get(ApiConfig.userProfile);
      return responseData;
    } catch (e) {
      debugPrint('Error fetching user profile: $e');
      throw ApiException('Failed to fetch user profile');
    }
  }

  // Method for user sign in
  Future<Map<String, dynamic>> signIn(String email, String password) async {
    try {
      final responseData = await publicPost(
        ApiConfig.login,
        {
          'email': email,
          'password': password,
        },
      );
      // Save token if present in response
      if (responseData is Map<String, dynamic> &&
          responseData.containsKey('token')) {
        await saveToken(responseData['token']);
      }
      return responseData;
    } catch (e) {
      debugPrint('Error during sign in: $e');
      throw ApiException('Sign in failed: $e');
    }
  }

  // Method for user sign up
  Future<Map<String, dynamic>> signUp(Map<String, dynamic> userData) async {
    try {
      final responseData = await publicPost(
        ApiConfig.register,
        userData,
      );
      return responseData;
    } catch (e) {
      debugPrint('Error during sign up: $e');
      throw ApiException('Sign up failed: $e');
    }
  }

  // Helper methods removed to fix duplication

  // Method removed to fix duplication

  /// Fetch station markers
  Future<List<Map<String, dynamic>>> fetchStationMarkers() async {
    try {
      if (kDebugMode) {
        debugPrint(
            'Fetching station markers with full URL: ${ApiConfig.apiUrl}${ApiConfig.markers}');
      }

      // Use the base 'get' method which includes retry logic with full URL
      final responseData = await get(ApiConfig.markers);

      if (responseData is Map<String, dynamic> &&
          responseData.containsKey('data') &&
          responseData['data'] is List) {
        final List<dynamic> stationList = responseData['data'];

        // Convert the API response to the format expected by the MapWidget
        final markers = stationList
            .map<Map<String, dynamic>?>((station) {
              // Helper function to clean URL strings
              String? cleanUrl(String? url) {
                if (url == null) return null;
                // Remove potential backticks and trim whitespace
                return url.replaceAll('`', '').trim();
              }

              // Determine status based on the marker URL
              String status = 'Available'; // Default status
              String? rawMapPinUrl = station['map_pin_url'];
              String? rawFocusedMapPinUrl = station['focused_map_pin_url'];

              String? mapPinUrl = cleanUrl(rawMapPinUrl);
              String? focusedMapPinUrl = cleanUrl(rawFocusedMapPinUrl);

              // CRITICAL: No fallback URLs - use only real API data
              // If API doesn't provide map pin URLs, skip this station
              if (mapPinUrl == null || mapPinUrl.isEmpty) {
                if (kDebugMode) {
                  debugPrint(
                      'Skipping station ${station['station_id']} - no map pin URL from API');
                }
                return null; // Skip this station instead of using fallback
              } else {
                // Determine status ONLY from a valid, non-fallback URL
                // Make checks case-insensitive and handle different paths
                final lowerCaseUrl = mapPinUrl.toLowerCase();
                if (lowerCaseUrl.contains('unavailable')) {
                  status = 'Unavailable';
                } else if (lowerCaseUrl.contains('charging')) {
                  status = 'In Use';
                }
                // Add more status checks if needed based on URL patterns
              }

              if (focusedMapPinUrl == null || focusedMapPinUrl.isEmpty) {
                if (kDebugMode) {
                  debugPrint(
                      'Skipping station ${station['station_id']} - no focused map pin URL from API');
                }
                return null; // Skip this station instead of using fallback
              }

              // CRITICAL: Only use real API data - no fallbacks or defaults
              // Skip stations with missing required data instead of using fallbacks
              if (station['station_name'] == null ||
                  station['latitude'] == null ||
                  station['longitude'] == null) {
                debugPrint(
                    'Skipping station with missing required data: ${station['station_id']}');
                return null; // Skip this station
              }

              final double? latitude =
                  double.tryParse(station['latitude']?.toString() ?? '');
              final double? longitude =
                  double.tryParse(station['longitude']?.toString() ?? '');

              if (latitude == null || longitude == null) {
                debugPrint(
                    'Skipping station with invalid coordinates: ${station['station_id']}');
                return null; // Skip this station
              }

              return {
                'id': station['station_id'].toString(),
                'name': station['station_name'], // Real name from API only
                'latitude': latitude, // Real coordinates from API only
                'longitude': longitude, // Real coordinates from API only
                'mapPinUrl': mapPinUrl,
                'focusedMapPinUrl': focusedMapPinUrl,
                'address': station[
                    'address'], // Real address from API only - null if not provided
                'distance': 0.0, // Distance calculated elsewhere
                'availability': status,
                'status': status, // Real status from API
                'connectorType': station[
                    'connector_type'], // Real connector type from API only - null if not provided
                'freeGuns': station[
                    'free_guns'], // Real free guns from API only - null if not provided
              };
            })
            .where((marker) => marker != null)
            .cast<Map<String, dynamic>>()
            .toList();

        return markers;
      } else {
        // Throw an exception if the format is invalid, handled by the catch block
        throw ApiException(
            'Invalid response format received for station markers.');
      }
    } catch (e) {
      // The base 'get' method handles retries and throws ApiException on failure
      // Propagate the error or return empty list based on desired behavior
      // Returning empty list might hide underlying API issues
      // Consider rethrowing or handling specific exceptions differently
      // For now, return empty list to match previous behavior on final failure
      return [];
    }
  }

  /// Fetch nearest stations
  Future<NearestStationResponse> fetchNearestStations(
      double latitude, double longitude,
      {double radius = 10.0}) async {
    try {
      final responseData = await get(
        ApiConfig.nearestStations,
        queryParams: {
          'lat': latitude.toString(), // Corrected parameter name
          'lng': longitude.toString(), // Corrected parameter name
          'radius': radius.toString(),
        },
      );
      // Ensure responseData is a Map before parsing
      if (responseData is Map<String, dynamic>) {
        final response = NearestStationResponse.fromJson(responseData);
        if (kDebugMode) {
          debugPrint('Fetched ${response.data?.length ?? 0} nearest stations');
        }
        return response;
      } else {
        throw ApiException(
            'Invalid response format received for nearest stations.',
            code: 'INVALID_RESPONSE_FORMAT');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Nearest stations request timed out');
        debugPrint('Error fetching nearest stations: $e');
      }
      throw ApiException(
        'Failed to fetch nearest stations. Please check your internet connection and try again.',
        code: 'SERVER_ERROR',
      );
    }
  }

  /// Direct method to fetch nearest stations using the exact URL format
  /// This uses the URL format: https://api2.eeil.online/api/v1/user/stations/nearest?lat=27.608162&lng=76.613853
  Future<NearestStationResponse> fetchNearestStationsDirectUrl(
      double latitude, double longitude,
      {double radius = 50.0}) async {
    debugPrint('=== FETCHING NEAREST STATIONS WITH DIRECT URL ===');
    debugPrint('Coordinates: Lat $latitude, Lng $longitude, Radius $radius km');

    try {
      // Try the direct URL format first
      final String directUrl =
          '${ApiConfig.apiUrl}${ApiConfig.nearestStations}';
      debugPrint('Using URL: $directUrl with lat/lng parameters');

      // Use the correct parameter names (lat/lng) as shown in the URL
      final responseData = await get(
        directUrl,
        queryParams: {
          'lat': latitude.toString(),
          'lng': longitude.toString(),
          'radius': radius.toString(),
        },
      ).timeout(
          const Duration(seconds: 8)); // Reduced timeout for faster response

      // Ensure responseData is a Map before parsing
      if (responseData is Map<String, dynamic>) {
        final response = NearestStationResponse.fromJson(responseData);

        debugPrint(
            'API Response: success=${response.success}, message=${response.message}');
        debugPrint('Fetched ${response.data?.length ?? 0} nearest stations');

        if (response.data == null || response.data!.isEmpty) {
          throw ApiException('No stations found in the specified area',
              code: 'NO_STATIONS_FOUND');
        }

        return response;
      } else {
        throw ApiException(
            'Invalid response format received for nearest stations.',
            code: 'INVALID_RESPONSE_FORMAT');
      }
    } catch (e) {
      debugPrint('Error fetching nearest stations with direct URL: $e');
      throw ApiException(
        'Failed to fetch nearest stations. Please check your internet connection and try again.',
        code: 'SERVER_ERROR',
      );
    }
  }

  /// Search stations by name
  Future<StationSearchResponse> searchStationsByName(String query) async {
    try {
      if (query.trim().isEmpty) {
        throw ApiException('Search query cannot be empty');
      }

      final responseData = await get(
        ApiConfig.stationSearch,
        queryParams: {'search': query},
      );

      // Ensure responseData is a Map before parsing
      if (responseData is Map<String, dynamic>) {
        final response = StationSearchResponse.fromJson(responseData);
        if (kDebugMode) {
          debugPrint(
              'Search returned ${response.data?.length ?? 0} results for query "$query"');
        }
        return response;
      } else {
        throw ApiException(
            'Invalid response format received for station search.',
            code: 'INVALID_RESPONSE_FORMAT');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error searching stations: $e');
      }

      // Return empty success response instead of throwing an error
      return StationSearchResponse(
        success: true,
        message: 'No results found for "$query"',
        data: [], // Empty list is fine for search results
      );
    }
  }

  /// Fetch paginated stations
  Future<PaginatedStationResponse> fetchPaginatedStations(
      {int page = 1, int limit = 20}) async {
    try {
      final responseData = await get(
        ApiConfig.stationPaginate,
        queryParams: {
          'page': page.toString(),
          'limit': limit.toString(),
        },
      );
      // Ensure responseData is a Map before parsing
      if (responseData is Map<String, dynamic>) {
        return PaginatedStationResponse.fromJson(responseData);
      } else {
        throw ApiException(
            'Invalid response format received for paginated stations.',
            code: 'INVALID_RESPONSE_FORMAT');
      }
    } catch (e) {
      debugPrint('Error fetching paginated stations: $e');

      // No fallback data - propagate the error to ensure proper error handling
      throw ApiException('Failed to fetch paginated stations: $e',
          code: 'PAGINATED_STATIONS_ERROR');
    }
  }

  /// Fetch station details by UID
  Future<Map<String, dynamic>> fetchStationDetails(String uid) async {
    try {
      // Validate UID parameter
      if (uid.trim().isEmpty) {
        debugPrint('ERROR: Empty UID provided to fetchStationDetails');
        throw ApiException('Cannot fetch station details: UID is empty',
            code: 'EMPTY_UID');
      }

      // Let the API validate the UID format
      debugPrint('Using UID for API call: "$uid"');

      debugPrint('Fetching details for station UID: $uid');

      final responseData = await get(
        ApiConfig.stationDetails, // FIXED: Use plural 'stations' endpoint
        queryParams: {'uid': uid},
      );

      // Ensure responseData is a Map before returning
      if (responseData is Map<String, dynamic>) {
        debugPrint('Successfully fetched details for station UID: $uid');

        // Log the response data for debugging
        if (kDebugMode) {
          debugPrint('Station details response: $responseData');
        }

        return responseData;
      } else {
        throw ApiException(
            'Invalid response format received for station details.',
            code: 'INVALID_RESPONSE_FORMAT');
      }
    } catch (e) {
      debugPrint('Error fetching station details: $e');
      // No fallback - propagate the error to ensure callers handle it properly
      throw ApiException('Failed to fetch station details: $e',
          code: 'STATION_DETAILS_ERROR');
    }
  }

  /// Update GST information
  Future<Map<String, dynamic>> updateGstInfo({
    required String gstin,
    required String businessName,
  }) async {
    try {
      debugPrint('\n=== UPDATING GST INFORMATION ===');
      debugPrint('GSTIN: $gstin');
      debugPrint('Business Name: $businessName');

      final payload = {
        'gstin': gstin,
        'business_name': businessName,
      };

      final responseData = await post(ApiConfig.updateGstin, data: payload);

      // Ensure responseData is a Map before returning
      if (responseData is Map<String, dynamic>) {
        debugPrint('Successfully updated GST information');
        return responseData;
      } else {
        throw ApiException(
          'Invalid response format received for GST update.',
          code: 'INVALID_RESPONSE_FORMAT',
        );
      }
    } catch (e) {
      debugPrint('Error updating GST information: $e');

      if (e is DioException) {
        if (e.type == DioExceptionType.connectionTimeout ||
            e.type == DioExceptionType.receiveTimeout ||
            e.type == DioExceptionType.sendTimeout) {
          throw ApiException(
            'Connection timed out. Please check your internet connection and try again.',
            code: 'CONNECTION_TIMEOUT',
          );
        } else if (e.response?.statusCode == 401) {
          throw ApiException(
            'Unauthorized access. Please log in again.',
            code: 'UNAUTHORIZED',
          );
        } else if (e.response?.statusCode == 400) {
          throw ApiException(
            'Invalid GST information provided. Please check your details.',
            code: 'INVALID_GST_DATA',
          );
        }
      }

      throw ApiException(
        'Failed to update GST information. Please try again.',
        code: 'UPDATE_GST_ERROR',
      );
    }
  }

  /// Fetch wallet balance
  Future<Map<String, dynamic>> fetchWalletBalance() async {
    try {
      debugPrint('Fetching wallet balance');

      final responseData = await get(ApiConfig.walletInfo);

      // Ensure responseData is a Map before returning
      if (responseData is Map<String, dynamic>) {
        debugPrint('Successfully fetched wallet balance');
        return responseData;
      } else {
        throw ApiException(
          'Invalid response format received for wallet balance.',
          code: 'INVALID_RESPONSE_FORMAT',
        );
      }
    } catch (e) {
      debugPrint('Error fetching wallet balance: $e');

      if (e is DioException) {
        if (e.type == DioExceptionType.connectionTimeout ||
            e.type == DioExceptionType.receiveTimeout ||
            e.type == DioExceptionType.sendTimeout) {
          throw ApiException(
            'Connection timed out. Please check your internet connection and try again.',
            code: 'CONNECTION_TIMEOUT',
          );
        } else if (e.response?.statusCode == 401) {
          throw ApiException(
            'Unauthorized access. Please log in again.',
            code: 'UNAUTHORIZED',
          );
        } else if (e.response?.statusCode == 404) {
          throw ApiException(
            'Wallet not found. Please contact support.',
            code: 'WALLET_NOT_FOUND',
          );
        }
      }

      throw ApiException(
        'Failed to fetch wallet balance. Please try again.',
        code: 'FETCH_WALLET_ERROR',
      );
    }
  }

  /// Add RFID card
  Future<Map<String, dynamic>> addRfidCard(String rfidCode) async {
    try {
      debugPrint('🏷️ Adding RFID card: $rfidCode');
      debugPrint('🌐 API Endpoint: ${ApiConfig.addRfid}');
      debugPrint(
          '🌐 Full URL will be: ${ApiConfig.apiUrl}${ApiConfig.addRfid}');

      final payload = {
        'rfid_code': rfidCode,
      };

      debugPrint('📦 Request payload: $payload');
      final responseData = await post(ApiConfig.addRfid, data: payload);

      // Ensure responseData is a Map before returning
      if (responseData is Map<String, dynamic>) {
        debugPrint('Successfully added RFID card');
        return responseData;
      } else {
        throw ApiException(
          'Invalid response format received for RFID add.',
          code: 'INVALID_RESPONSE_FORMAT',
        );
      }
    } catch (e) {
      debugPrint('Error adding RFID card: $e');

      if (e is DioException) {
        if (e.type == DioExceptionType.connectionTimeout ||
            e.type == DioExceptionType.receiveTimeout ||
            e.type == DioExceptionType.sendTimeout) {
          throw ApiException(
            'Connection timed out. Please check your internet connection and try again.',
            code: 'CONNECTION_TIMEOUT',
          );
        } else if (e.response?.statusCode == 401) {
          throw ApiException(
            'Unauthorized access. Please log in again.',
            code: 'UNAUTHORIZED',
          );
        } else if (e.response?.statusCode == 400) {
          throw ApiException(
            'Invalid RFID code provided. Please check your RFID number.',
            code: 'INVALID_RFID_CODE',
          );
        }
      }

      throw ApiException(
        'Failed to add RFID card. Please try again.',
        code: 'ADD_RFID_ERROR',
      );
    }
  }

  /// Delete RFID card
  Future<Map<String, dynamic>> deleteRfidCard(String rfidCode) async {
    try {
      debugPrint('🗑️ Deleting RFID card: $rfidCode');
      debugPrint('🌐 API Endpoint: ${ApiConfig.deleteRfid}');
      debugPrint(
          '🌐 Full URL will be: ${ApiConfig.apiUrl}${ApiConfig.deleteRfid}');

      final payload = {
        'rfid_code': rfidCode,
      };

      debugPrint('📦 Request payload: $payload');
      final responseData = await post(ApiConfig.deleteRfid, data: payload);

      // Ensure responseData is a Map before returning
      if (responseData is Map<String, dynamic>) {
        debugPrint('Successfully deleted RFID card');
        return responseData;
      } else {
        throw ApiException(
          'Invalid response format received for RFID delete.',
          code: 'INVALID_RESPONSE_FORMAT',
        );
      }
    } catch (e) {
      debugPrint('Error deleting RFID card: $e');

      if (e is DioException) {
        if (e.type == DioExceptionType.connectionTimeout ||
            e.type == DioExceptionType.receiveTimeout ||
            e.type == DioExceptionType.sendTimeout) {
          throw ApiException(
            'Connection timed out. Please check your internet connection and try again.',
            code: 'CONNECTION_TIMEOUT',
          );
        } else if (e.response?.statusCode == 401) {
          throw ApiException(
            'Unauthorized access. Please log in again.',
            code: 'UNAUTHORIZED',
          );
        } else if (e.response?.statusCode == 404) {
          throw ApiException(
            'RFID card not found.',
            code: 'RFID_NOT_FOUND',
          );
        }
      }

      throw ApiException(
        'Failed to delete RFID card. Please try again.',
        code: 'DELETE_RFID_ERROR',
      );
    }
  }

  /// Initialize RFID order
  Future<Map<String, dynamic>> initializeRfidOrder(
      Map<String, dynamic> orderData) async {
    try {
      debugPrint('📋 Initializing RFID order');
      debugPrint('🌐 API Endpoint: ${ApiConfig.rfidOrderInit}');
      debugPrint(
          '🌐 Full URL will be: ${ApiConfig.apiUrl}${ApiConfig.rfidOrderInit}');
      debugPrint('📦 Order data: $orderData');

      final responseData = await post(ApiConfig.rfidOrderInit, data: orderData);

      // Ensure responseData is a Map before returning
      if (responseData is Map<String, dynamic>) {
        debugPrint('Successfully initialized RFID order');
        return responseData;
      } else {
        throw ApiException(
          'Invalid response format received for RFID order initialization.',
          code: 'INVALID_RESPONSE_FORMAT',
        );
      }
    } catch (e) {
      debugPrint('Error initializing RFID order: $e');

      if (e is DioException) {
        if (e.type == DioExceptionType.connectionTimeout ||
            e.type == DioExceptionType.receiveTimeout ||
            e.type == DioExceptionType.sendTimeout) {
          throw ApiException(
            'Connection timed out. Please check your internet connection and try again.',
            code: 'CONNECTION_TIMEOUT',
          );
        } else if (e.response?.statusCode == 401) {
          throw ApiException(
            'Unauthorized access. Please log in again.',
            code: 'UNAUTHORIZED',
          );
        } else if (e.response?.statusCode == 400) {
          throw ApiException(
            'Invalid order data provided. Please check your details.',
            code: 'INVALID_ORDER_DATA',
          );
        }
      }

      throw ApiException(
        'Failed to initialize RFID order. Please try again.',
        code: 'RFID_ORDER_ERROR',
      );
    }
  }

  /// Handle PhonePe payment response
  Future<Map<String, dynamic>> handlePhonePeResponse(
      Map<String, dynamic> responseData) async {
    try {
      debugPrint('💳 Handling PhonePe payment response');
      debugPrint('🌐 API Endpoint: ${ApiConfig.paymentResponsePhonePe}');
      debugPrint(
          '🌐 Full URL will be: ${ApiConfig.apiUrl}${ApiConfig.paymentResponsePhonePe}');
      debugPrint('📦 Response data: $responseData');

      final apiResponse =
          await post(ApiConfig.paymentResponsePhonePe, data: responseData);

      // Ensure responseData is a Map before returning
      if (apiResponse is Map<String, dynamic>) {
        debugPrint('Successfully processed PhonePe payment response');
        return apiResponse;
      } else {
        throw ApiException(
          'Invalid response format received for PhonePe payment response.',
          code: 'INVALID_RESPONSE_FORMAT',
        );
      }
    } catch (e) {
      debugPrint('Error handling PhonePe payment response: $e');

      if (e is DioException) {
        if (e.type == DioExceptionType.connectionTimeout ||
            e.type == DioExceptionType.receiveTimeout ||
            e.type == DioExceptionType.sendTimeout) {
          throw ApiException(
            'Connection timed out. Please check your internet connection and try again.',
            code: 'CONNECTION_TIMEOUT',
          );
        } else if (e.response?.statusCode == 401) {
          throw ApiException(
            'Unauthorized access. Please log in again.',
            code: 'UNAUTHORIZED',
          );
        } else if (e.response?.statusCode == 400) {
          throw ApiException(
            'Invalid payment response data.',
            code: 'INVALID_PAYMENT_DATA',
          );
        }
      }

      throw ApiException(
        'Failed to process payment response. Please try again.',
        code: 'PAYMENT_RESPONSE_ERROR',
      );
    }
  }

  /// Enhanced PayU payment response handler with comprehensive validation and error handling
  Future<Map<String, dynamic>> handlePayUResponse(
      Map<String, dynamic> responseData) async {

    final startTime = DateTime.now();
    debugPrint('💳 PAYU API: ========== ENHANCED PAYU RESPONSE HANDLER START ==========');
    debugPrint('💳 PAYU API: Timestamp: ${startTime.toIso8601String()}');
    debugPrint('💳 PAYU API: Endpoint: ${ApiConfig.payuResponse}');
    debugPrint('💳 PAYU API: Full URL: ${ApiConfig.apiUrl}${ApiConfig.payuResponse}');

    try {
      // Validate input data
      _validatePayUResponseData(responseData);

      // Log sanitized request data (remove sensitive info)
      final sanitizedData = _sanitizePayUData(responseData);
      debugPrint('💳 PAYU API: Sanitized request: $sanitizedData');

      // Make API call with enhanced error handling
      final apiResponse = await _makePayUApiCall(responseData);

      // Process and validate response
      final processedResponse = _processPayUApiResponse(apiResponse);

      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);
      debugPrint('💳 PAYU API: Request completed in ${duration.inMilliseconds}ms');
      debugPrint('💳 PAYU API: ========== ENHANCED PAYU RESPONSE HANDLER END ==========');

      return processedResponse;

    } catch (e) {
      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);
      debugPrint('❌ PAYU API: Request failed after ${duration.inMilliseconds}ms');
      debugPrint('❌ PAYU API: Error: $e');

      // Enhanced error handling with specific error types
      throw _handlePayUApiError(e, responseData);
    }
  }

  /// Validate PayU response data before sending to backend
  void _validatePayUResponseData(Map<String, dynamic> data) {
    debugPrint('🔍 PAYU API: Validating request data...');

    // Check required fields
    final requiredFields = ['status', 'txnid'];
    final missingFields = <String>[];

    for (final field in requiredFields) {
      if (!data.containsKey(field) || data[field] == null || data[field].toString().isEmpty) {
        missingFields.add(field);
      }
    }

    if (missingFields.isNotEmpty) {
      throw ApiException(
        'Missing required fields: ${missingFields.join(', ')}',
        code: 'MISSING_REQUIRED_FIELDS',
      );
    }

    // Validate transaction ID format
    final txnId = data['txnid'].toString();
    if (txnId.length < 5) {
      throw ApiException(
        'Invalid transaction ID format',
        code: 'INVALID_TRANSACTION_ID',
      );
    }

    // Validate status value
    final status = data['status'].toString().toLowerCase();
    final validStatuses = ['success', 'failure', 'pending', 'cancelled', 'timeout', 'unknown'];
    if (!validStatuses.contains(status)) {
      debugPrint('⚠️ PAYU API: Unknown status "$status", but proceeding...');
    }

    debugPrint('✅ PAYU API: Request data validation passed');
  }

  /// Sanitize PayU data for logging (remove sensitive information)
  Map<String, dynamic> _sanitizePayUData(Map<String, dynamic> data) {
    final sanitized = Map<String, dynamic>.from(data);

    // Remove or mask sensitive fields
    if (sanitized.containsKey('hash')) {
      sanitized['hash'] = sanitized['hash'].toString().isNotEmpty ? '[HASH_PRESENT]' : '[NO_HASH]';
    }

    // Sanitize response data if present
    if (sanitized['response'] is Map<String, dynamic>) {
      final responseData = Map<String, dynamic>.from(sanitized['response']);

      // Mask sensitive fields in response
      final sensitiveFields = ['cardnum', 'email', 'phone', 'bank_ref_num'];
      for (final field in sensitiveFields) {
        if (responseData.containsKey(field) && responseData[field] != null) {
          final value = responseData[field].toString();
          responseData[field] = value.length > 4 ? '${value.substring(0, 4)}***' : '***';
        }
      }

      sanitized['response'] = responseData;
    }

    return sanitized;
  }

  /// Make PayU API call with enhanced error handling and retries
  Future<dynamic> _makePayUApiCall(Map<String, dynamic> data) async {
    const maxRetries = 3;
    const baseDelay = Duration(seconds: 1);

    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        debugPrint('💳 PAYU API: Making API call (attempt $attempt/$maxRetries)');

        final response = await post(ApiConfig.payuResponse, data: data);

        debugPrint('✅ PAYU API: API call successful on attempt $attempt');
        return response;

      } catch (e) {
        debugPrint('❌ PAYU API: Attempt $attempt failed: $e');

        if (attempt == maxRetries) {
          debugPrint('❌ PAYU API: All retry attempts exhausted');
          rethrow;
        }

        // Exponential backoff
        final delay = Duration(milliseconds: baseDelay.inMilliseconds * (attempt * attempt));
        debugPrint('🔄 PAYU API: Retrying in ${delay.inMilliseconds}ms...');
        await Future.delayed(delay);
      }
    }

    throw ApiException('All retry attempts failed', code: 'MAX_RETRIES_EXCEEDED');
  }

  /// Process and validate PayU API response
  Map<String, dynamic> _processPayUApiResponse(dynamic apiResponse) {
    debugPrint('🔍 PAYU API: Processing API response...');
    debugPrint('🔍 PAYU API: Response type: ${apiResponse.runtimeType}');

    // Handle Map responses (JSON)
    if (apiResponse is Map<String, dynamic>) {
      debugPrint('✅ PAYU API: JSON response received');

      // Validate JSON response structure
      if (apiResponse.containsKey('error') && apiResponse['error'] == true) {
        throw ApiException(
          apiResponse['message'] ?? 'Backend reported an error',
          code: 'BACKEND_ERROR',
        );
      }

      return {
        'success': true,
        'message': apiResponse['message'] ?? 'Payment response processed',
        'data': apiResponse,
        'response_type': 'json',
        'processed_at': DateTime.now().toIso8601String(),
      };
    }

    // Handle String responses
    else if (apiResponse is String) {
      debugPrint('✅ PAYU API: String response received: "$apiResponse"');

      // Validate string response
      if (apiResponse.trim().isEmpty) {
        throw ApiException(
          'Empty response received from server',
          code: 'EMPTY_RESPONSE',
        );
      }

      return {
        'success': true,
        'message': 'Payment status updated',
        'status': apiResponse.trim(),
        'raw_response': apiResponse,
        'response_type': 'string',
        'processed_at': DateTime.now().toIso8601String(),
      };
    }

    // Handle unexpected response types
    else {
      debugPrint('❌ PAYU API: Unexpected response type: ${apiResponse.runtimeType}');
      debugPrint('❌ PAYU API: Response content: $apiResponse');

      throw ApiException(
        'Unexpected response format: ${apiResponse.runtimeType}',
        code: 'INVALID_RESPONSE_FORMAT',
      );
    }
  }

  /// Enhanced error handling for PayU API calls
  ApiException _handlePayUApiError(dynamic error, Map<String, dynamic> requestData) {
    debugPrint('❌ PAYU API: Handling error: $error');
    debugPrint('❌ PAYU API: Error type: ${error.runtimeType}');

    // Handle ApiException (already processed)
    if (error is ApiException) {
      debugPrint('❌ PAYU API: Re-throwing ApiException: ${error.message}');
      return error;
    }

    // Handle DioException
    if (error is DioException) {
      debugPrint('❌ PAYU API: DioException - Type: ${error.type}');
      debugPrint('❌ PAYU API: DioException - Message: ${error.message}');
      debugPrint('❌ PAYU API: DioException - Response: ${error.response?.data}');

      switch (error.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
          return ApiException(
            'Connection timeout. Please check your internet connection and try again.',
            code: 'CONNECTION_TIMEOUT',
          );

        case DioExceptionType.badResponse:
          final statusCode = error.response?.statusCode;
          final responseData = error.response?.data;

          switch (statusCode) {
            case 400:
              return ApiException(
                'Invalid payment data: ${responseData ?? 'Bad request'}',
                code: 'INVALID_PAYMENT_DATA',
              );
            case 401:
              return ApiException(
                'Authentication failed. Please log in again.',
                code: 'UNAUTHORIZED',
              );
            case 404:
              return ApiException(
                'Payment endpoint not found. Please contact support.',
                code: 'ENDPOINT_NOT_FOUND',
              );
            case 500:
              return ApiException(
                'Server error occurred. Please try again later.',
                code: 'SERVER_ERROR',
              );
            default:
              return ApiException(
                'HTTP error $statusCode: ${responseData ?? 'Unknown error'}',
                code: 'HTTP_ERROR_$statusCode',
              );
          }

        case DioExceptionType.cancel:
          return ApiException(
            'Request was cancelled',
            code: 'REQUEST_CANCELLED',
          );

        case DioExceptionType.connectionError:
          return ApiException(
            'Network connection error. Please check your internet connection.',
            code: 'CONNECTION_ERROR',
          );

        default:
          return ApiException(
            'Network error: ${error.message}',
            code: 'NETWORK_ERROR',
          );
      }
    }

    // Handle other exceptions
    debugPrint('❌ PAYU API: Unknown error type: ${error.runtimeType}');
    return ApiException(
      'Unexpected error occurred: ${error.toString()}',
      code: 'UNKNOWN_ERROR',
    );
  }

  /// Handle Cashfree payment response
  Future<Map<String, dynamic>> handleCashfreeResponse(
      Map<String, dynamic> responseData) async {
    try {
      debugPrint('💳 Handling Cashfree payment response');
      debugPrint('🌐 API Endpoint: ${ApiConfig.cashfreeResponse}');
      debugPrint(
          '🌐 Full URL will be: ${ApiConfig.apiUrl}${ApiConfig.cashfreeResponse}');
      debugPrint('📦 Response data: $responseData');

      final apiResponse =
          await post(ApiConfig.cashfreeResponse, data: responseData);

      // Ensure responseData is a Map before returning
      if (apiResponse is Map<String, dynamic>) {
        debugPrint('Successfully processed Cashfree payment response');
        return apiResponse;
      } else {
        throw ApiException(
          'Invalid response format received for Cashfree payment response.',
          code: 'INVALID_RESPONSE_FORMAT',
        );
      }
    } catch (e) {
      debugPrint('Error handling Cashfree payment response: $e');

      if (e is DioException) {
        if (e.type == DioExceptionType.connectionTimeout ||
            e.type == DioExceptionType.receiveTimeout ||
            e.type == DioExceptionType.sendTimeout) {
          throw ApiException(
            'Connection timed out. Please check your internet connection and try again.',
            code: 'CONNECTION_TIMEOUT',
          );
        } else if (e.response?.statusCode == 401) {
          throw ApiException(
            'Unauthorized access. Please log in again.',
            code: 'UNAUTHORIZED',
          );
        } else if (e.response?.statusCode == 400) {
          throw ApiException(
            'Invalid payment response data.',
            code: 'INVALID_PAYMENT_DATA',
          );
        }
      }

      throw ApiException(
        'Failed to process Cashfree payment response. Please try again.',
        code: 'CASHFREE_RESPONSE_ERROR',
      );
    }
  }

  /// Verify API configuration for RFID endpoints
  /// This method can be called to test if the API configuration is correct
  void verifyRfidApiConfiguration() {
    debugPrint('\n🔧 ===== RFID API CONFIGURATION VERIFICATION =====');
    debugPrint('🌐 Base URL: ${ApiConfig.baseUrl}');
    debugPrint('🌐 API Version: ${ApiConfig.apiVersion}');
    debugPrint('🌐 Full API URL: ${ApiConfig.apiUrl}');
    debugPrint('');
    debugPrint('🏷️ RFID Endpoints:');
    debugPrint('   Add RFID: ${ApiConfig.apiUrl}${ApiConfig.addRfid}');
    debugPrint('   Delete RFID: ${ApiConfig.apiUrl}${ApiConfig.deleteRfid}');
    debugPrint('   Order Init: ${ApiConfig.apiUrl}${ApiConfig.rfidOrderInit}');
    debugPrint(
        '   Payment Response: ${ApiConfig.apiUrl}${ApiConfig.paymentResponsePhonePe}');
    debugPrint('');
    debugPrint(
        '🔐 Authentication: ${ApiConfig.publicEndpoints.contains(ApiConfig.addRfid) ? 'NOT REQUIRED' : 'REQUIRED'}');
    debugPrint('⏰ Timeout Settings:');
    debugPrint('   Connection: ${ApiConfig.connectionTimeout.inSeconds}s');
    debugPrint('   Receive: ${ApiConfig.receiveTimeout.inSeconds}s');
    debugPrint('   Send: ${ApiConfig.sendTimeout.inSeconds}s');
    debugPrint('🔧 ===== END VERIFICATION =====\n');
  }
}
