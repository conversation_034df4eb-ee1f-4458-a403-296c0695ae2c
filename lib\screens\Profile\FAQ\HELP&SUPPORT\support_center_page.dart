import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../Privacy_Settings/privacy_settings_page.dart';
import '../faq_page.dart';
import '../../../../utils/app_themes.dart';

class SupportCenterPage extends StatelessWidget {
  const SupportCenterPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const HelpSupportPage();
  }
}

class HelpSupportPage extends StatefulWidget {
  const HelpSupportPage({super.key});

  @override
  State<HelpSupportPage> createState() => _HelpSupportPageState();
}

class _HelpSupportPageState extends State<HelpSupportPage> {
  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: isDarkMode ? const Color(0xFF0E0E0E) : Colors.white,
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          icon: Icon(Icons.arrow_back,
              color: isDarkMode ? Colors.white : Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          "Contact Us",
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: isDarkMode ? Colors.white : Colors.black,
          ),
        ),
      ),
      backgroundColor: isDarkMode ? const Color(0xFF0E0E0E) : Colors.white,
      body: SafeArea(
        child: Column(
          children: [
            // Subheading below the AppBar
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              child: Text(
                "We're here to help! Choose your preferred way to reach our support team.",
                style: TextStyle(
                  fontSize: 15,
                  color:
                      isDarkMode ? Colors.grey.shade300 : Colors.grey.shade700,
                  height: 1.4,
                ),
                textAlign: TextAlign.center,
              ),
            ),

            // Expanded area with contact cards
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: Column(
                  children: [
                    _buildContactCard(
                      context,
                      backgroundColor: const Color(0xFFE7EDFF),
                      icon: Icons.call,
                      iconColor: const Color(0xFF3D7AF5),
                      title: "Call Us",
                      statusText: "Available 24/7",
                      statusColor: const Color(0xFF3D7AF5),
                      subtitle:
                          "Speak directly with our support team for immediate assistance with your charging or account issues.",
                      buttonLabel: "Call Now",
                      buttonColor: const Color(0xFF3D7AF5),
                      onPressed: () async {
                        final Uri phoneUri = Uri(
                          scheme: 'tel',
                          path: '+************',
                        );
                        try {
                          if (await canLaunchUrl(phoneUri)) {
                            await launchUrl(phoneUri);
                          } else {
                            // Handle error without using context after async gap
                            debugPrint('Could not launch phone call');
                            if (context.mounted) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text(
                                      'Unable to make phone call. Please check your device settings.'),
                                  backgroundColor: Colors.red,
                                ),
                              );
                            }
                          }
                        } catch (e) {
                          debugPrint('Error launching phone call: $e');
                          if (context.mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text(
                                    'Error occurred while trying to make phone call.'),
                                backgroundColor: Colors.red,
                              ),
                            );
                          }
                        }
                      },
                    ),

                    _buildContactCard(
                      context,
                      backgroundColor: const Color(0xFFE8FCEB),
                      icon: FontAwesomeIcons.whatsapp,
                      iconColor: Colors.green,
                      title: "Chat on WhatsApp",
                      statusText: "Typical response: < 5 mins",
                      statusColor: Colors.green,
                      subtitle:
                          "Get instant assistance via WhatsApp for quick queries and troubleshooting. Our team is ready to help!",
                      buttonLabel: "Start Chat",
                      buttonColor: Colors.green,
                      onPressed: () async {
                        final Uri whatsappUri = Uri.parse(
                          'https://wa.me/************',
                        );
                        try {
                          if (await canLaunchUrl(whatsappUri)) {
                            await launchUrl(whatsappUri);
                          } else {
                            // Handle error - WhatsApp not installed or URL scheme not supported
                            debugPrint('Could not launch WhatsApp');
                            if (context.mounted) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text(
                                      'WhatsApp is not installed on your device. Please install WhatsApp or contact us via phone call.'),
                                  backgroundColor: Colors.orange,
                                  duration: Duration(seconds: 4),
                                ),
                              );
                            }
                          }
                        } catch (e) {
                          debugPrint('Error launching WhatsApp: $e');
                          if (context.mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text(
                                    'Error occurred while trying to open WhatsApp.'),
                                backgroundColor: Colors.red,
                              ),
                            );
                          }
                        }
                      },
                    ),

                    _buildContactCard(
                      context,
                      backgroundColor: const Color(0xFFFFF2DD),
                      icon: Icons.email_outlined,
                      iconColor: Colors.orange,
                      title: "Email Us",
                      statusText: "Response within 24 hours",
                      statusColor: Colors.orange,
                      subtitle:
                          "Send us a detailed message about your issue, and our support team will get back to you with a solution.",
                      buttonLabel: "Send Email",
                      buttonColor: Colors.orange,
                      onPressed: () async {
                        final Uri emailUri = Uri(
                          scheme: 'mailto',
                          path: '<EMAIL>',
                        );

                        debugPrint(
                            '📧 Attempting to launch email with URI: $emailUri');

                        try {
                          // Add haptic feedback
                          HapticFeedback.lightImpact();

                          // Try to launch the email client
                          final bool canLaunch = await canLaunchUrl(emailUri);
                          debugPrint('📧 Can launch email URI: $canLaunch');

                          if (canLaunch) {
                            final bool launched = await launchUrl(
                              emailUri,
                              mode: LaunchMode.externalApplication,
                            );
                            debugPrint('📧 Email client launched: $launched');

                            if (!launched && context.mounted) {
                              _showErrorSnackBar(context,
                                  'Failed to open email client. Please try again.');
                            }
                          } else {
                            // Handle error without using context after async gap
                            debugPrint(
                                '❌ Could not launch email client - no compatible app found');
                            if (context.mounted) {
                              _showErrorSnackBar(context,
                                  'No email app found. Please install an email client or contact <NAME_EMAIL>');
                            }
                          }
                        } catch (e) {
                          debugPrint('❌ Error launching email client: $e');
                          if (context.mounted) {
                            _showErrorSnackBar(context,
                                'Error opening email client. Please contact <NAME_EMAIL>');
                          }
                        }
                      },
                    ),

                    /*
                    // Live chat card - COMMENTED OUT FOR FUTURE USE
                    // Backend configuration and live chat service not yet implemented
                    _buildContactCard(
                      context,
                      backgroundColor: const Color(0xFFE6F3FF),
                      icon: Icons.chat_bubble_outline,
                      iconColor: Colors.blue,
                      title: "Live Chat",
                      statusText: "Available 9 AM - 9 PM",
                      statusColor: Colors.blue,
                      subtitle:
                          "Chat with our support agents directly from the app for real-time assistance with your queries.",
                      buttonLabel: "Start Live Chat",
                      buttonColor: Colors.blue,
                      onPressed: () {
                        // Show a bottom sheet with a chat interface
                        _showLiveChatBottomSheet(context);
                      },
                    ),
                    */

                    // FAQ section card
                    Container(
                      margin: const EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 8,
                      ),
                      decoration: BoxDecoration(
                        color: isDarkMode
                            ? const Color(0xFF1E1E1E)
                            : const Color(0xFFF5F5F5),
                        borderRadius: BorderRadius.circular(16),
                        border: isDarkMode
                            ? Border.all(color: Colors.grey.shade800, width: 1)
                            : null,
                        boxShadow: [
                          BoxShadow(
                            color: isDarkMode
                                ? Colors.black.withAlpha(60)
                                : Colors.black.withAlpha(10),
                            blurRadius: 8,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(20),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: isDarkMode
                                        ? Colors.white.withAlpha(30)
                                        : Colors.grey.shade200,
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  child: Icon(
                                    Icons
                                        .question_answer_outlined, // Updated to match profile page
                                    color: isDarkMode
                                        ? Colors.white
                                        : Colors.grey.shade700,
                                    size: 24,
                                  ),
                                ),
                                const SizedBox(width: 12),
                                Text(
                                  "Frequently Asked Questions",
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.w600,
                                    color: isDarkMode
                                        ? Colors.white
                                        : Colors.black,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            Text(
                              "Find quick answers to common questions about charging, payments, and account management.",
                              style: TextStyle(
                                fontSize: 14,
                                color: isDarkMode
                                    ? Colors.grey.shade300
                                    : Colors.grey.shade800,
                                height: 1.4,
                              ),
                            ),
                            const SizedBox(height: 16),
                            SizedBox(
                              width: double.infinity,
                              height: 48,
                              child: OutlinedButton(
                                onPressed: () {
                                  _navigateToFAQPage(
                                      context); // Navigate forward to FAQ page
                                },
                                style: OutlinedButton.styleFrom(
                                  side: BorderSide(
                                      color: isDarkMode
                                          ? Colors.white.withAlpha(100)
                                          : Colors.grey.shade400),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  foregroundColor: isDarkMode
                                      ? Colors.white
                                      : Colors.grey.shade800,
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.question_answer_outlined,
                                      size: 18,
                                      color: isDarkMode
                                          ? Colors.white
                                          : Colors.grey.shade800,
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      "Browse FAQs",
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w600,
                                        color: isDarkMode
                                            ? Colors.white
                                            : Colors.grey.shade800,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Footer row at bottom
            Padding(
              padding: const EdgeInsets.only(bottom: 16, top: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _buildFooterLink("Privacy Policy", () {
                    _navigateToPrivacySettings(context);
                  }),
                  Text(" • ",
                      style: TextStyle(
                          color: isDarkMode ? Colors.white : Colors.grey)),
                  _buildFooterLink("Terms of Service", () {
                    _launchURL(
                        context, 'https://app.eeil.online/termsandcondition');
                  }),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Launch URL helper with enhanced error handling
  Future<void> _launchURL(BuildContext context, String url) async {
    try {
      final Uri uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        // Handle error without using context after async gap
        debugPrint('Could not launch URL: $url');
        if (context.mounted) {
          _showErrorSnackBar(context,
              'Could not open the link. Please check your internet connection.');
        }
      }
    } catch (e) {
      debugPrint('Error launching URL: $e');
      if (context.mounted) {
        _showErrorSnackBar(context, 'Error opening link: ${e.toString()}');
      }
    }
  }

  /// Show error snackbar with consistent styling matching Privacy Settings page
  void _showErrorSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        duration: const Duration(seconds: 4),
      ),
    );
  }

  /// Navigate to Privacy Settings page with proper error handling
  void _navigateToPrivacySettings(BuildContext context) {
    try {
      // Add haptic feedback for better user experience
      HapticFeedback.lightImpact();

      // Navigate to Privacy Settings page
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => const PrivacySettingsPage(),
        ),
      );

      debugPrint(
          '🔒 NAVIGATION: Successfully navigated to Privacy Settings page');
    } catch (e) {
      // Handle navigation errors gracefully
      debugPrint(
          '❌ NAVIGATION ERROR: Failed to navigate to Privacy Settings: $e');

      if (context.mounted) {
        _showErrorSnackBar(
          context,
          'Unable to open Privacy Settings. Please try again.',
        );
      }
    }
  }

  /// Navigate to FAQ page with proper error handling
  void _navigateToFAQPage(BuildContext context) {
    try {
      // Add haptic feedback for better user experience
      HapticFeedback.lightImpact();

      // Navigate forward to FAQ page
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => const FAQPage(),
        ),
      );

      debugPrint(
          '❓ NAVIGATION: Successfully navigated to FAQ page from Support Center');
    } catch (e) {
      // Handle navigation errors gracefully
      debugPrint('❌ NAVIGATION ERROR: Failed to navigate to FAQ page: $e');

      if (context.mounted) {
        _showErrorSnackBar(
          context,
          'Unable to open FAQ page. Please try again.',
        );
      }
    }
  }

  /*
  // Show live chat bottom sheet - COMMENTED OUT FOR FUTURE USE
  // Backend configuration and live chat service not yet implemented
  void _showLiveChatBottomSheet(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.75,
          decoration: BoxDecoration(
            color: isDarkMode ? const Color(0xFF0E0E0E) : Colors.white,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(isDarkMode ? 80 : 40),
                blurRadius: 10,
                offset: const Offset(0, -2),
              ),
            ],
          ),
          child: Column(
            children: [
              // Chat header
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 16,
                ),
                decoration: BoxDecoration(
                  color: const Color(0xFF3D7AF5),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(20),
                    topRight: Radius.circular(20),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(26),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: const Icon(
                        Icons.support_agent,
                        color: Color(0xFF3D7AF5),
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    const Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "Ecoplug Support",
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                          Text(
                            "Typically replies in a few minutes",
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.white70,
                            ),
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.close, color: Colors.white),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ],
                ),
              ),

              // Chat messages area (placeholder)
              Expanded(
                child: Center(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.chat_bubble_outline,
                        size: 64,
                        color: isDarkMode
                            ? Colors.grey.shade700
                            : Colors.grey.shade300,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        "Start a conversation",
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color:
                              isDarkMode ? Colors.white : Colors.grey.shade800,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 40),
                        child: Text(
                          "Our support team is here to help you with any questions or issues you may have.",
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 16,
                            color: isDarkMode
                                ? Colors.grey.shade400
                                : Colors.grey.shade600,
                            height: 1.4,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // Chat input area
              Container(
                padding: const EdgeInsets.fromLTRB(16, 12, 16, 24),
                decoration: BoxDecoration(
                  color: isDarkMode ? const Color(0xFF1A1A1A) : Colors.white,
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(20),
                    bottomRight: Radius.circular(20),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(isDarkMode ? 40 : 13),
                      blurRadius: 6,
                      offset: const Offset(0, -2),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: TextField(
                        decoration: InputDecoration(
                          hintText: "Type your message...",
                          hintStyle: TextStyle(
                              color: isDarkMode
                                  ? Colors.grey.shade400
                                  : Colors.grey.shade500),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(24),
                            borderSide: BorderSide.none,
                          ),
                          filled: true,
                          fillColor: isDarkMode
                              ? const Color(0xFF1A1A1A)
                              : Colors.grey.shade100,
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 20,
                            vertical: 12,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        color: const Color(0xFF3D7AF5),
                        borderRadius: BorderRadius.circular(24),
                        boxShadow: [
                          BoxShadow(
                            color: const Color(0xFF3D7AF5)
                                .withAlpha(isDarkMode ? 80 : 40),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          borderRadius: BorderRadius.circular(24),
                          onTap: () {
                            // Send message logic would go here
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: const Text('Message sent!'),
                                backgroundColor: isDarkMode
                                    ? const Color(0xFF3D7AF5)
                                    : const Color(0xFF3D7AF5),
                                behavior: SnackBarBehavior.floating,
                              ),
                            );
                          },
                          child: const Center(
                            child:
                                Icon(Icons.send, color: Colors.white, size: 22),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
  */

  // Reusable method for building each contact card with improved dark mode support
  Widget _buildContactCard(
    BuildContext context, {
    required Color backgroundColor,
    required IconData icon,
    required Color iconColor,
    required String title,
    required String statusText,
    required Color statusColor,
    required String subtitle,
    required String buttonLabel,
    required Color buttonColor,
    required VoidCallback onPressed,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // Simplified dark mode color mapping
    // Use predefined dark colors based on icon color for better reliability
    Color darkModeColor;

    if (iconColor == AppThemes.primaryColor) {
      // WhatsApp card
      darkModeColor = const Color(0xFF0E3B1C);
    } else if (iconColor == Colors.orange) {
      // Email card
      darkModeColor = const Color(0xFF4D3410);
    } else if (iconColor == const Color(0xFF3D7AF5) ||
        iconColor == Colors.blue) {
      // Call Us or Live Chat card
      darkModeColor = const Color(0xFF1A3366);
    } else {
      // Fallback for any other cards
      darkModeColor = const Color(0xFF1E1E1E);
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      decoration: BoxDecoration(
        // Use darker colors in dark mode
        color: isDarkMode ? darkModeColor : backgroundColor,
        borderRadius: BorderRadius.circular(16),
        border: isDarkMode
            ? Border.all(color: const Color(0xFF2E2E2E), width: 1)
            : null,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(isDarkMode ? 40 : 10),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      // Removed fixed height constraints to allow natural sizing
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Icon at the top with container for better visibility
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: isDarkMode
                    ? Colors.white.withAlpha(
                        38) // More visible in dark mode (15% opacity)
                    : iconColor.withAlpha(20),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: isDarkMode ? Colors.white : iconColor,
                size: 26, // Slightly larger
              ),
            ),
            const SizedBox(height: 10),

            // Title with proper spacing
            Text(
              title,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: isDarkMode ? Colors.white : Colors.black,
              ),
            ),
            const SizedBox(height: 4),

            // Status with icon aligned properly
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
              decoration: BoxDecoration(
                color: isDarkMode
                    ? Colors.white.withAlpha(25) // More visible background
                    : statusColor.withAlpha(15),
                borderRadius: BorderRadius.circular(12),
                border: isDarkMode
                    ? Border.all(color: Colors.white.withAlpha(50), width: 0.5)
                    : null,
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.access_time_rounded,
                      size: 14, color: isDarkMode ? Colors.white : statusColor),
                  const SizedBox(width: 6),
                  Text(
                    statusText,
                    style: TextStyle(
                      fontSize: 13,
                      color: isDarkMode ? Colors.white : statusColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 10),

            // Subtitle with limited lines to prevent overflow - removed Expanded widget
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 13, // Slightly smaller font
                color: isDarkMode ? Colors.grey.shade300 : Colors.grey.shade700,
                height: 1.2, // Reduced line height
              ),
              maxLines: 3, // Allow one more line
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 12), // Increased gap

            // Button at the bottom with proper spacing
            SizedBox(
              width: double.infinity,
              height: 48, // Slightly taller for better tap target
              child: ElevatedButton(
                onPressed: onPressed,
                style: ElevatedButton.styleFrom(
                  backgroundColor: isDarkMode
                      ? buttonColor
                          .withAlpha(230) // Slightly dimmed in dark mode
                      : buttonColor,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: isDarkMode ? 4 : 0,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shadowColor:
                      isDarkMode ? buttonColor.withAlpha(100) : Colors.black26,
                ),
                child: Text(
                  buttonLabel,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Enhanced footer link with better visibility in dark mode
  Widget _buildFooterLink(String text, VoidCallback onTap) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: isDarkMode
              ? Colors.white.withAlpha(15)
              : Colors.blue.withAlpha(10),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          text,
          style: TextStyle(
            fontSize: 14,
            color: isDarkMode
                ? Colors.white // White text for dark mode
                : const Color(0xFF3D7AF5),
            fontWeight: FontWeight.w500,
            decoration: TextDecoration.underline,
          ),
        ),
      ),
    );
  }
}
