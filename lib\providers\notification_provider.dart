import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import 'package:ecoplug/services/unified_notification_service.dart';
import 'package:ecoplug/config/notification_config.dart';

/// Notification state model
class NotificationState {
  final bool isInitialized;
  final String? fcmToken;
  final Map<String, bool> serviceStatus;
  final bool notificationsEnabled;
  final Map<String, bool> preferences;

  const NotificationState({
    this.isInitialized = false,
    this.fcmToken,
    this.serviceStatus = const {},
    this.notificationsEnabled = false,
    this.preferences = const {},
  });

  NotificationState copyWith({
    bool? isInitialized,
    String? fcmToken,
    Map<String, bool>? serviceStatus,
    bool? notificationsEnabled,
    Map<String, bool>? preferences,
  }) {
    return NotificationState(
      isInitialized: isInitialized ?? this.isInitialized,
      fcmToken: fcmToken ?? this.fcmToken,
      serviceStatus: serviceStatus ?? this.serviceStatus,
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      preferences: preferences ?? this.preferences,
    );
  }
}

/// Notification provider for managing notification state and operations
class NotificationNotifier extends StateNotifier<NotificationState> {
  final UnifiedNotificationService _notificationService;

  NotificationNotifier(this._notificationService) : super(const NotificationState()) {
    _initialize();
  }

  /// Initialize notification service and update state
  Future<void> _initialize() async {
    try {
      debugPrint('🔔 Initializing notification provider...');

      // Initialize the service
      await _notificationService.initialize();

      // Get FCM token
      final token = await _notificationService.getFCMToken();

      // Get service status
      final status = _notificationService.getServiceStatus();

      // Check if notifications are enabled
      final enabled = await _notificationService.areNotificationsEnabled();

      // Get default preferences from configuration
      final defaultPrefs = <String, bool>{};
      for (final pref in NotificationConfig.getUserConfigurablePreferences()) {
        defaultPrefs[pref.key] = pref.defaultEnabled;
      }

      // Update state
      state = state.copyWith(
        isInitialized: true,
        fcmToken: token,
        serviceStatus: status,
        notificationsEnabled: enabled,
        preferences: defaultPrefs,
      );

      debugPrint('✅ Notification provider initialized successfully');
    } catch (e) {
      debugPrint('❌ Error initializing notification provider: $e');
    }
  }

  /// Get FCM token
  Future<String?> getFCMToken() async {
    final token = await _notificationService.getFCMToken();
    state = state.copyWith(fcmToken: token);
    return token;
  }

  /// Subscribe to topic
  Future<void> subscribeToTopic(String topic) async {
    await _notificationService.subscribeToTopic(topic);
  }

  /// Unsubscribe from topic
  Future<void> unsubscribeFromTopic(String topic) async {
    await _notificationService.unsubscribeFromTopic(topic);
  }

  /// Subscribe to user-specific topics
  Future<void> subscribeToUserTopics(String userId) async {
    await _notificationService.subscribeToUserTopics(userId);
  }

  /// Unsubscribe from user-specific topics
  Future<void> unsubscribeFromUserTopics(String userId) async {
    await _notificationService.unsubscribeFromUserTopics(userId);
  }

  /// Update notification preferences
  Future<void> updatePreferences({
    bool? chargingUpdates,
    bool? stationAlerts,
    bool? promotions,
    bool? tripReminders,
  }) async {
    // Update service preferences
    await _notificationService.updateNotificationPreferences(
      chargingUpdates: chargingUpdates,
      stationAlerts: stationAlerts,
      promotions: promotions,
      tripReminders: tripReminders,
    );

    // Update local state
    final newPreferences = Map<String, bool>.from(state.preferences);
    if (chargingUpdates != null) newPreferences['charging_updates'] = chargingUpdates;
    if (stationAlerts != null) newPreferences['station_alerts'] = stationAlerts;
    if (promotions != null) newPreferences['promotions'] = promotions;
    if (tripReminders != null) newPreferences['trip_reminders'] = tripReminders;

    state = state.copyWith(preferences: newPreferences);
  }

  /// Show charging notification
  Future<void> showChargingNotification({
    required String title,
    required String body,
    required double chargePercentage,
    required bool isCharging,
    String? stationName,
    String? connectorType,
    double? powerKw,
    double? energyKwh,
    Duration? duration,
    double? cost,
  }) async {
    await _notificationService.showChargingNotification(
      title: title,
      body: body,
      chargePercentage: chargePercentage,
      isCharging: isCharging,
      stationName: stationName,
      connectorType: connectorType,
      powerKw: powerKw,
      energyKwh: energyKwh,
      duration: duration,
      cost: cost,
    );
  }

  /// Clear charging notification
  Future<void> clearChargingNotification() async {
    await _notificationService.clearChargingNotification();
  }

  /// Show test notification (debug only)
  Future<void> showTestNotification() async {
    if (kDebugMode) {
      await _notificationService.showTestNotification();
    }
  }

  /// Send FCM token to backend
  Future<void> sendTokenToBackend(String userId) async {
    await _notificationService.sendTokenToBackend(userId);
  }

  /// Refresh service status
  Future<void> refreshStatus() async {
    final status = _notificationService.getServiceStatus();
    final enabled = await _notificationService.areNotificationsEnabled();

    state = state.copyWith(
      serviceStatus: status,
      notificationsEnabled: enabled,
    );
  }

  /// Subscribe to location-based topics
  Future<void> subscribeToLocationTopics(String city, String state) async {
    await _notificationService.subscribeToLocationTopics(city, state);
  }

  /// Show welcome notification
  Future<void> showWelcomeNotification({
    String? userName,
    bool isFirstLogin = false,
  }) async {
    await _notificationService.showWelcomeNotification(
      userName: userName,
      isFirstLogin: isFirstLogin,
    );
  }

  /// Clear welcome notification
  Future<void> clearWelcomeNotification() async {
    await _notificationService.clearWelcomeNotification();
  }

  /// Get welcome notification statistics
  Future<Map<String, dynamic>> getWelcomeStats() async {
    return await _notificationService.getWelcomeStats();
  }

  /// Reset welcome notification tracking (debug only)
  Future<void> resetWelcomeTracking() async {
    await _notificationService.resetWelcomeTracking();
  }
}

/// Provider for notification service
final notificationServiceProvider = Provider<UnifiedNotificationService>((ref) {
  return UnifiedNotificationService();
});

/// Provider for notification state and operations
final notificationProvider = StateNotifierProvider<NotificationNotifier, NotificationState>((ref) {
  final service = ref.watch(notificationServiceProvider);
  return NotificationNotifier(service);
});

/// Provider for FCM token
final fcmTokenProvider = FutureProvider<String?>((ref) async {
  final notifier = ref.watch(notificationProvider.notifier);
  return await notifier.getFCMToken();
});

/// Provider for notification preferences
final notificationPreferencesProvider = Provider<Map<String, bool>>((ref) {
  return ref.watch(notificationProvider).preferences;
});

/// Provider for service status
final notificationServiceStatusProvider = Provider<Map<String, bool>>((ref) {
  return ref.watch(notificationProvider).serviceStatus;
});

/// Provider for notifications enabled status
final notificationsEnabledProvider = Provider<bool>((ref) {
  return ref.watch(notificationProvider).notificationsEnabled;
});
