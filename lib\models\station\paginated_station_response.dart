import 'package:flutter/foundation.dart';
import '../station.dart'; // Import the existing Station model
import 'paginated_stations_response.dart'; // Import the other paginated response model

class PaginatedStationResponse {
  final String message;
  final bool success;
  final List<Station> data;
  // Add pagination fields if the API provides them (e.g., currentPage, lastPage, total)
  // final int? currentPage;
  // final int? lastPage;
  // final int? total;

  PaginatedStationResponse({
    required this.message,
    required this.success,
    required this.data,
    // this.currentPage,
    // this.lastPage,
    // this.total,
  });

  factory PaginatedStationResponse.fromJson(Map<String, dynamic> json) {
    List<Station> stations = [];

    try {
      if (json['data'] != null) {
        if (json['data'] is List) {
          // Handle data as a list of stations with individual error handling
          final List<dynamic> stationList = json['data'] as List;
          for (int i = 0; i < stationList.length; i++) {
            try {
              final stationJson = stationList[i] as Map<String, dynamic>;
              final station = Station.fromJson(stationJson);
              stations.add(station);
            } catch (e) {
              debugPrint('Error parsing station at index $i: $e');
              debugPrint('Station data: ${stationList[i]}');
              // Continue with next station instead of breaking entire list
            }
          }
        } else if (json['data'] is Map) {
          // Handle data as a map (possibly with pagination info)
          final dataMap = json['data'] as Map<String, dynamic>;
          if (dataMap.containsKey('data') && dataMap['data'] is List) {
            final List<dynamic> stationList = dataMap['data'] as List;
            for (int i = 0; i < stationList.length; i++) {
              try {
                final stationJson = stationList[i] as Map<String, dynamic>;
                final station = Station.fromJson(stationJson);
                stations.add(station);
              } catch (e) {
                debugPrint(
                    'Error parsing station at index $i in nested data: $e');
                debugPrint('Station data: ${stationList[i]}');
                // Continue with next station instead of breaking entire list
              }
            }
          }
        }
      }
    } catch (e) {
      // Don't throw - return partial results if any stations were parsed successfully
    }

    return PaginatedStationResponse(
      message: json['message'] ?? 'No message',
      success: json['success'] ?? false,
      data: stations,
      // Parse pagination fields if they exist in the JSON
      // currentPage: json['current_page'],
      // lastPage: json['last_page'],
      // total: json['total'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'message': message,
      'success': success,
      'data': data.map((station) => station.toJson()).toList(),
      // 'current_page': currentPage,
      // 'last_page': lastPage,
      // 'total': total,
    };
  }

  // Convert from PaginatedStationsResponse to PaginatedStationResponse
  factory PaginatedStationResponse.fromPaginatedStationsResponse(
      PaginatedStationsResponse response) {
    // Convert PaginatedStation objects to Station objects
    List<Station> stations = [];

    if (response.data != null) {
      for (var paginatedStation in response.data!) {
        // Extract connector types
        List<ConnectorType> connectorTypes =
            paginatedStation.getConnectorTypes();

        // Convert connector types to Connector objects
        List<Connector> connectors = connectorTypes.map((connectorType) {
          // Create a unique ID for the connector
          final String connectorId =
              'conn-${paginatedStation.stationId}-${connectorType.name ?? "unknown"}';

          // Extract power value ONLY from direct API data - no parsing from names
          String powerValue = '';
          int? maxElectricPower;

          // CRITICAL: Only use direct power values from API, no extraction from names
          if (connectorType.power != null && connectorType.power!.isNotEmpty) {
            powerValue = connectorType.power!;
            debugPrint('Using direct power value from API: $powerValue');
          } else {
            debugPrint(
                'No power value available from API for connector type: ${connectorType.name}');
            powerValue = ''; // Keep empty when no API data available
          }

          // Only use maxElectricPower if directly provided
          if (connectorType.maxElectricPower != null &&
              connectorType.maxElectricPower! > 0) {
            maxElectricPower = connectorType.maxElectricPower;
            debugPrint(
                'Using direct maxElectricPower from API: $maxElectricPower');
          } else {
            debugPrint(
                'No maxElectricPower available from API for connector type: ${connectorType.name}');
            maxElectricPower = null; // Keep null when no API data available
          }

          return Connector(
            id: connectorId,
            name: connectorType.name ?? '',
            type: connectorType.name ?? '',
            price: 0.0,
            power: powerValue,
            totalGuns: connectorType.guns ?? 1,
            availableGuns: connectorType.availableGuns ?? 0,
            icon: connectorType.icon,
            maxElectricPower: maxElectricPower,
          );
        }).toList();

        // Validate that we have a valid UID
        if (paginatedStation.uid == null || paginatedStation.uid!.isEmpty) {
          debugPrint(
              'WARNING: Skipping station with missing UID: ${paginatedStation.name}');
          continue; // Skip stations without UIDs
        }

        // Create Station object with the validated UID
        stations.add(Station(
          id: paginatedStation.stationId?.toString() ?? '',
          name: paginatedStation.name ?? '',
          address: paginatedStation.address ?? '',
          city: paginatedStation.city,
          latitude: paginatedStation.latitude ?? 0.0,
          longitude: paginatedStation.longitude ?? 0.0,
          distance: paginatedStation.distance ?? 0.0,
          status: paginatedStation.status ?? '',
          rating: paginatedStation.rating ?? 0.0,
          reviews: paginatedStation.reviewCount ?? 0,
          connectors: connectors,
          images: paginatedStation.imageUrl != null
              ? [paginatedStation.imageUrl!]
              : [],
          evses: [],
          uid: paginatedStation.uid ?? '', // Handle nullable UID safely
          types: paginatedStation.types,
        ));
      }
    }

    return PaginatedStationResponse(
      message: response.message ?? 'No message',
      success: response.success ?? false,
      data: stations,
    );
  }
}
