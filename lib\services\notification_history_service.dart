import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:ecoplug/models/notification_history_item.dart';
import 'package:ecoplug/services/notification_storage_service.dart';
import 'package:ecoplug/services/fcm_service.dart';

/// Notification History Service
/// Integrates with all notification services to capture and store notification history
/// Provides unified interface for notification history management
class NotificationHistoryService {
  static final NotificationHistoryService _instance =
      NotificationHistoryService._internal();
  factory NotificationHistoryService() => _instance;
  NotificationHistoryService._internal();

  final NotificationStorageService _storageService =
      NotificationStorageService();
  final FCMService _fcmService = FCMService();

  bool _isInitialized = false;
  StreamSubscription<RemoteMessage>? _fcmSubscription;

  /// Initialize the notification history service
  Future<void> initialize() async {
    if (_isInitialized) {
      debugPrint('📚 Notification History Service already initialized');
      return;
    }

    try {
      debugPrint('📚 ===== INITIALIZING NOTIFICATION HISTORY SERVICE =====');

      // Initialize storage service
      await _storageService.initialize();

      // Clean up any existing sample/mock notifications from previous app versions
      await cleanupSampleNotifications();

      // Initialize FCM service if not already initialized
      await _fcmService.initialize();

      // Set up FCM message listeners
      _setupFCMListeners();

      _isInitialized = true;
      debugPrint('✅ Notification History Service initialized successfully');
    } catch (e) {
      debugPrint('❌ Error initializing Notification History Service: $e');
      rethrow;
    }
  }

  /// Set up FCM message listeners to capture notifications
  void _setupFCMListeners() {
    try {
      // Listen for foreground messages
      FirebaseMessaging.onMessage.listen((RemoteMessage message) {
        _handleFCMMessage(message);
      });

      // Listen for background messages (when app is opened from notification)
      FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
        _handleFCMMessage(message);
      });

      debugPrint('✅ FCM listeners set up for notification history');
    } catch (e) {
      debugPrint('❌ Error setting up FCM listeners: $e');
    }
  }

  /// Handle FCM message and store in history
  Future<void> _handleFCMMessage(RemoteMessage message) async {
    try {
      final notification = message.notification;
      if (notification == null) return;

      debugPrint('📚 Capturing FCM notification: ${notification.title}');

      final title = notification.title ?? 'FCM Notification';
      final body = notification.body ?? '';

      // Validate that this is a real notification, not test/sample data
      if (!_isRealNotification(title, body, message.data)) {
        debugPrint('🚫 Skipping test/sample FCM notification: $title');
        return;
      }

      final historyItem = NotificationHistoryItem.fromFCM(
        messageId:
            message.messageId ?? 'fcm_${DateTime.now().millisecondsSinceEpoch}',
        title: title,
        body: body,
        timestamp: DateTime.now(),
        data: message.data,
        imageUrl:
            notification.android?.imageUrl ?? notification.apple?.imageUrl,
      );

      await _storageService.addNotification(historyItem);
      debugPrint('✅ FCM notification stored in history');
    } catch (e) {
      debugPrint('❌ Error handling FCM message: $e');
    }
  }

  /// Add local notification to history
  Future<void> addLocalNotification({
    required int notificationId,
    required String title,
    required String body,
    required NotificationType type,
    String? channelId,
    Map<String, dynamic>? data,
    String? actionUrl,
    int? priority,
  }) async {
    if (!_isInitialized) await initialize();

    try {
      debugPrint('📚 Adding local notification to history: $title');

      // Validate that this is a real notification, not test/sample data
      if (!_isRealNotification(title, body, data)) {
        debugPrint('🚫 Skipping test/sample notification: $title');
        return;
      }

      final historyItem = NotificationHistoryItem.fromLocal(
        notificationId: notificationId,
        title: title,
        body: body,
        timestamp: DateTime.now(),
        type: type,
        channelId: channelId,
        data: data,
        actionUrl: actionUrl,
        priority: priority,
      );

      await _storageService.addNotification(historyItem);
      debugPrint('✅ Local notification stored in history');
    } catch (e) {
      debugPrint('❌ Error adding local notification: $e');
    }
  }

  /// Add charging notification to history
  Future<void> addChargingNotification({
    required String title,
    required String body,
    required Map<String, dynamic> chargingData,
  }) async {
    await addLocalNotification(
      notificationId: DateTime.now().millisecondsSinceEpoch,
      title: title,
      body: body,
      type: NotificationType.charging,
      channelId: 'charging_session',
      data: chargingData,
      priority: 1,
    );
  }

  /// Add welcome notification to history
  Future<void> addWelcomeNotification({
    required String title,
    required String body,
    String? userName,
    bool isFirstLogin = false,
  }) async {
    await addLocalNotification(
      notificationId: DateTime.now().millisecondsSinceEpoch,
      title: title,
      body: body,
      type: NotificationType.welcome,
      channelId: 'user_welcome',
      data: {
        'user_name': userName,
        'is_first_login': isFirstLogin,
      },
      priority: 1,
    );
  }

  /// Add payment notification to history
  Future<void> addPaymentNotification({
    required String title,
    required String body,
    required Map<String, dynamic> paymentData,
  }) async {
    await addLocalNotification(
      notificationId: DateTime.now().millisecondsSinceEpoch,
      title: title,
      body: body,
      type: NotificationType.payment,
      channelId: 'wallet_updates',
      data: paymentData,
      priority: 1,
    );
  }

  /// Add station notification to history
  Future<void> addStationNotification({
    required String title,
    required String body,
    required Map<String, dynamic> stationData,
  }) async {
    await addLocalNotification(
      notificationId: DateTime.now().millisecondsSinceEpoch,
      title: title,
      body: body,
      type: NotificationType.station,
      channelId: 'station_alerts',
      data: stationData,
      priority: 0,
    );
  }

  /// Add offer notification to history
  Future<void> addOfferNotification({
    required String title,
    required String body,
    required Map<String, dynamic> offerData,
  }) async {
    await addLocalNotification(
      notificationId: DateTime.now().millisecondsSinceEpoch,
      title: title,
      body: body,
      type: NotificationType.offer,
      channelId: 'promotions',
      data: offerData,
      priority: 0,
    );
  }

  /// Get all notifications
  Future<List<NotificationHistoryItem>> getAllNotifications() async {
    if (!_isInitialized) await initialize();
    return await _storageService.getAllNotifications();
  }

  /// Get notifications by type
  Future<List<NotificationHistoryItem>> getNotificationsByType(
      NotificationType type) async {
    if (!_isInitialized) await initialize();
    return await _storageService.getNotificationsByType(type);
  }

  /// Get unread notifications
  Future<List<NotificationHistoryItem>> getUnreadNotifications() async {
    if (!_isInitialized) await initialize();
    return await _storageService.getUnreadNotifications();
  }

  /// Get notifications by source
  Future<List<NotificationHistoryItem>> getNotificationsBySource(
      NotificationSource source) async {
    if (!_isInitialized) await initialize();
    return await _storageService.getNotificationsBySource(source);
  }

  /// Mark notification as read
  Future<void> markAsRead(String notificationId) async {
    if (!_isInitialized) await initialize();
    await _storageService.markAsRead(notificationId);
  }

  /// Mark all notifications as read
  Future<void> markAllAsRead() async {
    if (!_isInitialized) await initialize();
    await _storageService.markAllAsRead();
  }

  /// Delete notification
  Future<void> deleteNotification(String notificationId) async {
    if (!_isInitialized) await initialize();
    await _storageService.deleteNotification(notificationId);
  }

  /// Clear all notifications
  Future<void> clearAllNotifications() async {
    if (!_isInitialized) await initialize();
    await _storageService.clearAllNotifications();
  }

  /// Get notification count
  Future<int> getNotificationCount() async {
    if (!_isInitialized) await initialize();
    return await _storageService.getNotificationCount();
  }

  /// Get unread notification count
  Future<int> getUnreadCount() async {
    if (!_isInitialized) await initialize();
    return await _storageService.getUnreadCount();
  }

  /// Get notification stream for real-time updates
  Stream<List<NotificationHistoryItem>> get notificationStream {
    return _storageService.notificationStream;
  }

  /// Get service statistics
  Map<String, dynamic> getServiceStats() {
    final storageStats = _storageService.getStorageStats();
    storageStats['history_service_initialized'] = _isInitialized;
    storageStats['fcm_listener_active'] = _fcmSubscription != null;
    return storageStats;
  }

  /// Validate if a notification is real (not test/sample data)
  /// Returns false for test, sample, debug, or mock notifications
  bool _isRealNotification(
      String title, String body, Map<String, dynamic>? data) {
    // In debug mode, allow all notifications for testing
    if (kDebugMode) return true;

    final titleLower = title.toLowerCase();
    final bodyLower = body.toLowerCase();
    final dataMap = data ?? {};

    // Check for test/sample patterns in title and body
    final testPatterns = [
      'test',
      'sample',
      'debug',
      'mock',
      'demo',
      'example',
      'quick test',
      'notification test',
    ];

    for (final pattern in testPatterns) {
      if (titleLower.contains(pattern) || bodyLower.contains(pattern)) {
        return false;
      }
    }

    // Check for test patterns in data
    final testDataPatterns = {
      'session_id': ['test_session', 'debug_session', 'quick_test'],
      'transaction_id': ['txn_001', 'test_txn', 'debug_txn'],
      'station_id': ['koramangala_001', 'test_station', 'debug_station'],
      'offer_code': ['weekend15', 'test_offer', 'debug_offer'],
      'user_name': ['test_user', 'debug_user'],
    };

    for (final entry in testDataPatterns.entries) {
      final key = entry.key;
      final testValues = entry.value;

      if (dataMap.containsKey(key)) {
        final value = dataMap[key].toString().toLowerCase();
        for (final testValue in testValues) {
          if (value.contains(testValue)) {
            return false;
          }
        }
      }
    }

    return true;
  }

  /// Clean up any existing sample/mock notifications from storage
  /// This method removes test notifications that may have been added during development
  Future<void> cleanupSampleNotifications() async {
    try {
      debugPrint('🧹 Cleaning up sample notifications...');
      await _storageService.clearSampleNotifications();
      debugPrint('✅ Sample notification cleanup completed');
    } catch (e) {
      debugPrint('❌ Error cleaning up sample notifications: $e');
    }
  }

  /// Dispose of the service
  void dispose() {
    _fcmSubscription?.cancel();
    _storageService.dispose();
    debugPrint('📚 Notification History Service disposed');
  }
}
