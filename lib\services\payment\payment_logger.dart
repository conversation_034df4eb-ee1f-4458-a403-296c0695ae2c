import 'package:flutter/foundation.dart';
import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Enhanced logging and debugging system for payment flows
/// Provides comprehensive logging for debugging payment issues and monitoring payment system health
class PaymentLogger {
  static const String _logPrefix = '📝 PAYMENT_LOGGER:';
  static final List<PaymentLogEntry> _logBuffer = [];
  static const int _maxBufferSize = 1000;
  static const int _maxLogFileSize = 5 * 1024 * 1024; // 5MB
  static Timer? _flushTimer;
  static bool _isInitialized = false;
  static File? _logFile;

  /// Initialize the payment logger
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint('$_logPrefix Initializing payment logger...');
      
      // Create log file
      await _createLogFile();
      
      // Start periodic flush timer
      _startFlushTimer();
      
      _isInitialized = true;
      debugPrint('$_logPrefix Payment logger initialized successfully');
      
      // Log initialization
      await logEvent(
        gateway: 'SYSTEM',
        event: 'LOGGER_INITIALIZED',
        message: 'Payment logger system started',
        level: LogLevel.info,
      );
      
    } catch (e) {
      debugPrint('$_logPrefix Failed to initialize payment logger: $e');
    }
  }

  /// Create log file
  static Future<void> _createLogFile() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final logDir = Directory('${directory.path}/payment_logs');
      
      if (!await logDir.exists()) {
        await logDir.create(recursive: true);
      }
      
      final timestamp = DateTime.now().toIso8601String().split('T')[0];
      _logFile = File('${logDir.path}/payment_log_$timestamp.txt');
      
      debugPrint('$_logPrefix Log file created: ${_logFile!.path}');
    } catch (e) {
      debugPrint('$_logPrefix Failed to create log file: $e');
    }
  }

  /// Start periodic flush timer
  static void _startFlushTimer() {
    _flushTimer?.cancel();
    _flushTimer = Timer.periodic(Duration(seconds: 30), (timer) {
      _flushLogs();
    });
  }

  /// Log payment event
  static Future<void> logEvent({
    required String gateway,
    required String event,
    required String message,
    LogLevel level = LogLevel.info,
    String? transactionId,
    double? amount,
    Map<String, dynamic>? data,
    String? errorCode,
    StackTrace? stackTrace,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    final logEntry = PaymentLogEntry(
      timestamp: DateTime.now(),
      gateway: gateway,
      event: event,
      message: message,
      level: level,
      transactionId: transactionId,
      amount: amount,
      data: data,
      errorCode: errorCode,
      stackTrace: stackTrace?.toString(),
    );

    _addToBuffer(logEntry);

    // Also print to debug console for immediate visibility
    final logMessage = _formatLogMessage(logEntry);
    switch (level) {
      case LogLevel.error:
        debugPrint('❌ $logMessage');
        break;
      case LogLevel.warning:
        debugPrint('⚠️ $logMessage');
        break;
      case LogLevel.info:
        debugPrint('ℹ️ $logMessage');
        break;
      case LogLevel.debug:
        debugPrint('🐛 $logMessage');
        break;
      case LogLevel.trace:
        debugPrint('🔍 $logMessage');
        break;
    }
  }

  /// Log payment initialization
  static Future<void> logInitialization({
    required String gateway,
    required bool success,
    String? errorMessage,
    Map<String, dynamic>? config,
  }) async {
    await logEvent(
      gateway: gateway,
      event: 'SDK_INITIALIZATION',
      message: success 
        ? 'SDK initialized successfully' 
        : 'SDK initialization failed: ${errorMessage ?? "Unknown error"}',
      level: success ? LogLevel.info : LogLevel.error,
      data: config,
    );
  }

  /// Log payment transaction start
  static Future<void> logTransactionStart({
    required String gateway,
    required String transactionId,
    required double amount,
    Map<String, dynamic>? params,
  }) async {
    await logEvent(
      gateway: gateway,
      event: 'TRANSACTION_START',
      message: 'Payment transaction started',
      level: LogLevel.info,
      transactionId: transactionId,
      amount: amount,
      data: params,
    );
  }

  /// Log payment transaction result
  static Future<void> logTransactionResult({
    required String gateway,
    required String transactionId,
    required String status,
    required String message,
    double? amount,
    Map<String, dynamic>? response,
    Duration? duration,
  }) async {
    final isSuccess = ['SUCCESS', 'SUCCESSFUL', 'COMPLETED'].contains(status.toUpperCase());
    final level = isSuccess ? LogLevel.info : LogLevel.warning;

    await logEvent(
      gateway: gateway,
      event: 'TRANSACTION_RESULT',
      message: 'Transaction completed with status: $status - $message',
      level: level,
      transactionId: transactionId,
      amount: amount,
      data: {
        'status': status,
        'response': response,
        'duration_ms': duration?.inMilliseconds,
      },
    );
  }

  /// Log payment error
  static Future<void> logError({
    required String gateway,
    required String error,
    String? transactionId,
    String? errorCode,
    StackTrace? stackTrace,
    Map<String, dynamic>? context,
  }) async {
    await logEvent(
      gateway: gateway,
      event: 'ERROR',
      message: error,
      level: LogLevel.error,
      transactionId: transactionId,
      errorCode: errorCode,
      stackTrace: stackTrace,
      data: context,
    );
  }

  /// Log network error
  static Future<void> logNetworkError({
    required String gateway,
    required String operation,
    required String error,
    String? transactionId,
    int? attemptNumber,
    bool? willRetry,
  }) async {
    await logEvent(
      gateway: gateway,
      event: 'NETWORK_ERROR',
      message: 'Network error during $operation: $error',
      level: LogLevel.error,
      transactionId: transactionId,
      data: {
        'operation': operation,
        'attempt': attemptNumber,
        'will_retry': willRetry,
      },
    );
  }

  /// Log API call
  static Future<void> logApiCall({
    required String gateway,
    required String endpoint,
    required String method,
    String? transactionId,
    Map<String, dynamic>? requestData,
    Map<String, dynamic>? responseData,
    int? statusCode,
    Duration? duration,
    bool? success,
  }) async {
    await logEvent(
      gateway: gateway,
      event: 'API_CALL',
      message: '$method $endpoint - ${success == true ? "SUCCESS" : "FAILED"}',
      level: success == true ? LogLevel.debug : LogLevel.warning,
      transactionId: transactionId,
      data: {
        'endpoint': endpoint,
        'method': method,
        'status_code': statusCode,
        'duration_ms': duration?.inMilliseconds,
        'request_data': requestData,
        'response_data': responseData,
      },
    );
  }

  /// Add log entry to buffer
  static void _addToBuffer(PaymentLogEntry entry) {
    _logBuffer.add(entry);
    
    // Remove old entries if buffer is full
    if (_logBuffer.length > _maxBufferSize) {
      _logBuffer.removeAt(0);
    }
  }

  /// Format log message for console output
  static String _formatLogMessage(PaymentLogEntry entry) {
    final timestamp = entry.timestamp.toIso8601String();
    final gateway = entry.gateway.padRight(10);
    final event = entry.event.padRight(20);
    final txnId = entry.transactionId != null ? '[${entry.transactionId}] ' : '';
    
    return '$timestamp [$gateway] $event $txnId${entry.message}';
  }

  /// Flush logs to file
  static Future<void> _flushLogs() async {
    if (_logBuffer.isEmpty || _logFile == null) return;

    try {
      final logEntries = List<PaymentLogEntry>.from(_logBuffer);
      _logBuffer.clear();

      final logLines = logEntries.map((entry) => _formatLogEntryForFile(entry)).toList();
      final logContent = logLines.join('\n') + '\n';

      // Check file size and rotate if necessary
      if (await _logFile!.exists()) {
        final fileSize = await _logFile!.length();
        if (fileSize > _maxLogFileSize) {
          await _rotateLogFile();
        }
      }

      await _logFile!.writeAsString(logContent, mode: FileMode.append);
      
    } catch (e) {
      debugPrint('$_logPrefix Failed to flush logs: $e');
    }
  }

  /// Format log entry for file output
  static String _formatLogEntryForFile(PaymentLogEntry entry) {
    final json = {
      'timestamp': entry.timestamp.toIso8601String(),
      'gateway': entry.gateway,
      'event': entry.event,
      'level': entry.level.toString().split('.').last,
      'message': entry.message,
      if (entry.transactionId != null) 'transaction_id': entry.transactionId,
      if (entry.amount != null) 'amount': entry.amount,
      if (entry.errorCode != null) 'error_code': entry.errorCode,
      if (entry.data != null) 'data': entry.data,
      if (entry.stackTrace != null) 'stack_trace': entry.stackTrace,
    };
    
    return jsonEncode(json);
  }

  /// Rotate log file when it gets too large
  static Future<void> _rotateLogFile() async {
    try {
      if (_logFile == null || !await _logFile!.exists()) return;

      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final directory = _logFile!.parent;
      final newPath = '${directory.path}/payment_log_archived_$timestamp.txt';
      
      await _logFile!.rename(newPath);
      await _createLogFile();
      
      debugPrint('$_logPrefix Log file rotated to: $newPath');
    } catch (e) {
      debugPrint('$_logPrefix Failed to rotate log file: $e');
    }
  }

  /// Get recent log entries
  static List<PaymentLogEntry> getRecentLogs({int limit = 100}) {
    final recentLogs = _logBuffer.reversed.take(limit).toList();
    return recentLogs.reversed.toList();
  }

  /// Get logs for specific transaction
  static List<PaymentLogEntry> getTransactionLogs(String transactionId) {
    return _logBuffer.where((entry) => entry.transactionId == transactionId).toList();
  }

  /// Get logs for specific gateway
  static List<PaymentLogEntry> getGatewayLogs(String gateway) {
    return _logBuffer.where((entry) => entry.gateway.toLowerCase() == gateway.toLowerCase()).toList();
  }

  /// Export logs as JSON
  static Future<String> exportLogsAsJson({
    DateTime? startTime,
    DateTime? endTime,
    String? gateway,
    String? transactionId,
  }) async {
    var logs = _logBuffer.asMap().values;

    if (startTime != null) {
      logs = logs.where((log) => log.timestamp.isAfter(startTime));
    }

    if (endTime != null) {
      logs = logs.where((log) => log.timestamp.isBefore(endTime));
    }

    if (gateway != null) {
      logs = logs.where((log) => log.gateway.toLowerCase() == gateway.toLowerCase());
    }

    if (transactionId != null) {
      logs = logs.where((log) => log.transactionId == transactionId);
    }

    final exportData = {
      'export_timestamp': DateTime.now().toIso8601String(),
      'total_entries': logs.length,
      'filters': {
        'start_time': startTime?.toIso8601String(),
        'end_time': endTime?.toIso8601String(),
        'gateway': gateway,
        'transaction_id': transactionId,
      },
      'logs': logs.map((entry) => {
        'timestamp': entry.timestamp.toIso8601String(),
        'gateway': entry.gateway,
        'event': entry.event,
        'level': entry.level.toString().split('.').last,
        'message': entry.message,
        'transaction_id': entry.transactionId,
        'amount': entry.amount,
        'error_code': entry.errorCode,
        'data': entry.data,
        'stack_trace': entry.stackTrace,
      }).toList(),
    };

    return jsonEncode(exportData);
  }

  /// Clear all logs
  static Future<void> clearLogs() async {
    _logBuffer.clear();
    
    if (_logFile != null && await _logFile!.exists()) {
      await _logFile!.delete();
      await _createLogFile();
    }
    
    debugPrint('$_logPrefix All logs cleared');
  }

  /// Dispose logger resources
  static void dispose() {
    _flushTimer?.cancel();
    _flushLogs();
    _isInitialized = false;
    debugPrint('$_logPrefix Payment logger disposed');
  }
}

/// Log levels for payment events
enum LogLevel {
  error,
  warning,
  info,
  debug,
  trace,
}

/// Payment log entry
class PaymentLogEntry {
  final DateTime timestamp;
  final String gateway;
  final String event;
  final String message;
  final LogLevel level;
  final String? transactionId;
  final double? amount;
  final Map<String, dynamic>? data;
  final String? errorCode;
  final String? stackTrace;

  const PaymentLogEntry({
    required this.timestamp,
    required this.gateway,
    required this.event,
    required this.message,
    required this.level,
    this.transactionId,
    this.amount,
    this.data,
    this.errorCode,
    this.stackTrace,
  });

  @override
  String toString() {
    return 'PaymentLogEntry(timestamp: $timestamp, gateway: $gateway, event: $event, level: $level, message: $message)';
  }
}
