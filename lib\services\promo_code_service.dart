import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/promo_code.dart';
import '../services/token_service.dart';

/// Service for managing promo code operations with caching and error handling
class PromoCodeService {
  // Singleton pattern
  static final PromoCodeService _instance = PromoCodeService._internal();
  factory PromoCodeService() => _instance;
  PromoCodeService._internal();

  // API endpoints
  static const String _baseUrl = 'https://api2.eeil.online/api/v1';
  static const String _promoCodesEndpoint = '$_baseUrl/user/promocodes?promo_type=recharge';
  static const String _verifyEndpoint = '$_baseUrl/promocodes/verify';

  // Cache keys
  static const String _cacheKey = 'cached_promo_codes';
  static const String _cacheTimestampKey = 'promo_codes_cache_timestamp';
  static const Duration _cacheExpiry = Duration(minutes: 15);

  // Token service for authentication
  final TokenService _tokenService = TokenService();

  /// Fetch promo codes from API with caching
  Future<PromoCodeResponse> fetchPromoCodes({bool forceRefresh = false}) async {
    try {
      // Check cache first unless force refresh is requested
      if (!forceRefresh) {
        final cachedResponse = await _getCachedPromoCodes();
        if (cachedResponse != null) {
          if (kDebugMode) {
            debugPrint('PromoCodeService: Returning cached promo codes');
          }
          return cachedResponse;
        }
      }

      // Get authentication token
      final token = await _getAuthToken();
      
      // Make API request
      final response = await http.get(
        Uri.parse(_promoCodesEndpoint),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          if (token != null) 'Authorization': 'Bearer $token',
        },
      );

      if (kDebugMode) {
        debugPrint('PromoCodeService: API response status: ${response.statusCode}');
      }

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        final promoResponse = PromoCodeResponse.fromJson(jsonData);

        // Cache the successful response
        if (promoResponse.success && promoResponse.data.isNotEmpty) {
          await _cachePromoCodes(promoResponse);
        }

        return promoResponse;
      } else if (response.statusCode == 401) {
        // Handle unauthorized - clear invalid token
        await _tokenService.clearToken();
        final prefs = await SharedPreferences.getInstance();
        await prefs.remove('auth_token');
        
        return const PromoCodeResponse(
          success: false,
          message: 'Please log in again to access promo codes',
          data: [],
        );
      } else {
        // Handle other HTTP errors
        String errorMessage = 'Failed to load promo codes';
        try {
          final errorData = json.decode(response.body);
          if (errorData['message'] != null) {
            errorMessage = errorData['message'];
          }
        } catch (_) {
          errorMessage = 'Failed to load promo codes (Error: ${response.statusCode})';
        }

        return PromoCodeResponse(
          success: false,
          message: errorMessage,
          data: [],
        );
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('PromoCodeService: Error fetching promo codes: $e');
      }
      
      return const PromoCodeResponse(
        success: false,
        message: 'Unable to connect to the server. Please check your internet connection and try again.',
        data: [],
      );
    }
  }

  /// Verify a promo code with the server
  Future<PromoCodeVerificationResponse> verifyPromoCode(String promoCode) async {
    if (promoCode.length <= 3) {
      return const PromoCodeVerificationResponse(
        success: false,
        message: 'Promo code must be more than 3 characters',
      );
    }

    try {
      // Get authentication token
      final token = await _getAuthToken();

      // Make verification request
      final response = await http.post(
        Uri.parse(_verifyEndpoint),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          if (token != null) 'Authorization': 'Bearer $token',
        },
        body: json.encode({
          'promo': promoCode.toUpperCase(),
        }),
      );

      if (kDebugMode) {
        debugPrint('PromoCodeService: Verification response status: ${response.statusCode}');
      }

      final responseData = json.decode(response.body);
      return PromoCodeVerificationResponse.fromJson(responseData);
    } catch (e) {
      if (kDebugMode) {
        debugPrint('PromoCodeService: Error verifying promo code: $e');
      }
      
      return const PromoCodeVerificationResponse(
        success: false,
        message: 'Network error. Please check your connection and try again.',
      );
    }
  }

  /// Get authentication token from secure storage or SharedPreferences
  Future<String?> _getAuthToken() async {
    // Try secure storage first
    String? token = await _tokenService.getToken();
    
    // Fallback to SharedPreferences if needed
    if (token == null) {
      final prefs = await SharedPreferences.getInstance();
      final prefToken = prefs.getString('auth_token');
      
      // If we have a token in SharedPreferences but not in secure storage,
      // save it to secure storage for future use
      if (prefToken != null && prefToken.isNotEmpty) {
        await _tokenService.saveToken(prefToken);
        token = prefToken;
      }
    }
    
    return token;
  }

  /// Cache promo codes locally
  Future<void> _cachePromoCodes(PromoCodeResponse response) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = json.encode({
        'success': response.success,
        'message': response.message,
        'data': response.data.map((promo) => promo.toJson()).toList(),
      });
      
      await prefs.setString(_cacheKey, jsonString);
      await prefs.setInt(_cacheTimestampKey, DateTime.now().millisecondsSinceEpoch);
      
      if (kDebugMode) {
        debugPrint('PromoCodeService: Cached ${response.data.length} promo codes');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('PromoCodeService: Error caching promo codes: $e');
      }
    }
  }

  /// Get cached promo codes if available and not expired
  Future<PromoCodeResponse?> _getCachedPromoCodes() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedData = prefs.getString(_cacheKey);
      final cacheTimestamp = prefs.getInt(_cacheTimestampKey);
      
      if (cachedData == null || cacheTimestamp == null) {
        return null;
      }
      
      // Check if cache is expired
      final cacheTime = DateTime.fromMillisecondsSinceEpoch(cacheTimestamp);
      final now = DateTime.now();
      if (now.difference(cacheTime) > _cacheExpiry) {
        if (kDebugMode) {
          debugPrint('PromoCodeService: Cache expired');
        }
        return null;
      }
      
      // Parse cached data
      final jsonData = json.decode(cachedData);
      return PromoCodeResponse.fromJson(jsonData);
    } catch (e) {
      if (kDebugMode) {
        debugPrint('PromoCodeService: Error reading cached promo codes: $e');
      }
      return null;
    }
  }

  /// Clear cached promo codes
  Future<void> clearCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_cacheKey);
      await prefs.remove(_cacheTimestampKey);
      
      if (kDebugMode) {
        debugPrint('PromoCodeService: Cache cleared');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('PromoCodeService: Error clearing cache: $e');
      }
    }
  }

  /// Filter promo codes based on search query
  List<PromoCode> filterPromoCodes(List<PromoCode> promoCodes, String searchQuery) {
    if (searchQuery.isEmpty) return promoCodes;
    return promoCodes.where((promo) => promo.matchesSearch(searchQuery)).toList();
  }

  /// Sort promo codes by savings amount (highest first)
  List<PromoCode> sortPromoCodesBySavings(List<PromoCode> promoCodes) {
    final sortedList = List<PromoCode>.from(promoCodes);
    sortedList.sort((a, b) => b.credits.compareTo(a.credits));
    return sortedList;
  }
}
