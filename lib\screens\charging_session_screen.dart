import 'dart:async';
import 'dart:io';
import 'dart:math' as math;
import 'dart:ui';
import 'package:flutter/material.dart';
import '../services/charging_session_service.dart';
import '../services/charging_parameters_service.dart';
import '../services/local_notification_manager.dart';
import '../services/active_charging_notification_service.dart';
import '../services/fcm_subscription_service.dart';

import '../models/charging_session.dart';
import 'billing/billing_details_page.dart';
import '../utils/app_themes.dart';

// Custom painter for multi-layered circular progress indicator
class CircularProgressPainter extends CustomPainter {
  final double progress;
  final double strokeWidth;
  final double rotationAngle;

  CircularProgressPainter({
    required this.progress,
    this.strokeWidth = 20.0,
    this.rotationAngle = 0.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = math.min(size.width, size.height) / 2 - strokeWidth;

    // Draw background track
    final trackPaint = Paint()
      ..color = Colors.white.withOpacity(0.2)
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.round;

    canvas.drawCircle(center, radius, trackPaint);

    // Draw progress arc with gradient
    final progressPaint = Paint()
      ..shader = SweepGradient(
        colors: [
          const Color(0xFF22C55E),
          const Color(0xFF4ADE80),
          const Color(0xFF22C55E),
        ],
        stops: const [0.0, 0.5, 1.0],
        startAngle: -math.pi / 2,
        endAngle: -math.pi / 2 + 2 * math.pi,
        transform: GradientRotation(rotationAngle),
      ).createShader(Rect.fromCircle(center: center, radius: radius))
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.round
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 4);

    final sweepAngle = 2 * math.pi * progress.clamp(0.0, 1.0);

    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      -math.pi / 2,
      sweepAngle,
      false,
      progressPaint,
    );

    // Draw end cap circle
    if (progress > 0 && progress < 1) {
      final endAngle = -math.pi / 2 + sweepAngle;
      final endX = center.dx + radius * math.cos(endAngle);
      final endY = center.dy + radius * math.sin(endAngle);

      // Glow effect
      final glowPaint = Paint()
        ..color = Colors.white.withOpacity(0.5)
        ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 8);
      canvas.drawCircle(Offset(endX, endY), strokeWidth * 0.7, glowPaint);

      // White circle
      final circlePaint = Paint()
        ..color = Colors.white
        ..style = PaintingStyle.fill;
      canvas.drawCircle(Offset(endX, endY), strokeWidth * 0.4, circlePaint);
    }
  }

  @override
  bool shouldRepaint(covariant CircularProgressPainter oldDelegate) {
    return progress != oldDelegate.progress ||
        rotationAngle != oldDelegate.rotationAngle;
  }
}

// Custom painter for matrix bar visualization
class MatrixBarPainter extends CustomPainter {
  final List<double> values;
  final Color color;
  final double animationValue;

  MatrixBarPainter({
    required this.values,
    required this.color,
    required this.animationValue,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final barWidth = size.width / (values.length * 2 - 1);
    final maxHeight = size.height * 0.8;

    for (int i = 0; i < values.length; i++) {
      final x = i * barWidth * 2;
      final height = maxHeight * values[i] * animationValue;
      final y = size.height - height;

      final rect = RRect.fromRectAndRadius(
        Rect.fromLTWH(x, y, barWidth, height),
        const Radius.circular(4),
      );

      final paint = Paint()
        ..color = color.withValues(alpha: 0.8)
        ..style = PaintingStyle.fill;

      canvas.drawRRect(rect, paint);

      // Add glow effect
      final glowPaint = Paint()
        ..color = color.withValues(alpha: 0.3)
        ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 4);

      canvas.drawRRect(rect, glowPaint);
    }
  }

  @override
  bool shouldRepaint(covariant MatrixBarPainter oldDelegate) {
    return animationValue != oldDelegate.animationValue;
  }
}

class ChargingSessionScreen extends StatefulWidget {
  final String? stationUid;
  final String? connectorId;
  final double initialCharge;
  // Direct mode removed - only normal mode supported
  final Map<String, dynamic>?
      verifiedSessionData; // CRITICAL FIX: Session data from initialization screen

  const ChargingSessionScreen({
    super.key,
    this.stationUid,
    this.connectorId,
    this.initialCharge = 0.0, // Changed from 0.75 to 0.0 (0% initial charge)
    // Direct mode removed - only normal mode supported
    this.verifiedSessionData, // CRITICAL FIX: Accept session data from initialization
  });

  @override
  State<ChargingSessionScreen> createState() => _ChargingSessionScreenState();
}

class _ChargingSessionScreenState extends State<ChargingSessionScreen>
    with TickerProviderStateMixin {
  late double _chargePercentage;

  // API data fields - NO MORE MOCK DATA
  String _currentPrice = "₹0.00"; // Will be updated from API
  String _currentPower = "0.0 kW"; // Will be updated from API
  String _energyDelivered = "0.0 kWh"; // Will be updated from API
  String _co2EmissionSaved = "0.0 kg"; // Will be updated from API

  bool _isCharging = true;
  ChargingSession? _session;
  bool _isLoadingSession = false;
  String? _errorMessage;

  // Slider state for slide-to-stop functionality
  double _sliderPosition = 0.0;
  bool _isSliding = false;
  late AnimationController _sliderAnimationController;
  late Animation<double> _sliderAnimation;

  // Real charging flow manager
  String? _transactionId;
  String? _authorizationReference;

  // Navigation state management
  bool _hasNavigatedToBilling = false;

  // Stop charging loading state
  bool _isStoppingCharging = false;

  // Enhanced loading states for modern UI
  bool _isChargingTriggerLoading = false;

  // Charging timer state (for display only - notifications handled by FCM)
  String _chargingTimer = "00:00:00";
  DateTime? _chargingStartTime;
  Timer? _timerUpdateTimer;

  // Additional fields for compatibility - NOW UPDATED FROM API
  String _environmentalImpactCO2 = "0.0 kg CO2"; // Will be updated from API
  String _eta = "0h 0m"; // Will be calculated from API data

  // Size properties - Reduced to 240.0 for optimal balance
  final double progressSize = 240.0; // Reduced to 240.0 as requested
  final double strokeWidth = 14.0; // Keep stroke width same

  // Animation controllers
  late AnimationController _progressAnimationController;
  late AnimationController _rotationAnimationController;
  late AnimationController _matrixAnimationController;
  late AnimationController _pulseAnimationController;

  // Matrix bar data - removed (frequency animation bars removed)

  Timer? _progressTimer;

  // Current notification services using real charging data
  final LocalNotificationManager _localNotificationManager =
      LocalNotificationManager();
  final ActiveChargingNotificationService _activeChargingService =
      ActiveChargingNotificationService();

  // FCM subscription service for charging notifications
  final FCMSubscriptionService _fcmSubscriptionService =
      FCMSubscriptionService();

  // REMOVED: Local notification control - using FCM only
  // bool _hasShownInitialNotification = false;

  @override
  void initState() {
    super.initState();
    _chargePercentage = widget.initialCharge;

    // OPTIMIZATION: Ensure background image is ready for immediate display
    _ensureBackgroundImageReady();

    // Matrix values initialization removed (frequency animation bars removed)

    // Initialize animation controllers
    _progressAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2000),
    );

    _rotationAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 8),
    );

    _matrixAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    _pulseAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    );

    _sliderAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _sliderAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _sliderAnimationController,
      curve: Curves.easeInOut,
    ));

    // Start animations
    _progressAnimationController.value = 0.0;
    _progressAnimationController.animateTo(_chargePercentage,
        curve: Curves.easeInOut);
    _rotationAnimationController.repeat();
    _matrixAnimationController.forward();
    _pulseAnimationController.repeat(reverse: true);

    // Matrix animation removed (frequency animation bars removed)

    // Initialize error message to empty
    _errorMessage = null;

    // Initialize charging start time and timer
    _chargingStartTime = DateTime.now();
    _startChargingTimer();

    // Initialize notification services for both modes
    _initializeNotificationServices();

    // CRITICAL: Check if we have verified session data from Step 2 completion
    if (widget.verifiedSessionData != null) {
      debugPrint(
          '✅ VERIFIED SESSION DATA RECEIVED: ${widget.verifiedSessionData}');
      _initializeWithVerifiedSession(widget.verifiedSessionData!);
    } else if (widget.stationUid != null && widget.connectorId != null) {
      debugPrint('🔌 NORMAL MODE: Fetching real charging session data');
      _fetchChargingSession();
    } else {
      debugPrint('⚠️ FALLBACK: No station/connector data, starting simulation');
      _startChargingSimulation();
    }
  }

  /// OPTIMIZATION: Ensure background image is ready for immediate display
  void _ensureBackgroundImageReady() {
    try {
      debugPrint('🖼️ ===== ENSURING BACKGROUND IMAGE IS READY =====');
      debugPrint(
          '🖼️ Checking if background image is cached and ready for display');

      // The image should already be preloaded by the initialization screen
      // This is a fallback to ensure smooth rendering
      precacheImage(
        const AssetImage(
            'assets/images/charging_session _screen_background.png'),
        context,
      ).then((_) {
        debugPrint('✅ Background image confirmed ready for immediate display');
      }).catchError((e) {
        debugPrint('⚠️ Background image cache check failed: $e');
        // This is non-critical - the image will still load, just might have slight delay
      });
    } catch (e) {
      debugPrint('⚠️ Error in background image readiness check: $e');
      // Non-critical error - don't affect app functionality
    }
  }

  /// Initialize notification services with real charging data
  Future<void> _initializeNotificationServices() async {
    try {
      debugPrint('🔔 ===== INITIALIZING CHARGING NOTIFICATION SERVICES =====');
      debugPrint(
          '🔔 Using local notifications with real charging session data');
      debugPrint('🔔 Platform: ${Platform.isAndroid ? "Android" : "Other"}');

      // Initialize local notification manager
      debugPrint('🔔 Initializing local notification manager...');
      await _localNotificationManager.initialize();
      debugPrint('✅ Local notification manager initialized successfully');

      // Initialize active charging notification service
      debugPrint('🔔 Initializing active charging notification service...');
      await _activeChargingService.initialize();
      debugPrint(
          '✅ Active charging notification service initialized successfully');

      // Initialize FCM subscription service for charging notifications
      debugPrint('🔔 Initializing FCM subscription service...');
      await _fcmSubscriptionService.initialize();
      debugPrint('✅ FCM subscription service initialized successfully');

      // REMOVED: Local notification initialization for charging sessions
      // Charging notifications are now handled exclusively through FCM
      debugPrint('🔔 Local charging notifications disabled - using FCM only');

      debugPrint(
          '✅ CHARGING NOTIFICATION SERVICES READY - will show notifications with real API data');
    } catch (e) {
      debugPrint('❌ Error initializing notification services: $e');
      debugPrint('❌ Error type: ${e.runtimeType}');
      debugPrint('❌ Stack trace: ${StackTrace.current}');
    }
  }

  /// Subscribe to FCM notifications for charging session
  Future<void> _subscribeToChargingNotifications(String transactionId) async {
    try {
      debugPrint('🔔 ===== SUBSCRIBING TO FCM CHARGING NOTIFICATIONS =====');
      debugPrint('🔔 Transaction ID: $transactionId');

      // Subscribe to charging notifications
      final subscriptionSuccess = await _fcmSubscriptionService
          .subscribeToChargingNotifications(transactionId);

      if (subscriptionSuccess) {
        debugPrint('✅ Successfully subscribed to FCM charging notifications');
        // FCM subscription successful - no UI popup needed
      } else {
        debugPrint('❌ Failed to subscribe to FCM charging notifications');
        // FCM subscription failed - no UI popup needed
      }
    } catch (e) {
      debugPrint('❌ Error subscribing to FCM charging notifications: $e');
      // FCM subscription error - no UI popup needed
    }
  }

  /// Unsubscribe from FCM notifications when charging session ends
  Future<void> _unsubscribeFromChargingNotifications() async {
    try {
      debugPrint(
          '🔔 ===== UNSUBSCRIBING FROM FCM CHARGING NOTIFICATIONS =====');

      // Unsubscribe from charging notifications
      final unsubscriptionSuccess =
          await _fcmSubscriptionService.unsubscribeFromChargingNotifications();

      if (unsubscriptionSuccess) {
        debugPrint(
            '✅ Successfully unsubscribed from FCM charging notifications');
      } else {
        debugPrint('❌ Failed to unsubscribe from FCM charging notifications');
      }
    } catch (e) {
      debugPrint('❌ Error unsubscribing from FCM charging notifications: $e');
    }
  }

  // Matrix animation method removed (frequency animation bars removed)

  // Start real charging session using the API flow with AUTHENTIC PARAMETERS
  Future<void> _fetchChargingSession() async {
    // Prevent multiple simultaneous requests
    if (_isLoadingSession) return;

    if (!mounted) return;

    setState(() {
      _isLoadingSession = true;
      _errorMessage = null;
    });

    try {
      debugPrint(
          '🚀 Starting real charging session flow with AUTHENTIC PARAMETERS');

      // CRITICAL: Use authentic charging parameters from global service
      final chargingParamsService = ChargingParametersService();

      // Get authentic parameters or use fallbacks
      final authenticChargingValue =
          chargingParamsService.hasAuthenticParameters()
              ? chargingParamsService.getChargingValue()
              : 20.0;
      final authenticChargeType = chargingParamsService.hasAuthenticParameters()
          ? chargingParamsService.getChargeType()
          : 'units';
      final authenticPricePerUnit =
          chargingParamsService.hasAuthenticParameters()
              ? chargingParamsService.getPricePerUnit()
              : 0.25;

      debugPrint('🔌 CHARGING SESSION USING AUTHENTIC PARAMETERS:');
      debugPrint('  Charging Value: $authenticChargingValue');
      debugPrint('  Charge Type: $authenticChargeType');
      debugPrint('  Price Per Unit: ₹$authenticPricePerUnit');
      debugPrint(
          '  Max Power: ${chargingParamsService.hasAuthenticParameters() ? chargingParamsService.getMaxPower() : 0.0}kW');

      // CRITICAL FIX: Do NOT call startCompleteChargingFlow here!
      // The charging flow should already be started from charging_initialization_screen.dart
      // This screen should only display the ongoing charging session data

      debugPrint(
          '🔌 Charging session screen initialized - waiting for real-time data');
      debugPrint('  - EVSE UID: ${widget.stationUid}');
      debugPrint('  - Connector ID: ${widget.connectorId}');
      debugPrint('  - Mode: Normal (Real OCPP Flow)');

      setState(() {
        _isLoadingSession = false;
        _errorMessage = null;

        // Create a placeholder session object that will be updated with real data
        _session = ChargingSession(
          id: 'PENDING-${DateTime.now().millisecondsSinceEpoch}',
          stationUid: widget.stationUid ?? 'UNKNOWN-STATION',
          connectorId: widget.connectorId ?? 'UNKNOWN-CONNECTOR',
          startTime: DateTime.now(),
          currentCharge: _chargePercentage,
          currentPower: 0.0,
          energyDelivered: 0.0,
          cost: 0.0,
          co2Saved: 0.0,
        );
      });

      // The real charging flow is managed by charging_initialization_screen.dart
      // This screen will receive updates through the ChargingFlowManager singleton
      debugPrint(
          '✅ Charging session screen ready to receive real-time updates');

      // REMOVED: Local notification for Normal Mode - using FCM only
      debugPrint('🔔 Normal mode initialized - FCM notifications active');
    } catch (e, stackTrace) {
      debugPrint('❌ Error in charging session flow: $e');
      debugPrint('Stack trace: $stackTrace');

      if (!mounted) return;

      setState(() {
        _isLoadingSession = false;
        _errorMessage = 'Failed to start charging session';
        if (e is SocketException) {
          _errorMessage = 'Network error. Please check your connection.';
        } else if (e is TimeoutException) {
          _errorMessage = 'Request timed out. Please try again.';
        } else if (e is FormatException) {
          _errorMessage = 'Invalid data received from server';
        }

        // NO FALLBACK TO SIMULATION IN NORMAL MODE
        setState(() {
          _isLoadingSession = false;
          _errorMessage =
              'Failed to connect to charging session. Please try again.';
        });
      });
    }
  }

  /// CRITICAL: Initialize charging session with verified session data and start real-time polling
  Future<void> _initializeWithVerifiedSession(Map<String, dynamic> sessionData) async {
    debugPrint('🔧 ===== INITIALIZING WITH VERIFIED SESSION DATA =====');
    debugPrint('🔧 Session Data: $sessionData');

    // ENHANCEMENT: Show modern loading UI during initialization
    setState(() {
      _isChargingTriggerLoading = true;
    });

    try {
      debugPrint('🔧 ===== EXTRACTING ONGOING SESSION DATA =====');
      debugPrint('🔧 Full session data: $sessionData');
      debugPrint('🔧 Available keys: ${sessionData.keys.toList()}');

      // Extract transaction ID - for ongoing sessions, use the 'id' field from the API
      String? transactionId;

      // Check if this is an ongoing session (from dashboard)
      final isOngoingSession = sessionData['is_ongoing_session'] == true;
      debugPrint('🔧 Is ongoing session: $isOngoingSession');

      if (isOngoingSession) {
        // For ongoing sessions, the 'id' field from the API is the transaction ID
        transactionId = sessionData['id']?.toString();
        debugPrint(
            '🔧 ONGOING SESSION: Transaction ID from API id field: $transactionId');
      } else {
        // For new sessions (primary flow), use existing logic
        transactionId = sessionData['id']?.toString() ??
            sessionData['transaction_id']?.toString();
        debugPrint('🔧 NEW SESSION: Transaction ID: $transactionId');
      }

      final authorizationReference =
          sessionData['authorization_reference']?.toString();

      debugPrint('🔧 ===== FINAL EXTRACTED VALUES =====');
      debugPrint('🔧 Transaction ID for Stop API: $transactionId');
      debugPrint(
          '🔧 Authorization Reference for Data Polling: $authorizationReference');
      debugPrint('🔧 Session Type: ${isOngoingSession ? "ONGOING" : "NEW"}');

      // ENHANCEMENT: Check if session data contains initial charging data
      final initialData = sessionData['initial_data'] as Map<String, dynamic>?;
      if (initialData != null) {
        debugPrint('🚀 ===== IMMEDIATE DATA DISPLAY =====');
        debugPrint('🚀 Found initial charging data in session response');
        debugPrint('🚀 Initial Data: $initialData');

        // Immediately populate UI with initial data to eliminate 15-second delay
        _updateChargingDataFromAPI(initialData);
        debugPrint(
            '✅ UI immediately populated with initial data - no 15-second delay!');
      }

      // Validate extracted data
      if (transactionId == null || transactionId.isEmpty) {
        debugPrint('❌ CRITICAL ERROR: Invalid transaction ID in session data');
        debugPrint('❌ Session data keys: ${sessionData.keys.toList()}');
        debugPrint('❌ Session data values: $sessionData');
        setState(() {
          _isLoadingSession = false;
          _isChargingTriggerLoading = false; // ENHANCEMENT: Hide loading UI
          _errorMessage = 'Invalid session data. Please restart charging.';
        });
        return;
      }

      if (authorizationReference == null || authorizationReference.isEmpty) {
        debugPrint(
            '❌ CRITICAL ERROR: Invalid authorization reference in session data');
        debugPrint('❌ Session data keys: ${sessionData.keys.toList()}');
        debugPrint('❌ Session data values: $sessionData');
        setState(() {
          _isLoadingSession = false;
          _isChargingTriggerLoading = false; // ENHANCEMENT: Hide loading UI
          _errorMessage = 'Invalid session data. Please restart charging.';
        });
        return;
      }

      // At this point, both transactionId and authorizationReference are guaranteed to be non-null and non-empty
      final validTransactionId = transactionId;
      final validAuthorizationReference = authorizationReference;

      // Store the verified session data
      setState(() {
        _transactionId = validTransactionId;
        _authorizationReference = validAuthorizationReference;
        _isLoadingSession = false;
        _isChargingTriggerLoading = false; // ENHANCEMENT: Hide loading UI
        _errorMessage = null;

        // Create session object with verified data
        _session = ChargingSession(
          id: validTransactionId,
          stationUid: widget.stationUid ?? 'VERIFIED-STATION',
          connectorId: widget.connectorId ?? 'VERIFIED-CONNECTOR',
          startTime: DateTime.now(),
          currentCharge: _chargePercentage,
          currentPower: 0.0,
          energyDelivered: 0.0,
          cost: 0.0,
          co2Saved: 0.0,
        );
      });

      debugPrint('✅ Session initialized successfully with verified data');
      debugPrint('🚀 ===== STARTING IMMEDIATE REAL-TIME POLLING =====');
      debugPrint('🚀 Authorization Reference: $validAuthorizationReference');
      debugPrint(
          '🚀 Endpoint: GET /user/sessions/on-going-data?authorization_reference=$validAuthorizationReference');

      // CRITICAL: Start real-time polling immediately using the authorization reference
      await _startRealTimePolling(validAuthorizationReference);

      // Subscribe to FCM notifications for this charging session
      _subscribeToChargingNotifications(validTransactionId);

      // REMOVED: Local notification generation - using FCM only
      debugPrint('🔔 Charging session initialized - FCM notifications active');
    } catch (e, stackTrace) {
      debugPrint(
          '❌ CRITICAL ERROR initializing with verified session data: $e');
      debugPrint('❌ Stack trace: $stackTrace');
      setState(() {
        _isLoadingSession = false;
        _isChargingTriggerLoading = false; // ENHANCEMENT: Hide loading UI
        _errorMessage =
            'Failed to initialize charging session. Please try again.';
      });
    }
  }

  /// CRITICAL: Start real-time polling using authorization reference from Step 2
  Future<void> _startRealTimePolling(String authorizationReference) async {
    debugPrint('📊 ===== STARTING STEP 3: REAL-TIME POLLING =====');
    debugPrint('📊 Authorization Reference: $authorizationReference');
    debugPrint('📊 Polling Interval: 15 seconds (OCPP Standard)');
    debugPrint(
        '📊 API Endpoint: GET /user/sessions/on-going-data?authorization_reference=$authorizationReference');

    // ENHANCEMENT: Make first API call immediately to get initial data and populate UI
    debugPrint('🚀 ===== IMMEDIATE FIRST API CALL =====');
    debugPrint('🚀 Making immediate API call to eliminate any delay');

    // Make the first API call immediately without any delay
    if (mounted && _isCharging && context.mounted) {
      await _fetchRealTimeDataImmediate(authorizationReference);
    }

    // Start polling every 10 seconds (OCPP standard interval)
    _progressTimer = Timer.periodic(const Duration(seconds: 10), (timer) async {
      if (!mounted || !_isCharging || !context.mounted) {
        debugPrint(
            '📊 Stopping real-time polling - mounted: $mounted, context.mounted: ${context.mounted}, charging: $_isCharging');
        timer.cancel();
        return;
      }

      _fetchRealTimeData(authorizationReference);
    });

    debugPrint('✅ Step 3 real-time polling started successfully');
    debugPrint('✅ First poll executed immediately, next poll in 10 seconds...');
  }

  /// ENHANCEMENT: Immediate fetch for first API call to eliminate any delay
  Future<void> _fetchRealTimeDataImmediate(
      String authorizationReference) async {
    try {
      debugPrint('🚀 ===== IMMEDIATE FIRST API CALL =====');
      debugPrint('🚀 Timestamp: ${DateTime.now().toIso8601String()}');
      debugPrint(
          '🚀 Fetching initial charging data to populate UI immediately...');
      debugPrint('🚀 This eliminates the empty/zero values issue on screen load');

      // Call the real-time data API using ChargingSessionService directly
      final chargingService = ChargingSessionService();
      final response = await chargingService.getChargingData(
        authorizationReference: authorizationReference,
      );

      if (response.success && response.data != null) {
        debugPrint(
            '🚀 ✅ IMMEDIATE data received successfully - UI will update now!');
        debugPrint('🚀 Data: ${response.data}');
        debugPrint(
            '🚀 Users will see real charging data immediately instead of empty/zero values');
        _updateChargingDataFromAPI(response.data!);
        debugPrint('✅ UI populated immediately - data initialization issue fixed!');
      } else {
        debugPrint(
            '⚠️ Immediate data fetch failed - will retry in polling cycle');
        debugPrint('⚠️ Error message: ${response.message}');
      }
    } catch (e) {
      debugPrint('❌ Error in immediate data fetch: $e');
      debugPrint('❌ Will retry in regular polling cycle');
    }
  }

  /// Fetch real-time data from API (regular polling cycles)
  Future<void> _fetchRealTimeData(String authorizationReference) async {
    try {
      debugPrint('📊 ===== STEP 3 POLLING CYCLE =====');
      debugPrint('📊 Timestamp: ${DateTime.now().toIso8601String()}');
      debugPrint('📊 Polling real-time data from API...');

      // Call the real-time data API using ChargingSessionService directly
      final chargingService = ChargingSessionService();
      final response = await chargingService.getChargingData(
        authorizationReference: authorizationReference,
      );

      if (response.success && response.data != null) {
        debugPrint('📊 ✅ Real-time data received successfully');
        debugPrint('📊 Data: ${response.data}');
        _updateChargingDataFromAPI(response.data!);
      } else {
        debugPrint('⚠️ Real-time data polling failed');
        debugPrint('⚠️ Error message: ${response.message}');
      }
    } catch (e) {
      debugPrint('❌ Error in Step 3 real-time polling: $e');
    }
  }

  /// Update charging data from API response
  void _updateChargingDataFromAPI(Map<String, dynamic> data) {
    if (!mounted) return;

    try {
      debugPrint('📊 ===== REAL-TIME DATA UPDATE =====');
      debugPrint('📊 Timestamp: ${DateTime.now().toIso8601String()}');
      debugPrint('📊 Raw API Data: $data');
      debugPrint('📊 Current charging state: $_isCharging');
      debugPrint('📊 Transaction ID: $_transactionId');

      // Extract charging data from API response
      final soc = data['soc']?.toString() ?? '0';
      final unit = data['unit']?.toString() ?? '0';
      final amount = data['amount']?.toString() ?? '0';
      final co2 = data['co2']?.toString() ?? '0';
      final powerOutput = data['power_output']?.toString() ?? '0';
      final status = data['status']?.toString() ?? '';

      debugPrint('📊 Extracted values:');
      debugPrint('📊 SOC: $soc%');
      debugPrint('📊 Unit: $unit kWh');
      debugPrint('📊 Amount: ₹$amount');
      debugPrint('📊 CO2: $co2 kg');
      debugPrint('📊 Power Output: $powerOutput kW');
      debugPrint('📊 Status: "$status" (raw)');
      debugPrint('📊 Status length: ${status.length}');
      debugPrint('📊 Status bytes: ${status.codeUnits}');

      // ENHANCED: Check if charging session is completed automatically by server
      // Check for multiple possible completion status values
      final statusUpper = status.toUpperCase().trim();
      final isCompleted = statusUpper == 'COMPLETED' ||
          statusUpper == 'COMPLETE' ||
          statusUpper == 'FINISHED' ||
          statusUpper == 'DONE' ||
          statusUpper == 'STOPPED' ||
          statusUpper == 'TERMINATED';

      debugPrint(
          '📊 Status Check: "$status" -> "$statusUpper" | IsCompleted: $isCompleted | IsCharging: $_isCharging');

      if (isCompleted && _isCharging && !_hasNavigatedToBilling) {
        debugPrint('🎉 ===== CHARGING SESSION COMPLETED AUTOMATICALLY =====');
        debugPrint(
            '🎉 Server status changed to: "$status" (normalized: "$statusUpper")');
        debugPrint('🎉 Transaction ID: $_transactionId');
        debugPrint('🎉 Authorization Reference: $_authorizationReference');
        debugPrint('🎉 Current charge: ${(_chargePercentage * 100).toInt()}%');
        debugPrint('🎉 Final amount: ₹$amount');
        debugPrint('🎉 Final energy: $unit kWh');
        debugPrint('🎉 Stopping polling and navigating to billing...');

        // CRITICAL: Set navigation flag immediately to prevent duplicate navigation
        _hasNavigatedToBilling = true;

        // CRITICAL: Stop polling immediately to prevent duplicate calls
        _progressTimer?.cancel();
        debugPrint('✅ Real-time polling stopped');

        // CRITICAL: Update charging state immediately
        setState(() {
          _isCharging = false;
        });
        debugPrint('✅ Charging state updated to false');

        // Hide charging notifications since session is complete
        try {
          _localNotificationManager.cancelNotification(1001);
          debugPrint('✅ Charging notifications hidden');
        } catch (e) {
          debugPrint('⚠️ Error hiding notifications: $e');
        }

        // Show completion message and navigate to billing
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('🎉 Charging completed automatically!'),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
              margin: EdgeInsets.all(16),
              duration: Duration(seconds: 2),
            ),
          );
          debugPrint('✅ Completion message shown');

          // Navigate to billing page after showing the message
          Future.delayed(const Duration(milliseconds: 2500), () {
            if (mounted &&
                context.mounted &&
                !_hasNavigatedToBilling &&
                _isCharging == false) {
              try {
                debugPrint(
                    '🧾 ===== AUTOMATIC COMPLETION: NAVIGATING TO BILLING =====');
                debugPrint('🧾 Transaction ID for billing: $_transactionId');
                debugPrint('🧾 Final billing data will be fetched from server');
                _navigateToBillingPage();
              } catch (e) {
                debugPrint(
                    '❌ Error during automatic navigation to billing: $e');
              }
            } else {
              debugPrint(
                  '⚠️ Navigation skipped - widget not mounted, already navigated, or still charging');
              debugPrint(
                  '⚠️ Mounted: $mounted, HasNavigated: $_hasNavigatedToBilling, IsCharging: $_isCharging');
            }
          });
        }
        return; // CRITICAL: Don't update UI further if completed
      } else if (isCompleted && _hasNavigatedToBilling) {
        debugPrint(
            'ℹ️ Session completed but already navigated to billing - skipping');
        return; // Don't update UI if already navigated
      }

      setState(() {
        // Update charge percentage from SOC
        final socValue = double.tryParse(soc) ?? 0.0;
        _chargePercentage = (socValue / 100.0).clamp(0.0, 1.0);

        // Update display values with real API data
        _currentPower = _formatPowerValue("$powerOutput kW");
        _currentPrice = "₹$amount";
        _co2EmissionSaved = "$co2 kg";
        _energyDelivered = "$unit kWh";

        // Update additional fields with real API data
        _environmentalImpactCO2 = "$co2 kg CO2"; // Format for info badge

        // Calculate ETA based on current charge percentage and power
        _eta = _calculateETA(socValue, double.tryParse(powerOutput) ?? 0.0);

        // Animate to the new percentage
        _progressAnimationController.animateTo(
          _chargePercentage,
          duration: const Duration(milliseconds: 1000),
          curve: Curves.easeInOut,
        );

        // Update session data if available
        if (_session != null) {
          _session = _session!.copyWith(
            currentCharge: _chargePercentage,
            currentPower: double.tryParse(powerOutput) ?? 0.0,
            energyDelivered: double.tryParse(unit) ?? 0.0,
            cost: double.tryParse(amount) ?? 0.0,
            co2Saved: double.tryParse(co2) ?? 0.0,
          );
        }
      });

      // REMOVED: Local notification update - using FCM only
      debugPrint('🔔 Charging data updated - FCM will handle notifications');

      debugPrint('✅ UI updated with real-time charging data');
    } catch (e) {
      debugPrint('❌ Error updating charging data from API: $e');
    }
  }

  // Fallback charging simulation for testing (when no real API data available)
  void _startChargingSimulation() {
    // REMOVED: Local notification for fallback simulation - using FCM only
    debugPrint('🔔 Fallback simulation started - FCM notifications active');

    _progressTimer = Timer.periodic(const Duration(seconds: 2), (timer) {
      if (!_isCharging || !mounted) {
        timer.cancel();
        return;
      }

      setState(() {
        if (_chargePercentage < 1.0) {
          // Add a bit of randomness to make it look more realistic
          double increment = 0.01 + (math.Random().nextDouble() * 0.02);
          _chargePercentage = (_chargePercentage + increment).clamp(0.0, 1.0);

          // Animate to the new percentage
          _progressAnimationController.animateTo(
            _chargePercentage,
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeInOut,
          );

          // Update metrics with realistic simulation data (only for fallback)
          double powerBase = 15.6 + (_chargePercentage * 50);
          _currentPower =
              _formatPowerValue("${powerBase.toStringAsFixed(1)} kW");

          double energyBase =
              _chargePercentage * 32.4; // Realistic energy delivered
          _energyDelivered = "${energyBase.toStringAsFixed(1)} kWh";

          double priceBase = 23.45 + (_chargePercentage * 30);
          _currentPrice = "₹${priceBase.toStringAsFixed(2)}";

          double co2Base = 3.2 + (_chargePercentage * 5);
          _co2EmissionSaved = "${co2Base.toStringAsFixed(1)} kg";

          // Update additional fields for simulation consistency
          _environmentalImpactCO2 = "${co2Base.toStringAsFixed(1)} kg CO2";
          _eta = _calculateETA(_chargePercentage * 100, powerBase);

          // Update session data if available
          if (_session != null) {
            _session = _session!.copyWith(
                currentCharge: _chargePercentage,
                energyDelivered:
                    powerBase, // This is actually power, not energy
                cost: priceBase,
                co2Saved: co2Base);
          }

          // REMOVED: Local notification update - using FCM only
          debugPrint(
              '🔔 Simulation data updated - FCM will handle notifications');
        } else {
          _isCharging = false;
          timer.cancel();
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
              content: Text('✅ Charging completed successfully!'),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
              margin: EdgeInsets.all(16),
              duration: Duration(seconds: 2),
            ));

            // Navigate to billing page after simulation completion
            Future.delayed(const Duration(milliseconds: 2500), () {
              if (mounted && context.mounted) {
                debugPrint(
                    '🧾 ===== SIMULATION COMPLETION: NAVIGATING TO BILLING =====');
                debugPrint('🧾 Transaction ID for billing: $_transactionId');
                debugPrint('🧾 Mode: Normal (Direct mode removed)');

                // For simulation, use a mock transaction ID if none exists
                if (_transactionId == null) {
                  _transactionId =
                      'SIM-${DateTime.now().millisecondsSinceEpoch}';
                  debugPrint(
                      '🧾 Generated simulation transaction ID: $_transactionId');
                }

                _navigateToBillingPage();
              }
            });
          }
        }
      });
    });
  }

  /// Start charging timer to track session duration
  void _startChargingTimer() {
    _timerUpdateTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!mounted || !_isCharging) {
        timer.cancel();
        return;
      }

      if (_chargingStartTime != null) {
        final duration = DateTime.now().difference(_chargingStartTime!);
        setState(() {
          _chargingTimer = _formatDuration(duration);
        });
      }
    });
  }

  /// Format duration to HH:MM:SS format
  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    String hours = twoDigits(duration.inHours);
    String minutes = twoDigits(duration.inMinutes.remainder(60));
    String seconds = twoDigits(duration.inSeconds.remainder(60));
    return "$hours:$minutes:$seconds";
  }

  /// Format power value to exactly 2 decimal places
  String _formatPowerValue(String powerValue) {
    try {
      // Extract numeric value from string (remove 'kW' and other text)
      final numericValue = powerValue.replaceAll(RegExp(r'[^\d.]'), '');
      final power = double.tryParse(numericValue) ?? 0.0;
      return "${power.toStringAsFixed(2)} kW";
    } catch (e) {
      debugPrint('Error formatting power value: $e');
      return "0.00 kW";
    }
  }

  /// Calculate ETA based on current charge percentage and power output
  String _calculateETA(double currentSoc, double powerKw) {
    try {
      // If power is too low or SOC is already high, return default
      if (powerKw <= 0 || currentSoc >= 95) {
        return "0h 0m";
      }

      // Estimate remaining capacity (assuming typical EV battery of 60-80 kWh)
      // This is a rough estimation - in real implementation you'd get battery capacity from API
      double estimatedBatteryCapacity = 70.0; // kWh
      double currentCapacity = (currentSoc / 100.0) * estimatedBatteryCapacity;
      double targetCapacity = 0.95 * estimatedBatteryCapacity; // Charge to 95%
      double remainingCapacity = targetCapacity - currentCapacity;

      if (remainingCapacity <= 0) {
        return "0h 0m";
      }

      // Calculate time in hours
      double hoursToComplete = remainingCapacity / powerKw;

      // Convert to hours and minutes
      int hours = hoursToComplete.floor();
      int minutes = ((hoursToComplete - hours) * 60).round();

      // Format the result
      if (hours > 0) {
        return "${hours}h ${minutes}m";
      } else {
        return "${minutes}m";
      }
    } catch (e) {
      debugPrint('Error calculating ETA: $e');
      return "0h 0m";
    }
  }

  @override
  void dispose() {
    debugPrint('🧹 ===== DISPOSING CHARGING SESSION SCREEN =====');

    // CRITICAL: Stop Step 3 real-time polling
    _progressTimer?.cancel();
    debugPrint('✅ Step 3 real-time polling timer cancelled');

    // Stop charging timer
    _timerUpdateTimer?.cancel();
    debugPrint('✅ Charging timer cancelled');

    // Matrix timer cleanup removed (frequency animation bars removed)

    // Dispose animation controllers
    _progressAnimationController.dispose();
    _rotationAnimationController.dispose();
    _matrixAnimationController.dispose();
    _pulseAnimationController.dispose();
    _sliderAnimationController.dispose();
    debugPrint('✅ All animation controllers disposed');

    // Hide Android notifications
    _localNotificationManager.cancelNotification(1001);

    super.dispose();
    debugPrint('✅ Charging session screen disposed completely');
  }

  Widget _buildHeader() {
    return Container(
      // Maintain header structure but remove back button
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: const SizedBox(
        height: 40, // Maintain header height for layout consistency
      ),
    );
  }

  // STOP CHARGING WITH IMMEDIATE API CALL AND POST-API LOADING DELAY
  void _stopCharging() async {
    // Prevent multiple stop attempts
    if (_hasNavigatedToBilling || !_isCharging || _isStoppingCharging) {
      debugPrint(
          '🛑 Stop charging already in progress or completed - skipping');
      return;
    }

    try {
      debugPrint('🛑 ===== STOP CHARGING FLOW INITIATED =====');
      debugPrint('🛑 User manually stopped charging');
      debugPrint('🛑 Transaction ID: $_transactionId');
      debugPrint('🛑 Mode: Normal (Direct mode removed)');

      // STEP 1: Immediately show loading state and stop charging UI
      setState(() {
        _isCharging = false;
        _isStoppingCharging = true; // Show loading indicator
      });

      // Cancel the progress timer immediately
      _progressTimer?.cancel();

      // STEP 2: Call stop transaction API IMMEDIATELY (no delay before API call)
      if (_transactionId != null && _transactionId!.isNotEmpty) {
        debugPrint('🛑 ===== CALLING STOP TRANSACTION API IMMEDIATELY =====');
        debugPrint('🛑 Transaction ID from ongoing session: $_transactionId');
        debugPrint('🛑 Authorization Reference: $_authorizationReference');
        debugPrint('🛑 Transaction ID Type: ${_transactionId.runtimeType}');
        debugPrint(
            '🛑 Expected API Endpoint: /user/sessions/stop/$_transactionId');
        debugPrint(
            '🛑 Full URL: https://api2.eeil.online/api/v1/user/sessions/stop/$_transactionId');

        try {
          // AWAIT the stop transaction POST response
          final chargingService = ChargingSessionService();
          final stopResponse = await chargingService.stopChargingSession(
            transactionId: _transactionId!,
          );

          debugPrint('🛑 ===== STOP TRANSACTION POST RESPONSE RECEIVED =====');
          debugPrint('🛑 Response Success: ${stopResponse.success}');
          debugPrint('🛑 Response Message: "${stopResponse.message}"');
          debugPrint('🛑 Response Data: ${stopResponse.data}');

          if (stopResponse.success) {
            debugPrint('✅ Stop transaction POST completed successfully');
          } else {
            debugPrint(
                '⚠️ Stop transaction POST failed: ${stopResponse.message}');

            // Handle specific error cases
            if (stopResponse.message
                .toLowerCase()
                .contains('unknown transaction')) {
              debugPrint(
                  '⚠️ Server reports unknown transaction - session may have already ended');
              debugPrint('⚠️ Proceeding to billing page anyway');
            } else if (stopResponse.message
                .toLowerCase()
                .contains('already stopped')) {
              debugPrint(
                  '⚠️ Transaction already stopped - proceeding to billing');
            } else {
              debugPrint(
                  '⚠️ Unexpected stop error - still proceeding to billing');
            }
          }
        } catch (e) {
          debugPrint('❌ Stop transaction POST error: $e');
          debugPrint('❌ Network or API error - still proceeding to billing');
        }
      } else {
        debugPrint('🛑 No valid transaction ID available');
        debugPrint('🛑 Transaction ID: $_transactionId');
        debugPrint('🛑 This might be a simulation or test session');
        // Small delay to simulate API call
        await Future.delayed(const Duration(milliseconds: 500));
      }

      // STEP 3: Navigate immediately after API response (no post-API delay)
      debugPrint('🛑 ===== NAVIGATING IMMEDIATELY AFTER API RESPONSE =====');
      debugPrint('🛑 API call completed, proceeding directly to billing page');
      debugPrint('🛑 Billing page will handle the 5-second processing delay');

      if (!mounted) {
        debugPrint(
            '🛑 Widget unmounted after API call, aborting navigation');
        return;
      }

      // STEP 4: Set navigation flag just before navigation to prevent duplicates
      debugPrint('📄 ===== NAVIGATING TO BILLING PAGE =====');
      debugPrint('📄 Setting navigation flag and proceeding to billing');
      debugPrint('📄 Transaction ID: $_transactionId');
      debugPrint('📄 Billing page will handle 5-second delay before billing API');

      // FIXED: Set navigation flag just before navigation, not at the beginning
      _hasNavigatedToBilling = true;

      // Unsubscribe from FCM notifications before navigating to billing
      await _unsubscribeFromChargingNotifications();

      // STEP 4: Navigate to billing page immediately after successful stop API response
      _navigateToBillingPage();
    } catch (e) {
      debugPrint('❌ Error in stop charging flow: $e');
      if (mounted) {
        // Reset loading state on error
        setState(() {
          _isStoppingCharging = false;
          _isCharging = true; // Restore charging state on error
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error during stop and billing: $e'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            margin: const EdgeInsets.all(16),
          ),
        );
        Navigator.pop(context);
      }
    }
  }

  // Modern Slide to Stop Widget with Enhanced Visual Design
  Widget _buildSlideToStopWidget() {
    // Calculate progress percentage for color transitions
    double maxPosition = MediaQuery.of(context).size.width - 120;
    double progressPercentage = (_sliderPosition / maxPosition).clamp(0.0, 1.0);

    return AnimatedBuilder(
      animation: _sliderAnimation,
      builder: (context, child) {
        return Container(
          width: double.infinity,
          height: 64, // Reduced from 80px to 64px
          decoration: BoxDecoration(
            // Solid white background with subtle shadow for contrast against green gradient
            color: Colors.white,
            borderRadius:
                BorderRadius.circular(32), // Adjusted to maintain proportion
            border: Border.all(
              color:
                  const Color(0xFFE5E7EB), // Light gray border for definition
              width: 2,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.15),
                blurRadius: 20,
                offset: const Offset(0, 8),
                spreadRadius: 0,
              ),
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 6,
                offset: const Offset(0, 2),
                spreadRadius: 0,
              ),
            ],
          ),
          child: Stack(
            children: [
              // Base track with subtle gray background
              Container(
                width: double.infinity,
                height: 64, // Reduced from 80px to 64px
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(
                      32), // Adjusted to maintain proportion
                  color: const Color(0xFFF8FAFC), // Very light gray background
                ),
              ),
              // Progress fill with white-to-red gradient based on slide position
              AnimatedContainer(
                duration: const Duration(milliseconds: 100),
                width:
                    (_sliderPosition + 56) * 1.1, // Adjusted for smaller button
                height: 64, // Reduced from 80px to 64px
                decoration: BoxDecoration(
                  borderRadius:
                      BorderRadius.circular(32), // Adjusted to match container
                  gradient: LinearGradient(
                    colors: [
                      // White to red progression following iOS Material Design
                      Colors.white,
                      Color.lerp(Colors.white, const Color(0xFFEF4444),
                          progressPercentage * 0.3)!,
                      Color.lerp(const Color(0xFFEF4444),
                          const Color(0xFFDC2626), progressPercentage)!,
                    ],
                    stops: const [0.0, 0.6, 1.0],
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                  ),
                ),
              ),
              // Enhanced slide text with better contrast and visibility
              Center(
                child: AnimatedOpacity(
                  duration: const Duration(milliseconds: 200),
                  opacity: _isSliding ? 0.4 : 1.0,
                  child: Text(
                    // FIXED: Show appropriate text based on charging state and slider position
                    _isStoppingCharging
                        ? 'STOPPING CHARGING...'
                        : !_isCharging && _hasNavigatedToBilling
                            ? 'STOPPING...'
                            : _sliderPosition > 100
                                ? 'RELEASE TO STOP'
                                : 'SLIDE TO STOP',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight
                          .w700, // Increased weight for better visibility
                      color: progressPercentage > 0.3
                          ? Colors.white // White text on red background
                          : const Color(
                              0xFF374151), // Dark gray text on light background
                      letterSpacing: 1.2,
                      shadows: progressPercentage > 0.3
                          ? [
                              Shadow(
                                color: Colors.black.withValues(alpha: 0.3),
                                offset: const Offset(0, 1),
                                blurRadius: 2,
                              ),
                            ]
                          : null,
                    ),
                  ),
                ),
              ),
              // Draggable power button
              AnimatedPositioned(
                duration: _isSliding
                    ? Duration.zero
                    : const Duration(milliseconds: 300),
                curve: Curves.easeOut,
                left: 4 + _sliderPosition,
                top: 4,
                child: GestureDetector(
                  onPanStart: _isStoppingCharging
                      ? null
                      : (details) {
                          setState(() {
                            _isSliding = true;
                          });
                        },
                  onPanUpdate: _isStoppingCharging
                      ? null
                      : (details) {
                          _handleSliderDrag(details);
                        },
                  onPanEnd: _isStoppingCharging
                      ? null
                      : (details) {
                          _handleSliderDragEnd(details);
                        },
                  child: Container(
                    width: 56, // Reduced from 72px to 56px
                    height: 56, // Reduced from 72px to 56px
                    decoration: BoxDecoration(
                      // Enhanced button design following iOS Material Design
                      color: Colors.white,
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: progressPercentage > 0.9
                            ? AppThemes.primaryColor // Success green when ready (90% threshold)
                            : const Color(0xFFEF4444), // Primary red
                        width: 3,
                      ),
                      boxShadow: [
                        // Primary shadow for depth
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.15),
                          blurRadius: 12,
                          offset: const Offset(0, 4),
                          spreadRadius: 0,
                        ),
                        // Secondary shadow for iOS-style elevation
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.08),
                          blurRadius: 6,
                          offset: const Offset(0, 2),
                          spreadRadius: 0,
                        ),
                        // Colored glow effect based on state
                        BoxShadow(
                          color: (progressPercentage > 0.9
                                  ? const Color(0xFF10B981)
                                  : const Color(0xFFEF4444))
                              .withValues(alpha: 0.25),
                          blurRadius: 16,
                          offset: const Offset(0, 0),
                          spreadRadius: 1,
                        ),
                      ],
                    ),
                    child: Icon(
                      Icons.power_settings_new,
                      color: progressPercentage > 0.9
                          ? const Color(
                              0xFF10B981) // Success green (90% threshold)
                          : const Color(0xFFEF4444), // Primary red
                      size:
                          26, // Reduced from 32px to 26px to match smaller button
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // Handle slider drag movement
  void _handleSliderDrag(DragUpdateDetails details) {
    setState(() {
      // Calculate new position based on drag delta
      double newPosition = _sliderPosition + details.delta.dx;

      // Get the container width (adjusted for smaller button: 56px + padding)
      double maxPosition = MediaQuery.of(context).size.width -
          104; // Adjusted for smaller button (56px + 48px padding)

      // Constrain the position
      _sliderPosition = newPosition.clamp(0.0, maxPosition);
    });
  }

  // Handle slider drag end - trigger stop charging
  void _handleSliderDragEnd(DragEndDetails details) {
    setState(() {
      _isSliding = false;
    });

    // Check if slider was dragged far enough to the right (more than 90% of available space)
    double maxPosition =
        MediaQuery.of(context).size.width - 104; // Adjusted for smaller button
    double threshold =
        maxPosition * 0.9; // Changed from 0.7 to 0.9 (90% threshold)

    if (_sliderPosition > threshold &&
        !_hasNavigatedToBilling &&
        _isCharging &&
        !_isStoppingCharging) {
      debugPrint('🛑 SLIDE TO STOP TRIGGERED!');
      debugPrint('🛑 Slider position: $_sliderPosition, Threshold: $threshold');
      debugPrint('🛑 Transaction ID: $_transactionId');
      debugPrint('🛑 Charging state: $_isCharging');
      debugPrint('🛑 Navigation state: $_hasNavigatedToBilling');

      // FIXED: Keep slider in final position instead of resetting immediately
      setState(() {
        _sliderPosition =
            maxPosition; // Keep at maximum position to show "stopped" state
      });

      // Call stop charging without immediately setting navigation flag
      _stopCharging();
    } else {
      // Animate back to start position
      _sliderAnimationController.forward().then((_) {
        setState(() {
          _sliderPosition = 0.0;
        });
        _sliderAnimationController.reset();
      });

      // Log why stop was not triggered
      if (_hasNavigatedToBilling) {
        debugPrint('🛑 Stop not triggered: Already navigated to billing');
      } else if (!_isCharging) {
        debugPrint('🛑 Stop not triggered: Not currently charging');
      } else if (_sliderPosition <= threshold) {
        debugPrint(
            '🛑 Stop not triggered: Slider not dragged far enough ($_sliderPosition <= $threshold)');
      }
    }
  }

  /// Navigate to billing page with transaction details
  void _navigateToBillingPage() {
    debugPrint('🧾 ===== NAVIGATING TO BILLING PAGE =====');
    debugPrint('🧾 Transaction ID: $_transactionId');
    debugPrint('🧾 Station UID: ${widget.stationUid}');
    debugPrint('🧾 Mode: Normal (Direct mode removed)');
    debugPrint('🧾 Widget mounted: $mounted');
    debugPrint('🧾 Navigation flag: $_hasNavigatedToBilling');

    // FIXED: Simplified navigation check - only check if widget is mounted and we have transaction ID
    if (_transactionId != null && mounted) {
      try {
        debugPrint('🧾 ✅ Conditions met - proceeding with navigation');
        debugPrint(
            '🧾 Creating BillingDetailsPage with sourceScreen: charging_session');

        Navigator.pushReplacement(
          context,
          PageRouteBuilder(
            pageBuilder: (context, animation, secondaryAnimation) =>
                BillingDetailsPage(
              transactionId: _transactionId!,
              stationUid: widget.stationUid,
              stationName: null, // No station name available in this widget
              sourceScreen:
                  'charging_session', // Identify source for conditional navigation
            ),
            transitionsBuilder:
                (context, animation, secondaryAnimation, child) {
              return FadeTransition(
                opacity: animation,
                child: child,
              );
            },
            transitionDuration: const Duration(milliseconds: 300),
          ),
        );
        debugPrint('✅ Navigation to billing page initiated successfully');
      } catch (e) {
        debugPrint('❌ Error during navigation to billing page: $e');
        debugPrint('❌ Error type: ${e.runtimeType}');
        debugPrint('❌ Error details: ${e.toString()}');

        // Reset navigation flag on error to allow retry
        _hasNavigatedToBilling = false;

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Navigation error: $e'),
              backgroundColor: Colors.red,
              action: SnackBarAction(
                label: 'Retry',
                textColor: Colors.white,
                onPressed: () {
                  // Allow retry by calling navigation again
                  _navigateToBillingPage();
                },
              ),
            ),
          );
        }
      }
    } else {
      debugPrint('❌ Cannot navigate to billing page');
      debugPrint('❌ Transaction ID: $_transactionId');
      debugPrint('❌ Widget mounted: $mounted');
      debugPrint('❌ Transaction ID is null: ${_transactionId == null}');
      debugPrint('❌ Widget not mounted: ${!mounted}');

      // Reset navigation flag since navigation failed
      _hasNavigatedToBilling = false;

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_transactionId == null
                ? 'Unable to load billing details - no transaction ID'
                : 'Unable to navigate - widget not mounted'),
            backgroundColor: Colors.red,
            action: SnackBarAction(
              label: 'Go Back',
              textColor: Colors.white,
              onPressed: () {
                Navigator.pop(context);
              },
            ),
          ),
        );
      }
    }
  }

  // We'll use the existing strokeWidth field but modify it in initState

  Widget _buildVehicleVisualization() {
    // Removed matrix bar visualization (frequency animation bars)
    // Return minimal empty container
    return Container(
      height: 20, // Minimal height since no content
      margin: const EdgeInsets.symmetric(vertical: 2.0),
      child: const SizedBox.shrink(), // Empty widget
    );
  }

  Widget _buildChargingProgress() {
    return Container(
      margin: const EdgeInsets.symmetric(
          vertical: 8), // Optimized for bottom layout
      width: progressSize, // Use full progress size for better visibility
      height: progressSize, // Use full progress size for better visibility
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Enhanced outer glow effect with larger spread
          Container(
            width: progressSize + 40, // Increased glow area
            height: progressSize + 40, // Increased glow area
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF22C55E)
                      .withValues(alpha: 0.3), // Slightly more visible glow
                  blurRadius: 40, // Increased blur radius
                  spreadRadius: 20, // Increased spread radius
                ),
              ],
            ),
          ),

          // Enlarged background circle with increased spacing from progress bar
          Container(
            width: progressSize -
                60, // Increased spacing from 40 to 60 for better separation
            height: progressSize -
                60, // Increased spacing from 40 to 60 for better separation
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.white
                  .withValues(alpha: 0.12), // Slightly more visible background
              border: Border.all(
                color:
                    Colors.white.withValues(alpha: 0.25), // More visible border
                width: 2.5, // Slightly thicker border
              ),
            ),
          ),

          // Enhanced progress indicator with larger size
          AnimatedBuilder(
            animation: Listenable.merge([
              _progressAnimationController,
              _rotationAnimationController,
            ]),
            builder: (context, child) {
              return CustomPaint(
                size: Size(progressSize,
                    progressSize), // Full size for better visibility
                painter: CircularProgressPainter(
                  progress: _progressAnimationController.value,
                  strokeWidth: strokeWidth, // Enhanced stroke width
                  rotationAngle:
                      _rotationAnimationController.value * 2 * math.pi,
                ),
              );
            },
          ),

          // Enhanced center content with proportional scaling
          Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Enhanced battery icon
              Container(
                width: 44, // Reduced from 50 to 44 for better proportion
                height: 44, // Reduced from 50 to 44 for better proportion
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.18),
                  borderRadius: BorderRadius.circular(
                      12), // Reduced border radius proportionally
                ),
                child: Center(
                  child: Transform.rotate(
                    angle: -math.pi / 2,
                    child: const Icon(
                      Icons.battery_charging_full_rounded,
                      color: Colors.white,
                      size: 28, // Reduced from 32 to 28 for better proportion
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 14), // Increased spacing
              // Enhanced percentage text
              AnimatedBuilder(
                animation: _progressAnimationController,
                builder: (context, child) {
                  return Text(
                    '${(_progressAnimationController.value * 100).toInt()}%',
                    style: const TextStyle(
                      fontSize: 52, // Back to original size
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      height: 1,
                    ),
                  );
                },
              ),
              const SizedBox(height: 6), // Increased spacing
              // Enhanced status text
              Text(
                _isCharging ? 'Charging' : 'Charged',
                style: TextStyle(
                  fontSize: 20, // Back to original size
                  fontWeight: FontWeight.w500,
                  color: Colors.white.withValues(alpha: 0.9),
                  letterSpacing: 1.2,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMetricsGrid() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 2),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: _buildMetricCard(
                  icon: Icons.currency_rupee_rounded,
                  title: 'Current Price',
                  value: _currentPrice,
                  gradient: const LinearGradient(
                    colors: [Color(0xFF4ADE80), Color(0xFF22C55E)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildMetricCard(
                  icon: Icons.bolt_rounded,
                  title: 'Current Power',
                  value: _currentPower,
                  gradient: const LinearGradient(
                    colors: [Color(0xFF60A5FA), Color(0xFF3B82F6)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: _buildMetricCard(
                  icon: Icons.battery_charging_full_rounded,
                  title: 'Energy Delivered',
                  value: _energyDelivered,
                  gradient: const LinearGradient(
                    colors: [Color(0xFFFBBF24), Color(0xFFF59E0B)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildMetricCard(
                  icon: Icons.eco_rounded,
                  title: 'CO₂ Saved',
                  value: _co2EmissionSaved,
                  gradient: const LinearGradient(
                    colors: [Color(0xFF34D399), Color(0xFF10B981)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMetricCard({
    required IconData icon,
    required String title,
    required String value,
    required LinearGradient gradient,
  }) {
    return Container(
      height:
          85, // Increased height to fix pixel alignment and center values properly
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Stack(
        children: [
          // Gradient overlay
          Positioned(
            top: 0,
            right: 0,
            child: Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                gradient: gradient.scale(0.1),
                borderRadius: const BorderRadius.only(
                  topRight: Radius.circular(16),
                  bottomLeft: Radius.circular(60),
                ),
              ),
            ),
          ),
          // Content with improved alignment
          Padding(
            padding: const EdgeInsets.all(
                12), // Increased padding for better spacing
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  width: 28, // Slightly larger icon container
                  height: 28,
                  decoration: BoxDecoration(
                    gradient: gradient,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Icon(
                    icon,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
                // Better spacing and alignment for text content
                Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 11,
                        fontWeight: FontWeight.w500,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 2), // Slightly more spacing
                    Text(
                      value,
                      style: const TextStyle(
                        color: Colors.black87,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoBadges() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 1),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          Flexible(
            child: _buildInfoBadge(
              icon: Icons.eco_rounded,
              text: 'Saved $_environmentalImpactCO2',
              color: const Color(0xFF10B981),
              backgroundColor: const Color(0xFF10B981).withValues(alpha: 0.1),
            ),
          ),
          const SizedBox(width: 8),
          Flexible(
            child: _buildInfoBadge(
              icon: Icons.access_time_rounded,
              text: 'ETA: $_eta',
              color: const Color(0xFF3B82F6),
              backgroundColor: const Color(0xFF3B82F6).withValues(alpha: 0.1),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoBadge({
    required IconData icon,
    required String text,
    required Color color,
    required Color backgroundColor,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            color: color,
            size: 12,
          ),
          const SizedBox(width: 4),
          Flexible(
            child: Text(
              text,
              style: TextStyle(
                color: color,
                fontSize: 11,
                fontWeight: FontWeight.w600,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionSlider() {
    return Container(
      margin: const EdgeInsets.fromLTRB(16, 8, 16, 16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Modern Slide to Stop Widget
          _buildSlideToStopWidget(),
        ],
      ),
    );
  }

  /// ENHANCEMENT: Modern loading overlay with blur effect for stop charging
  Widget _buildModernStopChargingLoader() {
    return Container(
      color: Colors.black.withValues(alpha: 0.4),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 8.0, sigmaY: 8.0),
        child: Container(
          color: Colors.black.withValues(alpha: 0.2),
          child: Center(
            child: Container(
              padding: const EdgeInsets.all(32),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.95),
                borderRadius: BorderRadius.circular(24),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.15),
                    blurRadius: 30,
                    offset: const Offset(0, 10),
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Modern loading indicator with custom animation
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: const LinearGradient(
                        colors: [Color(0xFFEF4444), Color(0xFFDC2626)],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: const Color(0xFFEF4444).withValues(alpha: 0.3),
                          blurRadius: 20,
                          offset: const Offset(0, 8),
                          spreadRadius: 0,
                        ),
                      ],
                    ),
                    child: const Center(
                      child: SizedBox(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(
                          valueColor:
                              AlwaysStoppedAnimation<Color>(Colors.white),
                          strokeWidth: 3,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),
                  // Modern typography
                  const Text(
                    'Stopping Charging',
                    style: TextStyle(
                      color: Color(0xFF1F2937),
                      fontSize: 20,
                      fontWeight: FontWeight.w700,
                      letterSpacing: -0.5,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Processing your request...',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 16),
                  // Progress indicator
                  Container(
                    width: 200,
                    height: 4,
                    decoration: BoxDecoration(
                      color: Colors.grey[200],
                      borderRadius: BorderRadius.circular(2),
                    ),
                    child: AnimatedBuilder(
                      animation: _pulseAnimationController,
                      builder: (context, child) {
                        return Container(
                          width: 200 * _pulseAnimationController.value,
                          height: 4,
                          decoration: BoxDecoration(
                            gradient: const LinearGradient(
                              colors: [Color(0xFFEF4444), Color(0xFFDC2626)],
                            ),
                            borderRadius: BorderRadius.circular(2),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// ENHANCEMENT: Modern loading overlay with blur effect for charging trigger actions
  Widget _buildModernChargingTriggerLoader() {
    return Container(
      color: Colors.black.withValues(alpha: 0.4),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 8.0, sigmaY: 8.0),
        child: Container(
          color: Colors.black.withValues(alpha: 0.2),
          child: Center(
            child: Container(
              padding: const EdgeInsets.all(32),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.95),
                borderRadius: BorderRadius.circular(24),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.15),
                    blurRadius: 30,
                    offset: const Offset(0, 10),
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Modern loading indicator with green theme for charging
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: const LinearGradient(
                        colors: [Color(0xFF22C55E), Color(0xFF16A34A)],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: const Color(0xFF22C55E).withValues(alpha: 0.3),
                          blurRadius: 20,
                          offset: const Offset(0, 8),
                          spreadRadius: 0,
                        ),
                      ],
                    ),
                    child: const Center(
                      child: SizedBox(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(
                          valueColor:
                              AlwaysStoppedAnimation<Color>(Colors.white),
                          strokeWidth: 3,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),
                  // Modern typography
                  const Text(
                    'Initializing Charging',
                    style: TextStyle(
                      color: Color(0xFF1F2937),
                      fontSize: 20,
                      fontWeight: FontWeight.w700,
                      letterSpacing: -0.5,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Setting up your charging session...',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 16),
                  // Progress indicator
                  Container(
                    width: 200,
                    height: 4,
                    decoration: BoxDecoration(
                      color: Colors.grey[200],
                      borderRadius: BorderRadius.circular(2),
                    ),
                    child: AnimatedBuilder(
                      animation: _pulseAnimationController,
                      builder: (context, child) {
                        return Container(
                          width: 200 * _pulseAnimationController.value,
                          height: 4,
                          decoration: BoxDecoration(
                            gradient: const LinearGradient(
                              colors: [Color(0xFF22C55E), Color(0xFF16A34A)],
                            ),
                            borderRadius: BorderRadius.circular(2),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // Main content
          Container(
            decoration: const BoxDecoration(
              image: DecorationImage(
                image: AssetImage(
                    'assets/images/charging_session _screen_background.png'),
                fit: BoxFit.cover, // Reverted to cover for full screen coverage
              ),
            ),
            child: SafeArea(
              child: Column(
                children: [
                  _buildHeader(),

                  if (_errorMessage != null)
                    Container(
                      margin: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 4),
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        color: Colors.red.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Colors.red.withValues(alpha: 0.3),
                          width: 1,
                        ),
                      ),
                      child: Row(
                        children: [
                          const Icon(
                            Icons.error_outline_rounded,
                            color: Colors.red,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              _errorMessage!,
                              style: const TextStyle(
                                color: Colors.red,
                                fontSize: 13,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                  // Main content area with bottom-heavy layout
                  Expanded(
                    child: Column(
                      children: [
                        // Increased top spacer to push content further toward bottom
                        Expanded(
                          flex: 6, // Increased from 3 to 6 for more top space
                          child: Container(), // Empty space at top
                        ),

                        // Vehicle visualization - minimal space (empty container)
                        _buildVehicleVisualization(),

                        // Reduced spacer before main content
                        const SizedBox(height: 12), // Reduced from 20 to 12

                        // Charging progress - positioned closer to bottom
                        _buildChargingProgress(),

                        // Optimized spacing between progress and metrics
                        const SizedBox(height: 18), // Reduced from 24 to 18

                        // Metrics grid - positioned toward bottom
                        _buildMetricsGrid(),

                        // Optimized spacing between metrics and info badges
                        const SizedBox(height: 12), // Reduced from 16 to 12

                        // Info badges - close to bottom
                        _buildInfoBadges(),

                        // Optimized bottom padding to maintain spacing from action slider
                        const SizedBox(height: 16), // Reduced from 20 to 16
                      ],
                    ),
                  ),

                  // Action slider - fixed at bottom
                  _buildActionSlider(),
                ],
              ),
            ),
          ),

          // ENHANCEMENT: Modern loading overlay when stopping charging
          if (_isStoppingCharging) _buildModernStopChargingLoader(),

          // ENHANCEMENT: Modern loading overlay for charging trigger actions
          if (_isChargingTriggerLoading) _buildModernChargingTriggerLoader(),
        ],
      ),
    );
  }
}
