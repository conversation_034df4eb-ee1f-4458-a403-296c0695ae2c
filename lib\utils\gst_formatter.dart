import 'package:flutter/services.dart';

/// Utility class for GST number formatting and validation
class GSTFormatter {
  /// GST number regex pattern for validation
  /// Format: 22AAAAA0000A1Z5 (15 characters)
  /// - 2 digits: State code
  /// - 5 letters: PAN first 5 characters
  /// - 4 digits: Entity number
  /// - 1 letter: Check sum
  /// - 1 letter/digit: Additional check
  /// - 1 letter: 'Z' (fixed)
  /// - 1 letter/digit: Check digit
  static final RegExp _gstRegex = RegExp(
    r'^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$'
  );

  /// Validate GST number format
  static bool isValidGST(String? gstNumber) {
    if (gstNumber == null || gstNumber.isEmpty) {
      return false;
    }

    // Remove any spaces or special characters
    final cleanGST = gstNumber.replaceAll(RegExp(r'[^A-Z0-9]'), '');

    // Check length
    if (cleanGST.length != 15) {
      return false;
    }

    // Check format
    return _gstRegex.hasMatch(cleanGST);
  }

  /// Format GST number with proper spacing for display
  /// Input: "22AAAAA0000A1Z5"
  /// Output: "22 AAAAA 0000 A1Z5"
  static String formatGSTForDisplay(String? gstNumber) {
    if (gstNumber == null || gstNumber.isEmpty) {
      return '';
    }

    // Remove any existing formatting
    final cleanGST = gstNumber.replaceAll(RegExp(r'[^A-Z0-9]'), '');

    if (cleanGST.length != 15) {
      return gstNumber; // Return original if not valid length
    }

    // Format: XX XXXXX XXXX XXXX
    return '${cleanGST.substring(0, 2)} ${cleanGST.substring(2, 7)} ${cleanGST.substring(7, 11)} ${cleanGST.substring(11, 15)}';
  }

  /// Clean GST number for API submission (remove spaces and formatting)
  /// Input: "22 AAAAA 0000 A1Z5"
  /// Output: "22AAAAA0000A1Z5"
  static String cleanGSTForAPI(String? gstNumber) {
    if (gstNumber == null || gstNumber.isEmpty) {
      return '';
    }

    return gstNumber.replaceAll(RegExp(r'[^A-Z0-9]'), '').toUpperCase();
  }

  /// Extract state code from GST number
  static String? getStateCodeFromGST(String? gstNumber) {
    if (gstNumber == null || gstNumber.isEmpty) {
      return null;
    }

    final cleanGST = cleanGSTForAPI(gstNumber);
    if (cleanGST.length >= 2) {
      return cleanGST.substring(0, 2);
    }

    return null;
  }

  /// Extract PAN from GST number
  static String? getPANFromGST(String? gstNumber) {
    if (gstNumber == null || gstNumber.isEmpty) {
      return null;
    }

    final cleanGST = cleanGSTForAPI(gstNumber);
    if (cleanGST.length >= 12) {
      // PAN is characters 2-11 (10 characters)
      return cleanGST.substring(2, 12);
    }

    return null;
  }

  /// Get GST validation error message
  static String? getGSTValidationError(String? gstNumber) {
    if (gstNumber == null || gstNumber.isEmpty) {
      return null; // No error for empty GST
    }

    final cleanGST = cleanGSTForAPI(gstNumber);

    if (cleanGST.length != 15) {
      return 'GST number must be exactly 15 characters';
    }

    if (!_gstRegex.hasMatch(cleanGST)) {
      return 'Invalid GST number format';
    }

    return null; // No error
  }

  /// Create a text input formatter for GST number input (without automatic spacing)
  static List<TextInputFormatter> getGSTInputFormatters() {
    return [
      LengthLimitingTextInputFormatter(15), // 15 chars only, no spaces
      FilteringTextInputFormatter.allow(RegExp(r'[A-Z0-9]')),
      _UpperCaseTextFormatter(),
    ];
  }

  /// Parse GST number from API JSON data
  static Map<String, String?> parseGSTFromAPIData(Map<String, dynamic> apiData) {
    final gstNumber = apiData['gst_no'] as String?;
    final businessName = apiData['business_name'] as String?;

    return {
      'gst_number': gstNumber,
      'business_name': businessName,
      'formatted_gst': formatGSTForDisplay(gstNumber),
      'is_valid': isValidGST(gstNumber).toString(),
      'state_code': getStateCodeFromGST(gstNumber),
      'pan': getPANFromGST(gstNumber),
    };
  }

  /// Create GST display widget data
  static Map<String, dynamic> createGSTDisplayData(String? gstNumber, String? businessName) {
    return {
      'has_gst': gstNumber != null && gstNumber.isNotEmpty,
      'gst_number': gstNumber,
      'formatted_gst': formatGSTForDisplay(gstNumber),
      'business_name': businessName,
      'is_valid': isValidGST(gstNumber),
      'validation_error': getGSTValidationError(gstNumber),
      'state_code': getStateCodeFromGST(gstNumber),
      'pan': getPANFromGST(gstNumber),
    };
  }
}

/// Custom text input formatter for uppercase GST number input (no spacing)
class _UpperCaseTextFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    final text = newValue.text.toUpperCase();

    return TextEditingValue(
      text: text,
      selection: TextSelection.collapsed(offset: text.length),
    );
  }
}

/// GST state codes mapping (for reference)
class GSTStateCodes {
  static const Map<String, String> stateCodes = {
    '01': 'Jammu and Kashmir',
    '02': 'Himachal Pradesh',
    '03': 'Punjab',
    '04': 'Chandigarh',
    '05': 'Uttarakhand',
    '06': 'Haryana',
    '07': 'Delhi',
    '08': 'Rajasthan',
    '09': 'Uttar Pradesh',
    '10': 'Bihar',
    '11': 'Sikkim',
    '12': 'Arunachal Pradesh',
    '13': 'Nagaland',
    '14': 'Manipur',
    '15': 'Mizoram',
    '16': 'Tripura',
    '17': 'Meghalaya',
    '18': 'Assam',
    '19': 'West Bengal',
    '20': 'Jharkhand',
    '21': 'Odisha',
    '22': 'Chhattisgarh',
    '23': 'Madhya Pradesh',
    '24': 'Gujarat',
    '25': 'Daman and Diu',
    '26': 'Dadra and Nagar Haveli',
    '27': 'Maharashtra',
    '28': 'Andhra Pradesh',
    '29': 'Karnataka',
    '30': 'Goa',
    '31': 'Lakshadweep',
    '32': 'Kerala',
    '33': 'Tamil Nadu',
    '34': 'Puducherry',
    '35': 'Andaman and Nicobar Islands',
    '36': 'Telangana',
    '37': 'Andhra Pradesh (New)',
    '38': 'Ladakh',
  };

  /// Get state name from GST state code
  static String? getStateName(String? stateCode) {
    if (stateCode == null || stateCode.isEmpty) {
      return null;
    }
    return stateCodes[stateCode];
  }
}
