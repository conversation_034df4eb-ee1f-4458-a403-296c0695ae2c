import 'dart:math' as math;
import 'package:flutter/material.dart';

class AnimatedBackground extends StatefulWidget {
  final Color primaryColor;
  final Color secondaryColor;
  final int numberOfElements;

  const AnimatedBackground({
    super.key,
    required this.primaryColor,
    required this.secondaryColor,
    this.numberOfElements = 15,
  });

  @override
  AnimatedBackgroundState createState() => AnimatedBackgroundState();
}

class AnimatedBackgroundState extends State<AnimatedBackground>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late List<Color> _bgColors;

  @override
  void initState() {
    super.initState();

    // Initialize background colors
    _bgColors = [
      widget.primaryColor.withAlpha(13), // 0.05 * 255 = ~13
      widget.primaryColor.withAlpha(20), // 0.08 * 255 = ~20
      widget.primaryColor.withAlpha(31), // 0.12 * 255 = ~31
      widget.secondaryColor.withAlpha(13), // 0.05 * 255 = ~13
      widget.secondaryColor.withAlpha(20), // 0.08 * 255 = ~20
    ];

    // Initialize animation controller with a longer duration for continuous animation
    _animationController = AnimationController(
      vsync: this,
      duration:
          const Duration(seconds: 20), // Longer duration for smoother animation
    );

    // Make the animation repeat continuously
    _animationController.repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final screenWidth = screenSize.width;
    final screenHeight = screenSize.height;

    return Stack(
      children: [
        // Background static circles
        ..._buildBackgroundCircles(screenWidth, screenHeight),

        // Animated floating elements
        ..._buildFloatingElements(screenWidth, screenHeight),

        // Animated wave effect
        _buildWaveEffect(screenWidth, screenHeight),
      ],
    );
  }

  // Build static background circles
  List<Widget> _buildBackgroundCircles(
      double screenWidth, double screenHeight) {
    return [
      // Top left circle
      Positioned(
        top: -screenWidth * 0.3,
        left: -screenWidth * 0.3,
        child: Container(
          width: screenWidth * 0.6,
          height: screenWidth * 0.6,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: _bgColors[0],
          ),
        ),
      ),

      // Top right circle
      Positioned(
        top: screenHeight * 0.05,
        right: -screenWidth * 0.2,
        child: Container(
          width: screenWidth * 0.4,
          height: screenWidth * 0.4,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: _bgColors[1],
          ),
        ),
      ),

      // Bottom right circle
      Positioned(
        bottom: -screenWidth * 0.2,
        right: -screenWidth * 0.1,
        child: Container(
          width: screenWidth * 0.5,
          height: screenWidth * 0.5,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: _bgColors[2],
          ),
        ),
      ),

      // Bottom left small circle
      Positioned(
        bottom: screenHeight * 0.15,
        left: -screenWidth * 0.1,
        child: Container(
          width: screenWidth * 0.3,
          height: screenWidth * 0.3,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: _bgColors[3],
          ),
        ),
      ),

      // Middle right small circle
      Positioned(
        top: screenHeight * 0.45,
        right: -screenWidth * 0.05,
        child: Container(
          width: screenWidth * 0.2,
          height: screenWidth * 0.2,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: _bgColors[4],
          ),
        ),
      ),
    ];
  }

  // Build floating elements with different animation patterns
  List<Widget> _buildFloatingElements(double screenWidth, double screenHeight) {
    return List.generate(widget.numberOfElements, (index) {
      final random = math.Random(index);
      final size = 6.0 + random.nextDouble() * 14; // Varied sizes
      final initialX = random.nextDouble() * screenWidth;
      final initialY = random.nextDouble() * screenHeight;
      final opacity = 0.3 + random.nextDouble() * 0.3; // Varied opacity
      final speed = 0.5 + random.nextDouble() * 1.0; // Varied animation speed

      // Alternate between primary and secondary colors
      final color =
          index % 2 == 0 ? widget.primaryColor : widget.secondaryColor;

      // Determine shape - mostly circles but some squares for variety
      final isCircle = random.nextDouble() > 0.2; // 80% circles, 20% squares

      return Positioned(
        left: initialX,
        top: initialY,
        child: AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            // Create a unique animation pattern for each element
            final time = _animationController.value;
            final phase = index * 0.5; // Different phase for each element

            // Create a more complex motion pattern - figure 8 or circular patterns
            final dx = math.sin(time * math.pi * 2 * speed + phase) * 20;
            final dy = math.cos(time * math.pi * 2 * speed + phase + 1) * 20;

            // Add a slight pulsing effect
            final scale = 0.9 + 0.2 * math.sin(time * math.pi * speed + phase);

            // Add rotation for square elements
            final rotation = isCircle ? 0.0 : time * math.pi * 2 * speed;

            return Transform.translate(
              offset: Offset(dx, dy),
              child: Transform.scale(
                scale: scale,
                child: Transform.rotate(
                  angle: rotation,
                  child: Container(
                    width: size,
                    height: size,
                    decoration: BoxDecoration(
                      color: color.withAlpha((opacity * 255).toInt()),
                      shape: isCircle ? BoxShape.circle : BoxShape.rectangle,
                      borderRadius:
                          isCircle ? null : BorderRadius.circular(size / 4),
                      boxShadow: [
                        BoxShadow(
                          color: color.withAlpha(51), // 0.2 * 255 = ~51
                          blurRadius: 5,
                          spreadRadius: 1,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      );
    });
  }

  // Build wave effect animation
  Widget _buildWaveEffect(double screenWidth, double screenHeight) {
    return Positioned(
      top: screenHeight * 0.3,
      left: 0,
      right: 0,
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Stack(
            children: [
              // First wave
              CustomPaint(
                size: Size(screenWidth, screenHeight * 0.4),
                painter: WavePainter(
                  animation: _animationController,
                  primaryColor: widget.primaryColor,
                  amplitude: 0.05,
                  frequency: 4.0,
                  phase: 0.0,
                ),
              ),
              // Second wave with different parameters
              CustomPaint(
                size: Size(screenWidth, screenHeight * 0.4),
                painter: WavePainter(
                  animation: _animationController,
                  primaryColor: widget.secondaryColor,
                  amplitude: 0.03,
                  frequency: 3.0,
                  phase: math.pi / 2, // Offset phase
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}

// Custom painter for wave effect
class WavePainter extends CustomPainter {
  final Animation<double> animation;
  final Color primaryColor;
  final double amplitude; // Wave height factor
  final double frequency; // Wave frequency factor
  final double phase; // Phase offset

  WavePainter({
    required this.animation,
    required this.primaryColor,
    this.amplitude = 0.05,
    this.frequency = 4.0,
    this.phase = 0.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = primaryColor.withAlpha(13) // 0.05 * 255 = ~13
      ..style = PaintingStyle.fill;

    final path = Path();
    final width = size.width;
    final height = size.height;

    // Starting point
    path.moveTo(0, height * 0.5);

    // Create a wave pattern with smoother curve using cubic
    final step = 5.0; // Larger step for better performance
    for (double x = 0; x <= width; x += step) {
      final normalizedX = x / width;
      final waveHeight = math.sin((normalizedX * frequency * math.pi) +
              (animation.value * math.pi * 2) +
              phase) *
          height *
          amplitude;
      final y = height * 0.5 + waveHeight;

      // Use quadratic bezier for smoother curves
      if (x == 0) {
        path.lineTo(x, y);
      } else {
        final prevX = x - step;
        final prevY = height * 0.5 +
            math.sin(((prevX / width) * frequency * math.pi) +
                    (animation.value * math.pi * 2) +
                    phase) *
                height *
                amplitude;

        final midX = (prevX + x) / 2;
        path.quadraticBezierTo(prevX, prevY, midX, (prevY + y) / 2);
      }
    }

    // Complete the path
    path.lineTo(width, height);
    path.lineTo(0, height);
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
