import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../services/logout_service.dart';
import '../../auth/auth_screen.dart';

class PrivacySettingsPage extends StatefulWidget {
  const PrivacySettingsPage({super.key});

  @override
  State<PrivacySettingsPage> createState() => _PrivacySettingsPageState();
}

class _PrivacySettingsPageState extends State<PrivacySettingsPage> {
  bool _notificationPermission = true;
  bool _dataCollection = true;
  bool _marketingEmails = false;
  final TextEditingController _deleteConfirmationController =
      TextEditingController();

  @override
  void dispose() {
    _deleteConfirmationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      backgroundColor:
          isDarkMode ? const Color(0xFF121212) : const Color(0xFFF5F7FA),
      appBar: AppBar(
        title: const Text(
          'Privacy Settings',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Account Privacy & Policy section
            _buildPrivacyPolicySection(),

            const SizedBox(height: 24),

            /*
            // Permissions section - COMMENTED OUT FOR FUTURE USE
            _buildSectionHeader('App Permissions', Icons.security),
            const SizedBox(height: 8),
            _buildSettingsCard(
              children: [
                _buildSwitchTile(
                  title: 'Notifications',
                  subtitle: 'Allow app to send you notifications',
                  value: _notificationPermission,
                  onChanged: (value) {
                    setState(() {
                      _notificationPermission = value;
                    });
                  },
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Data & Privacy section - COMMENTED OUT FOR FUTURE USE
            _buildSectionHeader('Data & Privacy', Icons.data_usage),
            const SizedBox(height: 8),
            _buildSettingsCard(
              children: [
                _buildSwitchTile(
                  title: 'Usage Data Collection',
                  subtitle:
                      'Allow app to collect usage data to improve services',
                  value: _dataCollection,
                  onChanged: (value) {
                    setState(() {
                      _dataCollection = value;
                    });
                  },
                ),
                _buildDivider(),
                _buildSwitchTile(
                  title: 'Marketing Emails',
                  subtitle: 'Receive promotional emails and offers',
                  value: _marketingEmails,
                  onChanged: (value) {
                    setState(() {
                      _marketingEmails = value;
                    });
                  },
                ),
              ],
            ),

            const SizedBox(height: 24),
            */

            // Legal section
            _buildSectionHeader('Legal', Icons.gavel),
            const SizedBox(height: 8),
            _buildSettingsCard(
              children: [
                _buildActionTile(
                  title: 'Privacy Policy',
                  subtitle: 'Read our privacy policy',
                  icon: Icons.policy,
                  onTap: () {
                    _openPrivacyPolicy();
                  },
                ),
                _buildDivider(),
                _buildActionTile(
                  title: 'Terms and Conditions',
                  subtitle: 'Read our terms and conditions',
                  icon: Icons.description,
                  onTap: () {
                    _openTermsAndConditions();
                  },
                ),
                _buildDivider(),
                _buildActionTile(
                  title: 'Refund Policy',
                  subtitle: 'Read our refund policy',
                  icon: Icons.money_off,
                  onTap: () {
                    _openRefundPolicy();
                  },
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Danger Zone
            _buildSectionHeader('Danger Zone', Icons.warning_amber_rounded,
                color: Colors.red),
            const SizedBox(height: 8),
            _buildSettingsCard(
              children: [
                _buildActionTile(
                  title: 'Delete Account',
                  subtitle: 'Permanently delete your account and all data',
                  icon: Icons.delete_forever,
                  iconColor: Colors.red,
                  onTap: () {
                    _showDeleteAccountDialog();
                  },
                ),
              ],
            ),

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title, IconData icon, {Color? color}) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: Row(
        children: [
          Icon(
            icon,
            size: 20,
            color: color ?? (isDarkMode ? Colors.white70 : Colors.black54),
          ),
          const SizedBox(width: 8),
          Text(
            title,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color ?? (isDarkMode ? Colors.white : Colors.black87),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsCard({required List<Widget> children}) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: children,
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return SwitchListTile(
      title: Text(
        title,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: isDarkMode ? Colors.white : Colors.black87,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 14,
          color: isDarkMode ? Colors.white70 : Colors.black54,
        ),
      ),
      value: value,
      onChanged: onChanged,
      activeColor: const Color(0xFF67C44C),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
    );
  }

  Widget _buildActionTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
    Color? iconColor,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return ListTile(
      title: Text(
        title,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: isDarkMode ? Colors.white : Colors.black87,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 14,
          color: isDarkMode ? Colors.white70 : Colors.black54,
        ),
      ),
      leading: Icon(
        icon,
        color: iconColor ?? (isDarkMode ? Colors.white70 : Colors.black54),
      ),
      trailing: Icon(
        Icons.arrow_forward_ios,
        size: 16,
        color: isDarkMode ? Colors.white54 : Colors.black45,
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      onTap: onTap,
    );
  }

  Widget _buildDivider() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Divider(
      height: 1,
      thickness: 1,
      color:
          isDarkMode ? Colors.white.withAlpha(25) : Colors.grey.withAlpha(51),
      indent: 16,
      endIndent: 16,
    );
  }

  /// Build the Account Privacy & Policy section
  Widget _buildPrivacyPolicySection() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section header
        _buildSectionHeader('Account Privacy & Policy', Icons.privacy_tip),
        const SizedBox(height: 8),

        // Privacy policy content card
        Container(
          width: double.infinity,
          decoration: BoxDecoration(
            color: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(13),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Privacy policy text
                Text(
                  'This Privacy Policy describes how we collect, use, process, and disclose your information, including personal information, in conjunction with your access to and use of our website and mobile application, https://eeil.online/ ("Website").\n\n'
                  'If you see an undefined term in this Privacy Policy, it has the same definition as in our Terms of Service. When this policy mentions "Company", "we", "us", or "our" it refers to the "Ecoplug Energy India Limited", the company (ies) responsible for your information under this Privacy Policy.\n\n'
                  'If you have any questions or complaints about this Privacy Policy or Company\'s information handling practices, you may email <NAME_EMAIL>',
                  style: TextStyle(
                    fontSize: 14,
                    height: 1.5,
                    color: isDarkMode ? Colors.white70 : Colors.black54,
                  ),
                ),
                const SizedBox(height: 16),

                // Contact info highlight
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: const Color(0xFF67C44C).withAlpha(26),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: const Color(0xFF67C44C).withAlpha(51),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.email_outlined,
                        size: 20,
                        color: const Color(0xFF67C44C),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Contact us: <EMAIL>',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: isDarkMode ? Colors.white : Colors.black87,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// Open Privacy Policy in browser
  Future<void> _openPrivacyPolicy() async {
    const url = 'https://app.eeil.online/privacy';
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        if (mounted) {
          _showErrorSnackBar('Could not open Privacy Policy');
        }
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('Error opening Privacy Policy: ${e.toString()}');
      }
    }
  }

  /// Open Terms and Conditions in browser
  Future<void> _openTermsAndConditions() async {
    const url = 'https://app.eeil.online/termsandcondition';
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        if (mounted) {
          _showErrorSnackBar('Could not open Terms and Conditions');
        }
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar(
            'Error opening Terms and Conditions: ${e.toString()}');
      }
    }
  }

  /// Open Refund Policy in browser
  Future<void> _openRefundPolicy() async {
    const url = 'https://app.eeil.online/refund';
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        if (mounted) {
          _showErrorSnackBar('Could not open Refund Policy');
        }
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('Error opening Refund Policy: ${e.toString()}');
      }
    }
  }

  /// Show error snackbar with consistent styling
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  void _showDeleteAccountDialog() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    _deleteConfirmationController.clear(); // Clear any previous input

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Account'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Are you sure you want to delete your account? This action cannot be undone and will:',
            ),
            const SizedBox(height: 16),
            _buildBulletPoint('Delete all your personal information'),
            _buildBulletPoint('Delete your charging history'),
            const SizedBox(height: 16),
            const Text(
              'To confirm, please type "delete" below:',
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _deleteConfirmationController,
              decoration: InputDecoration(
                hintText: 'Type delete',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
              ),
              style: TextStyle(
                color: isDarkMode ? Colors.white : Colors.black87,
              ),
            ),
          ],
        ),
        backgroundColor: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
        titleTextStyle: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: isDarkMode ? Colors.white : Colors.black87,
        ),
        contentTextStyle: TextStyle(
          fontSize: 14,
          color: isDarkMode ? Colors.white70 : Colors.black54,
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              _handleAccountDeletion();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            child: const Text('Delete Account'),
          ),
        ],
      ),
    );
  }

  Widget _buildBulletPoint(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('• ', style: TextStyle(fontWeight: FontWeight.bold)),
          Expanded(child: Text(text)),
        ],
      ),
    );
  }

  /// Handle account deletion - performs logout instead of actual deletion
  void _handleAccountDeletion() async {
    // Validate the confirmation text
    final confirmationText =
        _deleteConfirmationController.text.trim().toLowerCase();

    if (confirmationText != 'delete') {
      // Show error if confirmation text doesn't match
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please type "delete" to confirm account deletion.'),
          backgroundColor: Colors.orange,
          behavior: SnackBarBehavior.floating,
        ),
      );
      return;
    }

    // Close the dialog
    Navigator.of(context).pop();

    // Show loading indicator
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: CircularProgressIndicator(),
      ),
    );

    try {
      // Perform logout instead of account deletion
      final logoutService = LogoutService();
      final logoutSuccess = await logoutService.performCompleteLogout();

      // Close loading dialog
      if (mounted) {
        Navigator.of(context).pop();
      }

      if (logoutSuccess) {
        // Show success message
        _showLogoutSuccess();

        // Navigate to auth screen after a short delay
        Future.delayed(const Duration(seconds: 2), () {
          if (mounted) {
            Navigator.of(context)
                .pushNamedAndRemoveUntil('/auth', (route) => false);
          }
        });
      } else {
        // Show error message
        _showErrorSnackBar('Logout failed. Please try again.');
      }
    } catch (e) {
      // Close loading dialog
      if (mounted) {
        Navigator.of(context).pop();
      }

      // Show error message
      _showErrorSnackBar('An error occurred during logout: ${e.toString()}');
    }
  }

  void _showLogoutSuccess() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text(
            'Account deletion confirmed. You have been logged out successfully.'),
        duration: Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        backgroundColor: Colors.green,
      ),
    );
  }

  // Removed unused refund request methods
}
