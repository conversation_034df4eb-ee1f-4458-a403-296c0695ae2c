import 'package:flutter/foundation.dart';
import 'package:ecoplug/services/auth_notification_service.dart';

/// Local Welcome Notification Test
/// Simple test functions to verify the local welcome notification system
class LocalWelcomeNotificationTest {
  static final AuthNotificationService _authService = AuthNotificationService();

  /// Test the complete local welcome notification flow
  static Future<Map<String, dynamic>> testCompleteFlow() async {
    final results = <String, dynamic>{};
    
    try {
      debugPrint('🧪 ===== TESTING LOCAL WELCOME NOTIFICATION FLOW =====');
      
      // Test 1: Initialize service
      results['initialization'] = await _testInitialization();
      
      // Test 2: First login notification
      results['first_login'] = await _testFirstLogin();
      
      // Test 3: Return user notification
      results['return_user'] = await _testReturnUser();
      
      // Test 4: Session management
      results['session_management'] = await _testSessionManagement();
      
      // Test 5: Error handling
      results['error_handling'] = await _testErrorHandling();
      
      // Overall result
      results['overall_status'] = _calculateOverallStatus(results);
      results['test_timestamp'] = DateTime.now().toIso8601String();
      
      debugPrint('✅ Local welcome notification test completed');
      return results;
      
    } catch (e) {
      debugPrint('❌ Error during local welcome notification test: $e');
      results['error'] = e.toString();
      results['overall_status'] = 'failed';
      return results;
    }
  }

  /// Test service initialization
  static Future<Map<String, dynamic>> _testInitialization() async {
    try {
      debugPrint('🧪 Testing service initialization...');
      
      await _authService.initialize();
      
      return {
        'status': 'passed',
        'service_initialized': true,
        'message': 'Service initialized successfully',
      };
    } catch (e) {
      debugPrint('❌ Initialization test failed: $e');
      return {
        'status': 'failed',
        'error': e.toString(),
        'service_initialized': false,
      };
    }
  }

  /// Test first login notification
  static Future<Map<String, dynamic>> _testFirstLogin() async {
    try {
      debugPrint('🧪 Testing first login notification...');
      
      final userId = 'test_first_user_${DateTime.now().millisecondsSinceEpoch}';
      final userName = 'John Doe';
      final userEmail = '<EMAIL>';
      
      await _authService.onLoginSuccess(
        userId: userId,
        userName: userName,
        userEmail: userEmail,
      );
      
      // Get login stats to verify
      final stats = await _authService.getLoginStats(userId);
      
      return {
        'status': 'passed',
        'user_id': userId,
        'user_name': userName,
        'is_first_time_user': stats['is_first_time_user'],
        'login_count': stats['login_count'],
        'message': 'First login notification triggered successfully',
      };
    } catch (e) {
      debugPrint('❌ First login test failed: $e');
      return {
        'status': 'failed',
        'error': e.toString(),
      };
    }
  }

  /// Test return user notification
  static Future<Map<String, dynamic>> _testReturnUser() async {
    try {
      debugPrint('🧪 Testing return user notification...');
      
      final userId = 'test_return_user_${DateTime.now().millisecondsSinceEpoch}';
      final userName = 'Jane Smith';
      
      // Simulate first login
      await _authService.onLoginSuccess(
        userId: userId,
        userName: userName,
        userEmail: '<EMAIL>',
      );
      
      // Wait a moment
      await Future.delayed(const Duration(milliseconds: 500));
      
      // Simulate logout
      await _authService.onLogout(userId: userId);
      
      // Wait a moment
      await Future.delayed(const Duration(milliseconds: 500));
      
      // Simulate return login
      await _authService.onLoginSuccess(
        userId: userId,
        userName: userName,
        userEmail: '<EMAIL>',
      );
      
      // Get login stats
      final stats = await _authService.getLoginStats(userId);
      
      return {
        'status': 'passed',
        'user_id': userId,
        'user_name': userName,
        'is_first_time_user': stats['is_first_time_user'],
        'login_count': stats['login_count'],
        'message': 'Return user notification triggered successfully',
      };
    } catch (e) {
      debugPrint('❌ Return user test failed: $e');
      return {
        'status': 'failed',
        'error': e.toString(),
      };
    }
  }

  /// Test session management
  static Future<Map<String, dynamic>> _testSessionManagement() async {
    try {
      debugPrint('🧪 Testing session management...');
      
      final userId = 'test_session_user_${DateTime.now().millisecondsSinceEpoch}';
      final userName = 'Session Test User';
      
      // First login in session
      await _authService.onLoginSuccess(
        userId: userId,
        userName: userName,
        userEmail: '<EMAIL>',
      );
      
      // Second login in same session (should not show duplicate notification)
      await _authService.onLoginSuccess(
        userId: userId,
        userName: userName,
        userEmail: '<EMAIL>',
      );
      
      return {
        'status': 'passed',
        'user_id': userId,
        'duplicate_prevention': true,
        'message': 'Session management working correctly',
      };
    } catch (e) {
      debugPrint('❌ Session management test failed: $e');
      return {
        'status': 'failed',
        'error': e.toString(),
      };
    }
  }

  /// Test error handling
  static Future<Map<String, dynamic>> _testErrorHandling() async {
    try {
      debugPrint('🧪 Testing error handling...');
      
      // Test with invalid data
      await _authService.onLoginSuccess(
        userId: '', // Empty user ID
        userName: null,
        userEmail: null,
      );
      
      return {
        'status': 'passed',
        'error_isolation': true,
        'message': 'Error handling working correctly - login succeeds even with notification errors',
      };
    } catch (e) {
      // This is expected - the service should handle errors gracefully
      debugPrint('🧪 Error handling test - caught expected error: $e');
      return {
        'status': 'passed',
        'error_isolation': true,
        'expected_error': e.toString(),
        'message': 'Error handling working correctly',
      };
    }
  }

  /// Calculate overall test status
  static String _calculateOverallStatus(Map<String, dynamic> results) {
    final testResults = <String>[];
    
    for (final key in results.keys) {
      if (results[key] is Map<String, dynamic>) {
        final testResult = results[key] as Map<String, dynamic>;
        final status = testResult['status'] as String?;
        if (status != null) {
          testResults.add(status);
        }
      }
    }
    
    if (testResults.every((status) => status == 'passed')) {
      return 'passed';
    } else if (testResults.any((status) => status == 'failed')) {
      return 'failed';
    } else {
      return 'partial';
    }
  }

  /// Quick test for immediate verification
  static Future<bool> quickTest() async {
    try {
      debugPrint('🧪 Running quick local welcome notification test...');
      
      // Initialize
      await _authService.initialize();
      
      // Test notification
      final userId = 'quick_test_${DateTime.now().millisecondsSinceEpoch}';
      await _authService.onLoginSuccess(
        userId: userId,
        userName: 'Quick Test User',
        userEmail: '<EMAIL>',
      );
      
      debugPrint('✅ Quick test passed');
      return true;
    } catch (e) {
      debugPrint('❌ Quick test failed: $e');
      return false;
    }
  }

  /// Test with your actual authentication flow
  static Future<void> testWithActualAuth({
    required String userId,
    required String userName,
    String? userEmail,
  }) async {
    try {
      debugPrint('🧪 Testing with actual authentication data...');
      debugPrint('🧪 User ID: $userId');
      debugPrint('🧪 User Name: $userName');
      debugPrint('🧪 User Email: $userEmail');
      
      // Initialize service
      await _authService.initialize();
      
      // Trigger welcome notification
      await _authService.onLoginSuccess(
        userId: userId,
        userName: userName,
        userEmail: userEmail,
      );
      
      debugPrint('✅ Actual auth test completed successfully');
    } catch (e) {
      debugPrint('❌ Actual auth test failed: $e');
      rethrow;
    }
  }

  /// Get test statistics
  static Future<Map<String, dynamic>> getTestStats() async {
    try {
      final authStatus = await _authService.getAuthNotificationStatus();
      final welcomeStats = await _authService.getWelcomeNotificationStats();
      
      return {
        'auth_status': authStatus,
        'welcome_stats': welcomeStats,
        'test_timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      return {
        'error': e.toString(),
        'test_timestamp': DateTime.now().toIso8601String(),
      };
    }
  }
}
