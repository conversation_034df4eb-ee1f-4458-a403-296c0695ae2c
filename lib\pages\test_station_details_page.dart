import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/station/station_details_response.dart';
import '../utils/app_themes.dart';

class TestStationDetailsPage extends ConsumerWidget {
  const TestStationDetailsPage({super.key});

  // HARDCODED TEST DATA - Exact JSON from API
  static const String testJsonData = '''
{
    "message": "OK",
    "success": true,
    "data": {
        "longitude": 76.613853,
        "latitude": 27.608162,
        "name": "E Plug energy Office DC Charger",
        "address": "raath nagar vijay mandir road",
        "city": "Alwar",
        "state": "Rajasthan",
        "postal_code": "301001",
        "open_status": true,
        "opening_times": "24 Hours",
        "rate": 5,
        "rate_total": 49,
        "images": "https://api2.eeil.online/uploads/ev-banner2.png",
        "evses": {
            "IN*EPL*E1719484988_1": {
                "last_used": "2025-06-04",
                "connectors": [
                    {
                        "soc": 0,
                        "evses_uid": "e409811c-a6b1-4b04-92e6-3a219a824bfc",
                        "status": "Offline",
                        "label": "Connector 1",
                        "max_electric_power": 30,
                        "connector_id": "1",
                        "standard": "IEC_62196_T2_COMBO",
                        "power_type": "DC",
                        "price": 14,
                        "price_label": "₹14",
                        "price_per_unit": 14,
                        "type": "CCS2",
                        "icon": "https://api2.eeil.online/uploads/connector_type/ccs2.svg"
                    },
                    {
                        "soc": 0,
                        "evses_uid": "f9ec9f38-5658-48ef-87b4-e3222b3599fb",
                        "status": "Offline",
                        "label": "Connector 2",
                        "max_electric_power": 30,
                        "connector_id": "2",
                        "standard": "IEC_62196_T2_COMBO",
                        "power_type": "DC",
                        "price": 14,
                        "price_label": "₹14",
                        "price_per_unit": 14,
                        "type": "CCS2",
                        "icon": "https://api2.eeil.online/uploads/connector_type/ccs2.svg"
                    },
                    {
                        "soc": 0,
                        "evses_uid": "747aec87-74ce-4c80-abf1-69a11a14ca47",
                        "status": "Offline",
                        "label": "Connector 3",
                        "max_electric_power": 7,
                        "connector_id": "3",
                        "standard": "SMART_PLUG",
                        "power_type": "AC",
                        "price": 17.95,
                        "price_label": "₹17.95",
                        "price_per_unit": 17.95,
                        "type": "SMARTPLUG",
                        "icon": "https://api2.eeil.online/uploads/connector_type/splug1.svg"
                    }
                ],
                "price": 14,
                "text_price": "₹14",
                "power_output": "DCAC",
                "max_power": 67,
                "name": "E Plug energy Office DC Charger"
            }
        }
    },
    "wallet": {
        "id": 4,
        "user_id": 4,
        "balance": 1287.82203371801,
        "exposure": -165.2,
        "created_at": "2024-11-11T05:41:51.000000Z",
        "updated_at": "2025-06-04T13:07:59.000000Z"
    },
    "instant_charging": 0
}
''';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('🧪 Station Details Test'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Test Status Header
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.green.shade100,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '🧪 TESTING STATION DETAILS PARSING',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'This page tests the StationDetailsResponse parsing with real API data',
                    style: TextStyle(color: Colors.green),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 20),

            // Test the JSON parsing
            _buildTestSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildTestSection() {
    return FutureBuilder<StationDetailsResponse>(
      future: _parseTestData(),
      builder: (context, snapshot) {
        if (snapshot.hasError) {
          return Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.red.shade100,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.red),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '❌ PARSING ERROR',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.red,
                  ),
                ),
                const SizedBox(height: 8),
                SelectableText(
                  'Error: ${snapshot.error}',
                  style: const TextStyle(color: Colors.red),
                ),
              ],
            ),
          );
        }

        if (!snapshot.hasData) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        final response = snapshot.data!;
        final stationData = response.data;

        if (stationData == null) {
          return const Text('❌ No station data found');
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Success indicator
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.green.shade100,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green),
              ),
              child: const Text(
                '✅ JSON PARSING SUCCESSFUL!',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.green,
                ),
              ),
            ),
            const SizedBox(height: 20),

            // Station Basic Info
            _buildStationBasicInfo(stationData),
            const SizedBox(height: 20),

            // Station Image
            _buildStationImage(stationData),
            const SizedBox(height: 20),

            // EVSEs and Connectors
            _buildEvsesList(stationData),
            const SizedBox(height: 20),

            // Wallet Info
            _buildWalletInfo(response),
          ],
        );
      },
    );
  }

  Future<StationDetailsResponse> _parseTestData() async {
    try {
      debugPrint('🧪 Starting JSON parsing test...');
      final response = StationDetailsResponse.fromJsonString(testJsonData);
      debugPrint('✅ JSON parsing completed successfully!');
      return response;
    } catch (e, stackTrace) {
      debugPrint('❌ JSON parsing failed: $e');
      debugPrint('Stack trace: $stackTrace');
      rethrow;
    }
  }

  Widget _buildStationBasicInfo(StationDetailsData stationData) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '🏢 STATION BASIC INFO',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            _buildInfoRow('Name', stationData.name ?? 'N/A'),
            _buildInfoRow('Address', stationData.address ?? 'N/A'),
            _buildInfoRow('City', stationData.city ?? 'N/A'),
            _buildInfoRow('State', stationData.state ?? 'N/A'),
            _buildInfoRow('Postal Code', stationData.postalCode ?? 'N/A'),
            _buildInfoRow('Opening Times', stationData.openingTimes ?? 'N/A'),
            _buildInfoRow('Rating', '${stationData.rate ?? 0} ⭐'),
            _buildInfoRow('Total Reviews', '${stationData.rateTotal ?? 0}'),
            _buildInfoRow('Open Status',
                stationData.openStatus == true ? '🟢 Open' : '🔴 Closed'),
          ],
        ),
      ),
    );
  }

  Widget _buildStationImage(StationDetailsData stationData) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '🖼️ STATION IMAGE',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            if (stationData.images != null && stationData.images!.isNotEmpty)
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.network(
                  stationData.images!,
                  height: 200,
                  width: double.infinity,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      height: 200,
                      width: double.infinity,
                      color: Colors.grey.shade300,
                      child: const Center(
                        child: Text('❌ Image failed to load'),
                      ),
                    );
                  },
                  loadingBuilder: (context, child, loadingProgress) {
                    if (loadingProgress == null) return child;
                    return Container(
                      height: 200,
                      width: double.infinity,
                      color: Colors.grey.shade300,
                      child: const Center(
                        child: CircularProgressIndicator(),
                      ),
                    );
                  },
                ),
              )
            else
              Container(
                height: 200,
                width: double.infinity,
                color: Colors.grey.shade300,
                child: const Center(
                  child: Text('❌ No image URL provided'),
                ),
              ),
            const SizedBox(height: 8),
            Text('Image URL: ${stationData.images ?? 'N/A'}'),
          ],
        ),
      ),
    );
  }

  Widget _buildEvsesList(StationDetailsData stationData) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '🔌 EVSES & CONNECTORS',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            if (stationData.evses != null && stationData.evses!.isNotEmpty)
              ...stationData.evses!.entries.map((evseEntry) {
                final evseId = evseEntry.key;
                final evse = evseEntry.value;
                return _buildEvseCard(evseId, evse);
              })
            else
              const Text('❌ No EVSEs found'),
          ],
        ),
      ),
    );
  }

  Widget _buildEvseCard(String evseId, EvseDetails evse) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '🏷️ EVSE: $evseId',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          _buildInfoRow('Name', evse.name ?? 'N/A'),
          _buildInfoRow('Last Used', evse.lastUsed ?? 'N/A'),
          _buildInfoRow('Price', '${evse.price ?? 'N/A'}'),
          _buildInfoRow('Text Price', evse.textPrice ?? 'N/A'),
          _buildInfoRow('Power Output', evse.powerOutput ?? 'N/A'),
          _buildInfoRow('Max Power', '${evse.maxPower ?? 'N/A'} kW'),
          const SizedBox(height: 12),
          const Text(
            '🔌 CONNECTORS:',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          if (evse.connectors != null && evse.connectors!.isNotEmpty)
            ...evse.connectors!
                .map((connector) => _buildConnectorCard(connector))
          else
            const Text('❌ No connectors found'),
        ],
      ),
    );
  }

  Widget _buildConnectorCard(Connector connector) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              if (connector.icon != null && connector.icon!.isNotEmpty)
                Image.network(
                  connector.icon!,
                  width: 24,
                  height: 24,
                  errorBuilder: (context, error, stackTrace) {
                    return const Icon(Icons.power, size: 24);
                  },
                )
              else
                const Icon(Icons.power, size: 24),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  connector.label ?? 'Unknown Connector',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getStatusColor(connector.status),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  connector.status ?? 'Unknown',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          _buildInfoRow('Type', connector.type ?? 'N/A'),
          _buildInfoRow('Power Type', connector.powerOutput ?? 'N/A'),
          _buildInfoRow(
              'Max Power', '${connector.maxElectricPower ?? 'N/A'} kW'),
          _buildInfoRow('Price', '${connector.price ?? 'N/A'}'),
          _buildInfoRow('Price Label', connector.priceLabel ?? 'N/A'),
          _buildInfoRow('Price Per Unit', '${connector.pricePerUnit ?? 'N/A'}'),
          _buildInfoRow('SOC', '${connector.soc ?? 0}%'),
          _buildInfoRow('EVSE UID', connector.evsesUid ?? 'N/A'),
        ],
      ),
    );
  }

  Widget _buildWalletInfo(StationDetailsResponse response) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '💰 WALLET INFO',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            if (response.wallet != null)
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildInfoRow('Balance',
                      '₹${response.wallet!.balance?.toStringAsFixed(2) ?? 'N/A'}'),
                  _buildInfoRow('Exposure',
                      '₹${response.wallet!.exposure?.toStringAsFixed(2) ?? 'N/A'}'),
                  _buildInfoRow(
                      'User ID', '${response.wallet!.userId ?? 'N/A'}'),
                ],
              )
            else
              const Text('❌ No wallet data found'),
            const SizedBox(height: 8),
            _buildInfoRow('Instant Charging',
                response.instantCharging == 1 ? '✅ Enabled' : '❌ Disabled'),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: SelectableText(
              value,
              style: const TextStyle(color: Colors.black87),
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String? status) {
    switch (status?.toLowerCase()) {
      case 'available':
        return AppThemes.primaryColor;
      case 'occupied':
        return Colors.orange;
      case 'offline':
        return Colors.red;
      case 'faulted':
        return Colors.red.shade800;
      default:
        return Colors.grey;
    }
  }
}
