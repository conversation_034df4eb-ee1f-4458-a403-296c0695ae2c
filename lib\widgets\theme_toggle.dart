import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart'; // Import Riverpod
import '../providers/theme_provider.dart'; // Import the new Riverpod theme provider
import '../utils/app_themes.dart';

class ThemeToggle extends ConsumerWidget {
  // Change to ConsumerWidget
  final bool showLabel;
  final bool useIconButton;

  const ThemeToggle({
    super.key,
    this.showLabel = true,
    this.useIconButton = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Add WidgetRef ref
    // Access the ThemeNotifier using ref.watch
    final themeNotifier = ref.watch(themeNotifierProvider.notifier);
    final isDarkMode = themeNotifier.isDarkMode;

    if (useIconButton) {
      return AnimatedSwitcher(
        duration: const Duration(milliseconds: 300),
        transitionBuilder: (Widget child, Animation<double> animation) {
          return ScaleTransition(
            scale: animation,
            child: child,
          );
        },
        child: IconButton(
          key: <PERSON><PERSON><PERSON><bool>(isDarkMode),
          icon: Icon(
            isDarkMode ? Icons.light_mode_rounded : Icons.dark_mode_rounded,
            color: isDarkMode
                ? AppThemes.primaryColor
                : Theme.of(context).iconTheme.color,
            size: 24,
          ),
          onPressed: () {
            // Use ref.read to call the toggleTheme method
            ref.read(themeNotifierProvider.notifier).toggleTheme();
          },
          tooltip: isDarkMode ? 'Switch to Light Mode' : 'Switch to Dark Mode',
        ),
      );
    }

    return GestureDetector(
      onTap: () {
        // Use ref.read to call the toggleTheme method
        ref.read(themeNotifierProvider.notifier).toggleTheme();
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isDarkMode ? AppThemes.darkCard : Colors.grey.shade100,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isDarkMode ? AppThemes.darkBorder : Colors.grey.shade300,
            width: 1.5,
          ),
          boxShadow: [
            if (!isDarkMode)
              BoxShadow(
                color: Colors.black.withAlpha(13), // 0.05 * 255 = ~13
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            AnimatedSwitcher(
              duration: const Duration(milliseconds: 300),
              child: Icon(
                key: ValueKey<bool>(isDarkMode),
                isDarkMode ? Icons.light_mode_rounded : Icons.dark_mode_rounded,
                size: 20,
                color:
                    isDarkMode ? AppThemes.primaryColor : Colors.grey.shade700,
              ),
            ),
            if (showLabel) ...[
              const SizedBox(width: 8),
              AnimatedSwitcher(
                duration: const Duration(milliseconds: 300),
                child: Text(
                  key: ValueKey<bool>(isDarkMode),
                  isDarkMode ? 'Light Mode' : 'Dark Mode',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: isDarkMode
                        ? AppThemes.darkTextSecondary
                        : Colors.grey.shade700,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
