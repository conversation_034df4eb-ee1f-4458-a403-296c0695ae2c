import 'package:flutter/foundation.dart';
import 'dart:async';
import 'universal_payment_handler.dart';
import 'network_error_handler.dart';
import '../../core/api/api_service.dart';
import '../../core/api/api_config.dart';

/// Payment status verification system for all payment gateways
/// Implements transaction verification and status polling as required by gateway documentation
class PaymentStatusVerifier {
  static const String _logPrefix = '🔍 STATUS_VERIFIER:';
  static final Map<String, Timer> _activeVerifications = {};
  static final Map<String, Completer<VerificationResult>> _verificationCompleters = {};

  /// Verify payment status with comprehensive checks and retry mechanism
  static Future<VerificationResult> verifyPaymentStatus({
    required String gateway,
    required String transactionId,
    required String expectedStatus,
    int maxRetries = 5,
    Duration retryInterval = const Duration(seconds: 10),
    Duration maxVerificationTime = const Duration(minutes: 5),
  }) async {
    debugPrint('$_logPrefix Starting payment verification');
    debugPrint('$_logPrefix Gateway: $gateway');
    debugPrint('$_logPrefix Transaction ID: $transactionId');
    debugPrint('$_logPrefix Expected Status: $expectedStatus');
    debugPrint('$_logPrefix Max Retries: $maxRetries');
    debugPrint('$_logPrefix Retry Interval: ${retryInterval.inSeconds}s');

    final verificationKey = '${gateway}_$transactionId';
    
    // Check if verification is already in progress
    if (_activeVerifications.containsKey(verificationKey)) {
      debugPrint('$_logPrefix Verification already in progress for $verificationKey');
      return await _verificationCompleters[verificationKey]!.future;
    }

    // Create completer for this verification
    final completer = Completer<VerificationResult>();
    _verificationCompleters[verificationKey] = completer;

    final startTime = DateTime.now();
    int attempt = 0;

    // Set up maximum verification time timeout
    final maxTimeTimer = Timer(maxVerificationTime, () {
      debugPrint('$_logPrefix Maximum verification time reached for $verificationKey');
      _completeVerification(
        verificationKey,
        VerificationResult.timeout(
          gateway: gateway,
          transactionId: transactionId,
          message: 'Payment verification timed out after ${maxVerificationTime.inMinutes} minutes',
        ),
      );
    });

    // Start verification loop
    final verificationTimer = Timer.periodic(retryInterval, (timer) async {
      attempt++;
      debugPrint('$_logPrefix Verification attempt $attempt/$maxRetries for $verificationKey');

      try {
        final result = await _performSingleVerification(
          gateway: gateway,
          transactionId: transactionId,
          expectedStatus: expectedStatus,
        );

        debugPrint('$_logPrefix Verification result: ${result.status}');

        // Check if verification is complete
        if (result.isVerified || result.isFinalStatus || attempt >= maxRetries) {
          timer.cancel();
          maxTimeTimer.cancel();
          
          final totalTime = DateTime.now().difference(startTime);
          debugPrint('$_logPrefix Verification completed in ${totalTime.inSeconds}s after $attempt attempts');
          
          _completeVerification(verificationKey, result.copyWith(
            attempts: attempt,
            totalTime: totalTime,
          ));
        }

      } catch (e) {
        debugPrint('$_logPrefix Verification attempt $attempt failed: $e');
        
        if (attempt >= maxRetries) {
          timer.cancel();
          maxTimeTimer.cancel();
          
          _completeVerification(
            verificationKey,
            VerificationResult.error(
              gateway: gateway,
              transactionId: transactionId,
              message: 'Verification failed after $maxRetries attempts: ${e.toString()}',
              attempts: attempt,
              totalTime: DateTime.now().difference(startTime),
            ),
          );
        }
      }
    });

    _activeVerifications[verificationKey] = verificationTimer;

    return await completer.future;
  }

  /// Perform single verification attempt
  static Future<VerificationResult> _performSingleVerification({
    required String gateway,
    required String transactionId,
    required String expectedStatus,
  }) async {
    debugPrint('$_logPrefix Performing single verification for $gateway transaction $transactionId');

    try {
      // Use network error handler for API calls
      final response = await NetworkErrorHandler.handleApiCall(
        () => _callVerificationAPI(gateway, transactionId),
        apiName: 'Payment Status Verification',
        maxRetries: 2,
        timeout: Duration(seconds: 15),
      );

      return _processVerificationResponse(
        gateway: gateway,
        transactionId: transactionId,
        expectedStatus: expectedStatus,
        response: response,
      );

    } on NetworkException catch (e) {
      debugPrint('$_logPrefix Network error during verification: $e');
      return VerificationResult.networkError(
        gateway: gateway,
        transactionId: transactionId,
        message: NetworkErrorHandler.getUserFriendlyMessage(e),
      );
    } catch (e) {
      debugPrint('$_logPrefix Unexpected error during verification: $e');
      return VerificationResult.error(
        gateway: gateway,
        transactionId: transactionId,
        message: 'Verification failed: ${e.toString()}',
      );
    }
  }

  /// Call appropriate verification API based on gateway
  static Future<Map<String, dynamic>> _callVerificationAPI(String gateway, String transactionId) async {
    debugPrint('$_logPrefix Calling verification API for $gateway');

    switch (gateway.toLowerCase()) {
      case 'phonepe':
        return await ApiService().post(
          ApiConfig.phonepeResponse,
          data: {'transactionId': transactionId, 'action': 'verify'},
        );
      case 'payu':
        return await ApiService().post(
          ApiConfig.payuResponse,
          data: {'txnid': transactionId, 'action': 'verify'},
        );
      case 'cashfree':
        return await ApiService().post(
          ApiConfig.cashfreeResponse,
          data: {'orderId': transactionId, 'action': 'verify'},
        );
      default:
        throw Exception('Unsupported gateway: $gateway');
    }
  }

  /// Process verification API response
  static VerificationResult _processVerificationResponse({
    required String gateway,
    required String transactionId,
    required String expectedStatus,
    required Map<String, dynamic> response,
  }) {
    debugPrint('$_logPrefix Processing verification response for $gateway');
    debugPrint('$_logPrefix Response: $response');

    try {
      final status = _extractStatus(gateway, response);
      final normalizedStatus = UniversalPaymentHandler.normalizeStatus(status);
      final normalizedExpected = UniversalPaymentHandler.normalizeStatus(expectedStatus);

      debugPrint('$_logPrefix Extracted status: $status (normalized: $normalizedStatus)');
      debugPrint('$_logPrefix Expected status: $expectedStatus (normalized: $normalizedExpected)');

      final isVerified = normalizedStatus == normalizedExpected;
      final isFinalStatus = _isFinalStatus(normalizedStatus);

      if (isVerified) {
        return VerificationResult.verified(
          gateway: gateway,
          transactionId: transactionId,
          actualStatus: status,
          expectedStatus: expectedStatus,
          response: response,
        );
      } else if (isFinalStatus) {
        return VerificationResult.failed(
          gateway: gateway,
          transactionId: transactionId,
          actualStatus: status,
          expectedStatus: expectedStatus,
          message: 'Payment status is $status, expected $expectedStatus',
          response: response,
        );
      } else {
        return VerificationResult.pending(
          gateway: gateway,
          transactionId: transactionId,
          actualStatus: status,
          expectedStatus: expectedStatus,
          message: 'Payment is still processing (status: $status)',
          response: response,
        );
      }

    } catch (e) {
      debugPrint('$_logPrefix Error processing verification response: $e');
      return VerificationResult.error(
        gateway: gateway,
        transactionId: transactionId,
        message: 'Failed to process verification response: ${e.toString()}',
      );
    }
  }

  /// Extract status from gateway-specific response
  static String _extractStatus(String gateway, Map<String, dynamic> response) {
    switch (gateway.toLowerCase()) {
      case 'phonepe':
        return response['status']?.toString() ?? 
               response['data']?['status']?.toString() ?? 
               'UNKNOWN';
      case 'payu':
        return response['status']?.toString() ?? 
               response['result']?['status']?.toString() ?? 
               'UNKNOWN';
      case 'cashfree':
        return response['order_status']?.toString() ?? 
               response['payment_status']?.toString() ?? 
               'UNKNOWN';
      default:
        return response['status']?.toString() ?? 'UNKNOWN';
    }
  }

  /// Check if status is final (no further changes expected)
  static bool _isFinalStatus(String normalizedStatus) {
    final finalStatuses = ['SUCCESS', 'FAILED', 'CANCELLED', 'TIMEOUT'];
    return finalStatuses.contains(normalizedStatus);
  }

  /// Complete verification and cleanup
  static void _completeVerification(String verificationKey, VerificationResult result) {
    debugPrint('$_logPrefix Completing verification for $verificationKey');
    
    final completer = _verificationCompleters.remove(verificationKey);
    final timer = _activeVerifications.remove(verificationKey);
    
    timer?.cancel();
    
    if (completer != null && !completer.isCompleted) {
      completer.complete(result);
    }
  }

  /// Cancel ongoing verification
  static void cancelVerification(String gateway, String transactionId) {
    final verificationKey = '${gateway}_$transactionId';
    debugPrint('$_logPrefix Cancelling verification for $verificationKey');
    
    final timer = _activeVerifications.remove(verificationKey);
    final completer = _verificationCompleters.remove(verificationKey);
    
    timer?.cancel();
    
    if (completer != null && !completer.isCompleted) {
      completer.complete(VerificationResult.cancelled(
        gateway: gateway,
        transactionId: transactionId,
        message: 'Verification cancelled by user',
      ));
    }
  }

  /// Get active verifications count
  static int get activeVerificationsCount => _activeVerifications.length;

  /// Cleanup all verifications
  static void cleanup() {
    debugPrint('$_logPrefix Cleaning up all active verifications');
    
    for (final timer in _activeVerifications.values) {
      timer.cancel();
    }
    
    for (final completer in _verificationCompleters.values) {
      if (!completer.isCompleted) {
        completer.complete(VerificationResult.cancelled(
          gateway: 'Unknown',
          transactionId: 'Unknown',
          message: 'Verification cancelled due to cleanup',
        ));
      }
    }
    
    _activeVerifications.clear();
    _verificationCompleters.clear();
  }
}

/// Verification result class
class VerificationResult {
  final String gateway;
  final String transactionId;
  final String status;
  final String? actualStatus;
  final String? expectedStatus;
  final String message;
  final Map<String, dynamic>? response;
  final bool isVerified;
  final bool isFinalStatus;
  final int attempts;
  final Duration totalTime;
  final DateTime timestamp;

  VerificationResult({
    required this.gateway,
    required this.transactionId,
    required this.status,
    this.actualStatus,
    this.expectedStatus,
    required this.message,
    this.response,
    required this.isVerified,
    required this.isFinalStatus,
    this.attempts = 0,
    this.totalTime = Duration.zero,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  factory VerificationResult.verified({
    required String gateway,
    required String transactionId,
    required String actualStatus,
    required String expectedStatus,
    Map<String, dynamic>? response,
  }) {
    return VerificationResult(
      gateway: gateway,
      transactionId: transactionId,
      status: 'VERIFIED',
      actualStatus: actualStatus,
      expectedStatus: expectedStatus,
      message: 'Payment status verified successfully',
      response: response,
      isVerified: true,
      isFinalStatus: true,
    );
  }

  factory VerificationResult.failed({
    required String gateway,
    required String transactionId,
    required String actualStatus,
    required String expectedStatus,
    required String message,
    Map<String, dynamic>? response,
  }) {
    return VerificationResult(
      gateway: gateway,
      transactionId: transactionId,
      status: 'FAILED',
      actualStatus: actualStatus,
      expectedStatus: expectedStatus,
      message: message,
      response: response,
      isVerified: false,
      isFinalStatus: true,
    );
  }

  factory VerificationResult.pending({
    required String gateway,
    required String transactionId,
    required String actualStatus,
    required String expectedStatus,
    required String message,
    Map<String, dynamic>? response,
  }) {
    return VerificationResult(
      gateway: gateway,
      transactionId: transactionId,
      status: 'PENDING',
      actualStatus: actualStatus,
      expectedStatus: expectedStatus,
      message: message,
      response: response,
      isVerified: false,
      isFinalStatus: false,
    );
  }

  factory VerificationResult.timeout({
    required String gateway,
    required String transactionId,
    required String message,
  }) {
    return VerificationResult(
      gateway: gateway,
      transactionId: transactionId,
      status: 'TIMEOUT',
      message: message,
      isVerified: false,
      isFinalStatus: true,
    );
  }

  factory VerificationResult.networkError({
    required String gateway,
    required String transactionId,
    required String message,
  }) {
    return VerificationResult(
      gateway: gateway,
      transactionId: transactionId,
      status: 'NETWORK_ERROR',
      message: message,
      isVerified: false,
      isFinalStatus: false,
    );
  }

  factory VerificationResult.error({
    required String gateway,
    required String transactionId,
    required String message,
    int attempts = 0,
    Duration totalTime = Duration.zero,
  }) {
    return VerificationResult(
      gateway: gateway,
      transactionId: transactionId,
      status: 'ERROR',
      message: message,
      isVerified: false,
      isFinalStatus: true,
      attempts: attempts,
      totalTime: totalTime,
    );
  }

  factory VerificationResult.cancelled({
    required String gateway,
    required String transactionId,
    required String message,
  }) {
    return VerificationResult(
      gateway: gateway,
      transactionId: transactionId,
      status: 'CANCELLED',
      message: message,
      isVerified: false,
      isFinalStatus: true,
    );
  }

  VerificationResult copyWith({
    int? attempts,
    Duration? totalTime,
  }) {
    return VerificationResult(
      gateway: gateway,
      transactionId: transactionId,
      status: status,
      actualStatus: actualStatus,
      expectedStatus: expectedStatus,
      message: message,
      response: response,
      isVerified: isVerified,
      isFinalStatus: isFinalStatus,
      attempts: attempts ?? this.attempts,
      totalTime: totalTime ?? this.totalTime,
      timestamp: timestamp,
    );
  }

  @override
  String toString() {
    return 'VerificationResult(gateway: $gateway, transactionId: $transactionId, status: $status, isVerified: $isVerified, attempts: $attempts)';
  }
}
