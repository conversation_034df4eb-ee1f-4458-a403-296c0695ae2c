<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Successful</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: #f5f5f5;
        }
        .container {
            text-align: center;
            padding: 2rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            max-width: 90%;
            width: 100%;
        }
        .success-icon {
            font-size: 4rem;
            color: #4CAF50;
            margin-bottom: 1rem;
        }
        h1 {
            color: #333;
            margin-bottom: 1rem;
        }
        p {
            color: #666;
            margin-bottom: 1.5rem;
        }
        .btn {
            display: inline-block;
            padding: 0.8rem 1.5rem;
            background-color: #4CAF50;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-weight: bold;
            transition: background-color 0.3s;
        }
        .btn:hover {
            background-color: #45a049;
        }
        @media (max-width: 480px) {
            .container {
                padding: 1rem;
            }
            h1 {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="success-icon">✓</div>
        <h1>Payment Successful!</h1>
        <p>Thank you for your payment. Your transaction has been completed successfully.</p>
        <p>A receipt for your purchase has been emailed to you.</p>
        <a href="#" class="btn" onclick="closeWindow()">Return to App</a>
    </div>

    <script>
        // Function to send message back to Flutter app
        function closeWindow() {
            // This will be replaced with the actual Flutter WebView message
            if (window.flutter_inappwebview) {
                window.flutter_inappwebview.callHandler('paymentCallback', {
                    status: 'success',
                    message: 'Payment completed successfully',
                    transactionId: getUrlParameter('txnid'),
                    amount: getUrlParameter('amount'),
                    productInfo: getUrlParameter('productinfo')
                });
            } else if (window.parent && window.parent.flutter_inappwebview) {
                // For iOS
                window.parent.flutter_inappwebview.callHandler('paymentCallback', {
                    status: 'success',
                    message: 'Payment completed successfully',
                    transactionId: getUrlParameter('txnid'),
                    amount: getUrlParameter('amount'),
                    productInfo: getUrlParameter('productinfo')
                });
            } else {
                // Fallback for testing in browser
                console.log('Payment successful', {
                    status: 'success',
                    transactionId: getUrlParameter('txnid'),
                    amount: getUrlParameter('amount'),
                    productInfo: getUrlParameter('productinfo')
                });
                alert('Payment successful! You can close this window.');
            }
        }

        // Helper function to get URL parameters
        function getUrlParameter(name) {
            name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
            const regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
            const results = regex.exec(location.search);
            return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
        }

        // Auto-close after a delay if no interaction
        setTimeout(function() {
            if (window.flutter_inappwebview || (window.parent && window.parent.flutter_inappwebview)) {
                closeWindow();
            }
        }, 5000);

        // Call closeWindow when the page loads
        document.addEventListener('DOMContentLoaded', closeWindow);
    </script>
</body>
</html>
