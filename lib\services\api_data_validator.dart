import 'package:flutter/foundation.dart';

/// Comprehensive API data validator to ensure data integrity
/// This service validates all API responses to prevent default/fallback data display
class ApiDataValidator {
  static const String _logPrefix = 'ApiDataValidator';

  /// Validate station details response from API
  static bool validateStationDetailsResponse(Map<String, dynamic> response) {
    debugPrint('$_logPrefix: Validating station details response');

    // Check response structure
    if (!response.containsKey('success') || response['success'] != true) {
      debugPrint(
          '$_logPrefix: Response indicates failure or missing success field');
      return false;
    }

    if (!response.containsKey('data') || response['data'] == null) {
      debugPrint('$_logPrefix: Response missing data field');
      return false;
    }

    final data = response['data'] as Map<String, dynamic>;

    // Validate required station fields
    final validationErrors = <String>[];

    // Check UID
    if (!data.containsKey('uid') ||
        data['uid'] == null ||
        (data['uid'] as String).isEmpty) {
      validationErrors.add('Station UID is missing or empty');
    }

    // Check name
    if (!data.containsKey('name') ||
        data['name'] == null ||
        (data['name'] as String).isEmpty) {
      validationErrors.add('Station name is missing or empty');
    }

    // Check address
    if (!data.containsKey('address') ||
        data['address'] == null ||
        (data['address'] as String).isEmpty) {
      validationErrors.add('Station address is missing or empty');
    }

    // Check coordinates
    if (!data.containsKey('latitude') ||
        !data.containsKey('longitude') ||
        data['latitude'] == null ||
        data['longitude'] == null) {
      validationErrors.add('Station coordinates are missing');
    } else {
      final lat = data['latitude'];
      final lng = data['longitude'];

      double? latitude;
      double? longitude;

      if (lat is num) {
        latitude = lat.toDouble();
      } else if (lat is String) {
        latitude = double.tryParse(lat);
      }

      if (lng is num) {
        longitude = lng.toDouble();
      } else if (lng is String) {
        longitude = double.tryParse(lng);
      }

      if (latitude == null ||
          longitude == null ||
          latitude == 0.0 ||
          longitude == 0.0) {
        validationErrors
            .add('Station coordinates are invalid (0,0 or unparseable)');
      }
    }

    // Check EVSEs
    if (!data.containsKey('evses') ||
        data['evses'] == null ||
        (data['evses'] as Map).isEmpty) {
      validationErrors.add('Station EVSEs are missing or empty');
    } else {
      // Validate EVSE structure
      final evses = data['evses'] as Map<String, dynamic>;
      bool hasValidConnectors = false;

      for (final evseEntry in evses.entries) {
        final evse = evseEntry.value as Map<String, dynamic>;
        if (evse.containsKey('connectors') &&
            evse['connectors'] != null &&
            (evse['connectors'] as List).isNotEmpty) {
          hasValidConnectors = true;
          break;
        }
      }

      if (!hasValidConnectors) {
        validationErrors.add('No valid connectors found in any EVSE');
      }
    }

    // Check open status
    if (!data.containsKey('open_status')) {
      validationErrors.add('Station open status is missing');
    }

    // Log validation results
    if (validationErrors.isNotEmpty) {
      debugPrint('$_logPrefix: Validation failed with errors:');
      for (final error in validationErrors) {
        debugPrint('  - $error');
      }
      return false;
    }

    debugPrint('$_logPrefix: Station details validation passed');
    return true;
  }

  /// Validate connector data from API
  static bool validateConnectorData(Map<String, dynamic> connector) {
    debugPrint('$_logPrefix: Validating connector data');

    final validationErrors = <String>[];

    // Check connector type
    if (!connector.containsKey('type') ||
        connector['type'] == null ||
        (connector['type'] as String).isEmpty) {
      validationErrors.add('Connector type is missing or empty');
    }

    // Check connector status
    if (!connector.containsKey('status') ||
        connector['status'] == null ||
        (connector['status'] as String).isEmpty) {
      validationErrors.add('Connector status is missing or empty');
    }

    // Check EVSE UID
    if (!connector.containsKey('evses_uid') ||
        connector['evses_uid'] == null ||
        (connector['evses_uid'] as String).isEmpty) {
      validationErrors.add('Connector EVSE UID is missing or empty');
    }

    // Log validation results
    if (validationErrors.isNotEmpty) {
      debugPrint('$_logPrefix: Connector validation failed with errors:');
      for (final error in validationErrors) {
        debugPrint('  - $error');
      }
      return false;
    }

    debugPrint('$_logPrefix: Connector validation passed');
    return true;
  }

  /// Validate station list response from API
  static bool validateStationListResponse(Map<String, dynamic> response) {
    debugPrint('$_logPrefix: Validating station list response');

    // Check response structure
    if (!response.containsKey('success') || response['success'] != true) {
      debugPrint('$_logPrefix: Station list response indicates failure');
      return false;
    }

    if (!response.containsKey('data') || response['data'] == null) {
      debugPrint('$_logPrefix: Station list response missing data field');
      return false;
    }

    final data = response['data'];

    // Handle different response structures
    List<dynamic>? stations;

    if (data is List) {
      stations = data;
    } else if (data is Map && data.containsKey('data')) {
      stations = data['data'] as List<dynamic>?;
    }

    if (stations == null || stations.isEmpty) {
      debugPrint('$_logPrefix: No stations found in response');
      return false;
    }

    // Validate each station has required fields
    int validStations = 0;
    for (final station in stations) {
      if (station is Map<String, dynamic>) {
        if (validateStationBasicData(station)) {
          validStations++;
        }
      }
    }

    if (validStations == 0) {
      debugPrint('$_logPrefix: No valid stations found in list');
      return false;
    }

    debugPrint(
        '$_logPrefix: Station list validation passed ($validStations valid stations)');
    return true;
  }

  /// Validate basic station data (for list items)
  static bool validateStationBasicData(Map<String, dynamic> station) {
    // Check for UID (required for navigation)
    if (!station.containsKey('uid') ||
        station['uid'] == null ||
        (station['uid'] as String).isEmpty) {
      return false;
    }

    // Check for name
    if (!station.containsKey('name') ||
        station['name'] == null ||
        (station['name'] as String).isEmpty) {
      return false;
    }

    // Check for coordinates
    if (!station.containsKey('latitude') ||
        !station.containsKey('longitude') ||
        station['latitude'] == null ||
        station['longitude'] == null) {
      return false;
    }

    return true;
  }

  /// Validate API endpoint response structure
  static bool validateApiResponse(dynamic response) {
    if (response == null) {
      debugPrint('$_logPrefix: Response is null');
      return false;
    }

    if (response is! Map<String, dynamic>) {
      debugPrint('$_logPrefix: Response is not a valid JSON object');
      return false;
    }

    if (!response.containsKey('success')) {
      debugPrint('$_logPrefix: Response missing success field');
      return false;
    }

    return true;
  }
}
