import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'connectivity_service.dart';
import 'connectivity_error_service.dart';

/// Global connectivity monitor that automatically shows/hides connectivity error page
/// based on real-time network status changes
class GlobalConnectivityMonitor {
  // Singleton pattern
  static final GlobalConnectivityMonitor _instance = GlobalConnectivityMonitor._internal();
  factory GlobalConnectivityMonitor() => _instance;
  GlobalConnectivityMonitor._internal();

  // Services
  final ConnectivityService _connectivityService = ConnectivityService();
  
  // State management
  bool _isInitialized = false;
  bool _isErrorPageShowing = false;
  StreamSubscription<bool>? _connectivitySubscription;
  BuildContext? _currentContext;
  
  // Navigation key for global navigation
  static GlobalKey<NavigatorState>? _navigatorKey;
  
  /// Initialize the global connectivity monitor
  /// Should be called once in main.dart after app initialization
  static void initialize(GlobalKey<NavigatorState> navigatorKey) {
    _navigatorKey = navigatorKey;
    _instance._initializeMonitoring();
  }
  
  /// Set the current context for navigation
  /// Should be called from the main app widget or root pages
  void setContext(BuildContext context) {
    _currentContext = context;
  }
  
  /// Initialize real-time connectivity monitoring
  void _initializeMonitoring() {
    if (_isInitialized) return;
    
    debugPrint('🌐 GLOBAL CONNECTIVITY MONITOR: Initializing real-time monitoring');
    
    // Initialize the connectivity service
    _connectivityService.initialize();
    
    // Listen to connectivity changes
    _connectivitySubscription = _connectivityService.connectionStatus.listen(
      _handleConnectivityChange,
      onError: (error) {
        debugPrint('🌐 GLOBAL CONNECTIVITY MONITOR: Error in connectivity stream: $error');
      },
    );
    
    // Perform initial connectivity check
    _performInitialConnectivityCheck();
    
    _isInitialized = true;
    debugPrint('🌐 GLOBAL CONNECTIVITY MONITOR: Initialization complete');
  }
  
  /// Perform initial connectivity check on app startup
  void _performInitialConnectivityCheck() async {
    try {
      final hasConnection = await _connectivityService.checkConnectionManually();
      debugPrint('🌐 GLOBAL CONNECTIVITY MONITOR: Initial connectivity check: ${hasConnection ? 'Connected' : 'Disconnected'}');
      
      if (!hasConnection) {
        // Small delay to ensure app is fully loaded
        await Future.delayed(const Duration(milliseconds: 500));
        _showConnectivityErrorPage();
      }
    } catch (e) {
      debugPrint('🌐 GLOBAL CONNECTIVITY MONITOR: Error in initial connectivity check: $e');
    }
  }
  
  /// Handle connectivity status changes
  void _handleConnectivityChange(bool hasConnection) {
    debugPrint('🌐 GLOBAL CONNECTIVITY MONITOR: Connectivity changed: ${hasConnection ? 'Connected' : 'Disconnected'}');
    debugPrint('🌐 GLOBAL CONNECTIVITY MONITOR: Error page currently showing: $_isErrorPageShowing');
    
    if (hasConnection && _isErrorPageShowing) {
      // Connection restored - hide error page
      _hideConnectivityErrorPage();
    } else if (!hasConnection && !_isErrorPageShowing) {
      // Connection lost - show error page
      _showConnectivityErrorPage();
    }
  }
  
  /// Show the connectivity error page
  void _showConnectivityErrorPage() {
    if (_isErrorPageShowing) {
      debugPrint('🌐 GLOBAL CONNECTIVITY MONITOR: Error page already showing, skipping');
      return;
    }
    
    final context = _getNavigationContext();
    if (context == null) {
      debugPrint('🌐 GLOBAL CONNECTIVITY MONITOR: No navigation context available');
      return;
    }
    
    debugPrint('🌐 GLOBAL CONNECTIVITY MONITOR: Showing connectivity error page');
    _isErrorPageShowing = true;
    
    // Show the connectivity error page with automatic retry
    ConnectivityErrorService.showConnectivityError(
      context,
      customMessage: 'You appear to be offline. The app will automatically reconnect when your internet connection is restored.',
      onRetry: () async {
        debugPrint('🌐 GLOBAL CONNECTIVITY MONITOR: Manual retry triggered');
        final hasConnection = await _connectivityService.checkConnectionManually();
        if (hasConnection) {
          _hideConnectivityErrorPage();
        }
      },
      replaceCurrentRoute: true,
    );
  }
  
  /// Hide the connectivity error page
  void _hideConnectivityErrorPage() {
    if (!_isErrorPageShowing) {
      debugPrint('🌐 GLOBAL CONNECTIVITY MONITOR: Error page not showing, skipping hide');
      return;
    }
    
    final context = _getNavigationContext();
    if (context == null) {
      debugPrint('🌐 GLOBAL CONNECTIVITY MONITOR: No navigation context available for hiding');
      return;
    }
    
    debugPrint('🌐 GLOBAL CONNECTIVITY MONITOR: Hiding connectivity error page');
    _isErrorPageShowing = false;
    
    // Navigate back to the previous screen
    if (Navigator.of(context).canPop()) {
      Navigator.of(context).pop();
    } else {
      // If we can't pop, navigate to the dashboard
      Navigator.of(context).pushNamedAndRemoveUntil(
        '/dashboard',
        (route) => false,
      );
    }
  }
  
  /// Get the current navigation context
  BuildContext? _getNavigationContext() {
    // Try to use the global navigator key first
    if (_navigatorKey?.currentContext != null) {
      return _navigatorKey!.currentContext!;
    }
    
    // Fallback to the stored context
    if (_currentContext != null && _currentContext!.mounted) {
      return _currentContext;
    }
    
    return null;
  }
  
  /// Check if the error page is currently showing
  bool get isErrorPageShowing => _isErrorPageShowing;
  
  /// Get current connectivity status
  bool get hasConnection => _connectivityService.hasConnection;
  
  /// Manually trigger connectivity check
  Future<bool> checkConnectivity() async {
    return await _connectivityService.checkConnectionManually();
  }
  
  /// Dispose resources
  void dispose() {
    debugPrint('🌐 GLOBAL CONNECTIVITY MONITOR: Disposing resources');
    _connectivitySubscription?.cancel();
    _connectivityService.dispose();
    _isInitialized = false;
    _isErrorPageShowing = false;
    _currentContext = null;
  }
}
