import 'package:flutter/material.dart';

/// Utility class for handling API errors
class ErrorHandler {
  /// Converts API error messages to user-friendly messages
  static String getUserFriendlyMessage(String apiErrorMessage) {
    // Log the actual error for debugging
    debugPrint('API Error: $apiErrorMessage');

    // Check for success messages that might be misinterpreted as errors
    if (apiErrorMessage.contains('OTP Verify') ||
        apiErrorMessage.contains('verified successfully') ||
        apiErrorMessage.contains('Verify') ||
        apiErrorMessage.contains('verification successful')) {
      debugPrint('\n=== SUCCESS MESSAGE DETECTED IN ERROR HANDLER ===');
      debugPrint('Message: $apiErrorMessage');
      debugPrint('Converting to success message');
      return "OTP verified successfully";
    }

    // Check for specific error patterns
    if (apiErrorMessage.contains('SQLSTATE') ||
        apiErrorMessage.contains('Data too long')) {
      return "There was an issue with your phone number format. Please try again.";
    } else if (apiErrorMessage.contains('Invalid OTP')) {
      return "Invalid OTP. Please check and try again.";
    } else if (apiErrorMessage.contains('not found') ||
        apiErrorMessage.contains('No user found')) {
      return "Account not found. Please check your phone number.";
    } else if (apiErrorMessage.contains('timeout') ||
        apiErrorMessage.contains('timed out')) {
      return "Connection timed out. Please try again.";
    } else if (apiErrorMessage.contains('Connection reset by peer')) {
      return "We're having trouble connecting to our servers. Please try again.";
    } else if (apiErrorMessage.contains('network') ||
        apiErrorMessage.contains('connection') ||
        apiErrorMessage.contains('SocketException') ||
        apiErrorMessage.contains('Failed to send OTP')) {
      return "Network error. Please try again.";
    } else if (apiErrorMessage.contains('rate limit') ||
        apiErrorMessage.contains('too many requests') ||
        apiErrorMessage.contains('429')) {
      return "Too many requests. Please try again in a moment.";
    } else if (apiErrorMessage.contains('unauthorized') ||
        apiErrorMessage.contains('invalid token') ||
        apiErrorMessage.contains('401')) {
      return "Session expired. Please log in again.";
    } else if (apiErrorMessage.contains('forbidden') ||
        apiErrorMessage.contains('403')) {
      return "You don't have permission to access this resource.";
    } else if (apiErrorMessage.contains('server error') ||
        apiErrorMessage.contains('500') ||
        apiErrorMessage.contains('502') ||
        apiErrorMessage.contains('503') ||
        apiErrorMessage.contains('504')) {
      return "Server error. Please try again later.";
    }

    // Default message for unknown errors - context-aware based on common patterns
    if (apiErrorMessage.toLowerCase().contains('login') ||
        apiErrorMessage.toLowerCase().contains('auth')) {
      return "Authentication failed. Please try again.";
    } else if (apiErrorMessage.toLowerCase().contains('profile') ||
        apiErrorMessage.toLowerCase().contains('user')) {
      return "Unable to update profile. Please try again.";
    } else if (apiErrorMessage.toLowerCase().contains('station') ||
        apiErrorMessage.toLowerCase().contains('charger')) {
      return "Connection problem. Please check your internet and try again.";
    }

    // Generic fallback message
    return "Something went wrong. Please try again.";
  }

  /// Get error message based on error code
  static String getErrorMessageByCode(String? errorCode) {
    if (errorCode == null) return "Something went wrong. Please try again.";

    switch (errorCode) {
      case 'CONNECTION_ERROR':
        return "Network error. Please check your connection and try again.";
      case 'CONNECTION_RESET':
        return "The server unexpectedly closed the connection. Please try again.";
      case 'TIMEOUT':
        return "The server is taking too long to respond. Please try again later.";
      case 'SERVER_ERROR':
        return "Server error. Please try again later.";
      case 'RATE_LIMIT_EXCEEDED':
        return "Too many requests. Please try again in a moment.";
      case 'AUTHENTICATION_ERROR':
        return "Authentication failed. Please log in again.";
      default:
        return "Something went wrong. Please try again.";
    }
  }
}
