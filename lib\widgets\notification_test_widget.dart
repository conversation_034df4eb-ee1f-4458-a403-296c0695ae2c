import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ecoplug/providers/notification_provider.dart';
import 'package:ecoplug/screens/settings/notification_settings_screen.dart';

/// Test widget for notification functionality
/// Shows notification status and provides test buttons
class NotificationTestWidget extends ConsumerWidget {
  const NotificationTestWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final notificationState = ref.watch(notificationProvider);
    final notificationNotifier = ref.watch(notificationProvider.notifier);
    final fcmTokenAsync = ref.watch(fcmTokenProvider);
    final theme = Theme.of(context);

    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.notifications,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Notification System',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const NotificationSettingsScreen(),
                      ),
                    );
                  },
                  icon: const Icon(Icons.settings),
                  tooltip: 'Notification Settings',
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Status Indicators
            _buildStatusRow(
              'Service Initialized',
              notificationState.isInitialized,
              theme,
            ),
            _buildStatusRow(
              'Notifications Enabled',
              notificationState.notificationsEnabled,
              theme,
            ),
            const SizedBox(height: 8),

            // FCM Token Status
            fcmTokenAsync.when(
              data: (token) => _buildTokenStatus(token, theme),
              loading: () => _buildLoadingStatus('Loading FCM token...', theme),
              error: (error, _) => _buildErrorStatus('FCM Error: $error', theme),
            ),
            const SizedBox(height: 16),

            // Service Status Summary
            if (notificationState.serviceStatus.isNotEmpty) ...[
              Text(
                'Service Status:',
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 4,
                children: notificationState.serviceStatus.entries.map((entry) {
                  return Chip(
                    label: Text(
                      _formatServiceName(entry.key),
                      style: theme.textTheme.bodySmall,
                    ),
                    backgroundColor: entry.value
                        ? theme.colorScheme.primaryContainer
                        : theme.colorScheme.errorContainer,
                    side: BorderSide.none,
                  );
                }).toList(),
              ),
              const SizedBox(height: 16),
            ],

            // Test Buttons
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton.icon(
                  onPressed: notificationState.isInitialized
                      ? () => _showTestNotification(notificationNotifier, context)
                      : null,
                  icon: const Icon(Icons.notification_add),
                  label: const Text('Test Local'),
                ),
                ElevatedButton.icon(
                  onPressed: notificationState.isInitialized
                      ? () => _showChargingNotification(notificationNotifier, context)
                      : null,
                  icon: const Icon(Icons.battery_charging_full),
                  label: const Text('Test Charging'),
                ),
                OutlinedButton.icon(
                  onPressed: () => _refreshStatus(notificationNotifier),
                  icon: const Icon(Icons.refresh),
                  label: const Text('Refresh'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusRow(String label, bool status, ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Icon(
            status ? Icons.check_circle : Icons.error,
            size: 16,
            color: status ? Colors.green : Colors.red,
          ),
          const SizedBox(width: 8),
          Text(
            label,
            style: theme.textTheme.bodyMedium,
          ),
        ],
      ),
    );
  }

  Widget _buildTokenStatus(String? token, ThemeData theme) {
    if (token == null) {
      return _buildErrorStatus('No FCM token available', theme);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.check_circle,
              size: 16,
              color: Colors.green,
            ),
            const SizedBox(width: 8),
            Text(
              'FCM Token Available',
              style: theme.textTheme.bodyMedium,
            ),
          ],
        ),
        const SizedBox(height: 4),
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: theme.colorScheme.surfaceVariant,
            borderRadius: BorderRadius.circular(4),
          ),
          child: Text(
            '${token.substring(0, 20)}...',
            style: theme.textTheme.bodySmall?.copyWith(
              fontFamily: 'monospace',
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLoadingStatus(String message, ThemeData theme) {
    return Row(
      children: [
        SizedBox(
          width: 16,
          height: 16,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            color: theme.colorScheme.primary,
          ),
        ),
        const SizedBox(width: 8),
        Text(
          message,
          style: theme.textTheme.bodyMedium,
        ),
      ],
    );
  }

  Widget _buildErrorStatus(String message, ThemeData theme) {
    return Row(
      children: [
        Icon(
          Icons.error,
          size: 16,
          color: Colors.red,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            message,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: Colors.red,
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _showTestNotification(
    NotificationNotifier notifier,
    BuildContext context,
  ) async {
    try {
      await notifier.showTestNotification();
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Test notification sent!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _showChargingNotification(
    NotificationNotifier notifier,
    BuildContext context,
  ) async {
    try {
      await notifier.showChargingNotification(
        title: 'Test Charging Session',
        body: 'Your EV is charging at 75%',
        chargePercentage: 0.75,
        isCharging: true,
        stationName: 'EcoPlug Test Station',
        connectorType: 'CCS2',
        powerKw: 50.0,
        energyKwh: 25.5,
        duration: const Duration(minutes: 45),
        cost: 125.50,
      );
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Charging notification sent!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _refreshStatus(NotificationNotifier notifier) async {
    await notifier.refreshStatus();
  }

  String _formatServiceName(String serviceName) {
    return serviceName
        .split('_')
        .map((word) => word[0].toUpperCase() + word.substring(1))
        .join(' ');
  }
}
