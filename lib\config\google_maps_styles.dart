/// Google Maps style configurations for light and dark themes
///
/// This file contains professional Google Maps themes:
/// - Standard theme for light/day mode (default Google Maps appearance with full details)
/// - Aubergine theme for dark/night mode (professional dark theme with excellent road visibility)
class GoogleMapsStyles {

  /// Standard Google Maps theme for light/day mode
  /// Uses default Google Maps styling with full road details, landmarks, and features
  /// Provides the best visibility and detail for daytime use
  static const String? lightMapStyle = null; // Use default Google Maps style

  /// Aubergine theme for dark/night mode
  /// Professional dark theme with excellent road visibility and contrast
  /// Similar to themes used by Uber and other professional navigation apps
  static const String darkMapStyle = '''
[
  {
    "elementType": "geometry",
    "stylers": [
      {
        "color": "#1d2c4d"
      }
    ]
  },
  {
    "elementType": "labels.text.fill",
    "stylers": [
      {
        "color": "#8ec3b9"
      }
    ]
  },
  {
    "elementType": "labels.text.stroke",
    "stylers": [
      {
        "color": "#1a3646"
      }
    ]
  },
  {
    "featureType": "administrative.country",
    "elementType": "geometry.stroke",
    "stylers": [
      {
        "color": "#4b6878"
      }
    ]
  },
  {
    "featureType": "administrative.land_parcel",
    "elementType": "labels.text.fill",
    "stylers": [
      {
        "color": "#64779f"
      }
    ]
  },
  {
    "featureType": "administrative.province",
    "elementType": "geometry.stroke",
    "stylers": [
      {
        "color": "#4b6878"
      }
    ]
  },
  {
    "featureType": "landscape.man_made",
    "elementType": "geometry.stroke",
    "stylers": [
      {
        "color": "#334e87"
      }
    ]
  },
  {
    "featureType": "landscape.natural",
    "elementType": "geometry",
    "stylers": [
      {
        "color": "#023e58"
      }
    ]
  },
  {
    "featureType": "poi",
    "elementType": "geometry",
    "stylers": [
      {
        "color": "#283d6a"
      }
    ]
  },
  {
    "featureType": "poi",
    "elementType": "labels.text.fill",
    "stylers": [
      {
        "color": "#6f9ba5"
      }
    ]
  },
  {
    "featureType": "poi",
    "elementType": "labels.text.stroke",
    "stylers": [
      {
        "color": "#1d2c4d"
      }
    ]
  },
  {
    "featureType": "poi.park",
    "elementType": "geometry.fill",
    "stylers": [
      {
        "color": "#023e58"
      }
    ]
  },
  {
    "featureType": "poi.park",
    "elementType": "labels.text.fill",
    "stylers": [
      {
        "color": "#3C7680"
      }
    ]
  },
  {
    "featureType": "road",
    "elementType": "geometry",
    "stylers": [
      {
        "color": "#304a7d"
      }
    ]
  },
  {
    "featureType": "road",
    "elementType": "labels.text.fill",
    "stylers": [
      {
        "color": "#98a5be"
      }
    ]
  },
  {
    "featureType": "road",
    "elementType": "labels.text.stroke",
    "stylers": [
      {
        "color": "#1d2c4d"
      }
    ]
  },
  {
    "featureType": "road.highway",
    "elementType": "geometry",
    "stylers": [
      {
        "color": "#2c6675"
      }
    ]
  },
  {
    "featureType": "road.highway",
    "elementType": "geometry.stroke",
    "stylers": [
      {
        "color": "#255763"
      }
    ]
  },
  {
    "featureType": "road.highway",
    "elementType": "labels.text.fill",
    "stylers": [
      {
        "color": "#b0d5df"
      }
    ]
  },
  {
    "featureType": "road.highway",
    "elementType": "labels.text.stroke",
    "stylers": [
      {
        "color": "#023e58"
      }
    ]
  },
  {
    "featureType": "transit",
    "elementType": "labels.text.fill",
    "stylers": [
      {
        "color": "#98a5be"
      }
    ]
  },
  {
    "featureType": "transit",
    "elementType": "labels.text.stroke",
    "stylers": [
      {
        "color": "#1d2c4d"
      }
    ]
  },
  {
    "featureType": "transit.line",
    "elementType": "geometry.fill",
    "stylers": [
      {
        "color": "#283d6a"
      }
    ]
  },
  {
    "featureType": "transit.station",
    "elementType": "geometry",
    "stylers": [
      {
        "color": "#3a4762"
      }
    ]
  },
  {
    "featureType": "water",
    "elementType": "geometry",
    "stylers": [
      {
        "color": "#0e1626"
      }
    ]
  },
  {
    "featureType": "water",
    "elementType": "labels.text.fill",
    "stylers": [
      {
        "color": "#4e6d70"
      }
    ]
  }
]
''';

  /// Get the appropriate map style based on theme mode
  ///
  /// [isDarkMode] - Whether the app is currently in dark mode
  /// Returns the JSON string for dark mode or null for light mode (default Google Maps style)
  static String? getMapStyle(bool isDarkMode) {
    return isDarkMode ? darkMapStyle : lightMapStyle;
  }

  /// Get a null style (default Google Maps style)
  /// Useful for fallback or when custom styling is disabled
  static String? getDefaultStyle() {
    return null;
  }

  /// Check if dark mode styling is supported
  /// Always returns true since we have both light and dark styles
  static bool isDarkModeSupported() {
    return true;
  }

  /// Get map style with fallback handling
  ///
  /// [isDarkMode] - Whether the app is currently in dark mode
  /// [enableCustomStyles] - Whether to use custom styles or fall back to default
  /// Returns the appropriate map style or null for default Google Maps style
  static String? getMapStyleWithFallback(bool isDarkMode, {bool enableCustomStyles = true}) {
    if (!enableCustomStyles) {
      return null; // Use default Google Maps style
    }

    try {
      return getMapStyle(isDarkMode);
    } catch (e) {
      // If there's any error with custom styles, fall back to default
      return null;
    }
  }

  /// Get theme description for debugging
  static String getThemeDescription(bool isDarkMode) {
    return isDarkMode
        ? 'Aubergine Dark Theme - Professional dark mode with excellent road visibility'
        : 'Standard Google Maps Theme - Default styling with full road details and landmarks';
  }
}
