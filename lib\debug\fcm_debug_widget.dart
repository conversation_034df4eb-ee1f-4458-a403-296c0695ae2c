import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/fcm_debug_service.dart';
import '../services/fcm_subscription_service.dart';

/// FCM Debug Widget for testing and verifying FCM functionality
class FCMDebugWidget extends StatefulWidget {
  const FCMDebugWidget({super.key});

  @override
  State<FCMDebugWidget> createState() => _FCMDebugWidgetState();
}

class _FCMDebugWidgetState extends State<FCMDebugWidget> {
  final FCMDebugService _debugService = FCMDebugService();
  final FCMSubscriptionService _subscriptionService = FCMSubscriptionService();
  final TextEditingController _topicController = TextEditingController();
  final TextEditingController _chargingIdController = TextEditingController();
  
  Map<String, dynamic> _subscriptionStatus = {};
  List<Map<String, dynamic>> _receivedMessages = [];
  bool _isLoading = false;
  String _lastResult = '';

  @override
  void initState() {
    super.initState();
    _initializeServices();
  }

  Future<void> _initializeServices() async {
    setState(() => _isLoading = true);
    
    try {
      await _debugService.initialize();
      await _subscriptionService.initialize();
      await _refreshStatus();
    } catch (e) {
      setState(() => _lastResult = 'Error initializing: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _refreshStatus() async {
    try {
      final status = await _debugService.getSubscriptionStatus();
      final messages = _debugService.getReceivedMessages();
      
      setState(() {
        _subscriptionStatus = status;
        _receivedMessages = messages;
      });
    } catch (e) {
      setState(() => _lastResult = 'Error refreshing status: $e');
    }
  }

  Future<void> _testTopicSubscription() async {
    final topic = _topicController.text.trim();
    if (topic.isEmpty) {
      setState(() => _lastResult = 'Please enter a topic name');
      return;
    }

    setState(() => _isLoading = true);
    
    try {
      final success = await _debugService.testTopicSubscription(topic);
      setState(() {
        _lastResult = success 
            ? '✅ Successfully subscribed to topic: $topic'
            : '❌ Failed to subscribe to topic: $topic';
      });
      await _refreshStatus();
    } catch (e) {
      setState(() => _lastResult = '❌ Error: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _testChargingSubscription() async {
    final chargingId = _chargingIdController.text.trim();
    if (chargingId.isEmpty) {
      setState(() => _lastResult = 'Please enter a charging ID');
      return;
    }

    setState(() => _isLoading = true);
    
    try {
      final success = await _subscriptionService.subscribeToChargingNotifications(chargingId);
      setState(() {
        _lastResult = success 
            ? '✅ Successfully subscribed to charging notifications: Charging_$chargingId'
            : '❌ Failed to subscribe to charging notifications';
      });
      await _refreshStatus();
    } catch (e) {
      setState(() => _lastResult = '❌ Error: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _generateReport() async {
    try {
      final report = await _debugService.generateDebugReport();
      await Clipboard.setData(ClipboardData(text: report));
      setState(() => _lastResult = '✅ Debug report copied to clipboard');
    } catch (e) {
      setState(() => _lastResult = '❌ Error generating report: $e');
    }
  }

  Future<void> _clearDebugData() async {
    try {
      await _debugService.clearDebugData();
      await _refreshStatus();
      setState(() => _lastResult = '✅ Debug data cleared');
    } catch (e) {
      setState(() => _lastResult = '❌ Error clearing data: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('FCM Debug Tool'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildStatusSection(),
                  const SizedBox(height: 20),
                  _buildTestingSection(),
                  const SizedBox(height: 20),
                  _buildMessagesSection(),
                  const SizedBox(height: 20),
                  _buildActionsSection(),
                ],
              ),
            ),
    );
  }

  Widget _buildStatusSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('FCM Status', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(height: 10),
            Text('FCM Token: ${_subscriptionStatus['fcm_token']?.toString().substring(0, 30) ?? 'Not available'}...'),
            Text('Subscribed Topics: ${_subscriptionStatus['subscribed_topics'] ?? []}'),
            Text('Messages Received: ${_subscriptionStatus['messages_received'] ?? 0}'),
            Text('Last Message: ${_subscriptionStatus['last_message_time'] ?? 'None'}'),
            const SizedBox(height: 10),
            ElevatedButton(
              onPressed: _refreshStatus,
              child: const Text('Refresh Status'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTestingSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Testing', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(height: 10),
            
            // Topic subscription test
            TextField(
              controller: _topicController,
              decoration: const InputDecoration(
                labelText: 'Topic Name',
                hintText: 'e.g., Charging_TRANSACTION_123',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 10),
            ElevatedButton(
              onPressed: _testTopicSubscription,
              child: const Text('Test Topic Subscription'),
            ),
            
            const SizedBox(height: 20),
            
            // Charging subscription test
            TextField(
              controller: _chargingIdController,
              decoration: const InputDecoration(
                labelText: 'Charging ID',
                hintText: 'e.g., TRANSACTION_123',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 10),
            ElevatedButton(
              onPressed: _testChargingSubscription,
              child: const Text('Test Charging Subscription'),
            ),
            
            const SizedBox(height: 10),
            if (_lastResult.isNotEmpty)
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: _lastResult.startsWith('✅') ? Colors.green.shade100 : Colors.red.shade100,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(_lastResult),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildMessagesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Recent Messages', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(height: 10),
            if (_receivedMessages.isEmpty)
              const Text('No FCM messages received yet')
            else
              ...(_receivedMessages.take(5).map((message) => _buildMessageTile(message))),
          ],
        ),
      ),
    );
  }

  Widget _buildMessageTile(Map<String, dynamic> message) {
    final data = message['data'] as Map<String, dynamic>? ?? {};
    final isChargingMessage = data['type'] == 'charging' || data.containsKey('soc');
    
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4),
      color: isChargingMessage ? Colors.green.shade50 : Colors.grey.shade50,
      child: ListTile(
        title: Text('${message['source']} - ${message['messageId']}'),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Time: ${message['timestamp']}'),
            if (isChargingMessage) ...[
              Text('SOC: ${data['soc'] ?? data['charge_percentage'] ?? 'N/A'}'),
              Text('Power: ${data['power'] ?? data['current_power'] ?? 'N/A'}'),
            ],
            Text('Data: ${data.toString()}'),
          ],
        ),
        isThreeLine: true,
      ),
    );
  }

  Widget _buildActionsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Actions', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(height: 10),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _generateReport,
                    child: const Text('Generate Report'),
                  ),
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _clearDebugData,
                    style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                    child: const Text('Clear Data'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _topicController.dispose();
    _chargingIdController.dispose();
    super.dispose();
  }
}
