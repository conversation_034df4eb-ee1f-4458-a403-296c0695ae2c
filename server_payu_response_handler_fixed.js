const express = require('express');
const mysql = require('mysql2/promise');
const router = express.Router();

// Database configuration - update with your actual database settings
const dbConfig = {
    host: 'localhost',
    user: 'your_db_user',
    password: 'your_db_password',
    database: 'your_database_name'
};

// FIXED PayU Response Handler Endpoint
router.post('/user/payment/response-payu', async (req, res) => {
    try {
        console.log('🔔 PayU Response received:', req.body);
        
        const { status, txnid, hash, response } = req.body;
        
        // 1. VALIDATE REQUIRED PARAMETERS
        if (!status || !txnid) {
            console.error('❌ Missing required parameters:', { status, txnid });
            return res.status(400).send('missing_parameters');
        }
        
        // 2. ENHANCED DATABASE TRANSACTION LOOKUP
        const transaction = await findTransactionByTxnId(txnid);
        
        if (!transaction) {
            console.error('❌ Transaction not found:', txnid);
            // Return "cancelled" to match client expectations
            return res.status(200).send('cancelled');
        }
        
        console.log('✅ Transaction found:', transaction);
        
        // 3. ENHANCED STATUS MAPPING AND UPDATE
        const newStatus = mapPayUStatusEnhanced(status);
        
        await updateTransactionStatusEnhanced(transaction.id, newStatus, {
            payu_response: JSON.stringify(response || {}),
            payu_hash: hash || '',
            payu_status: status,
            updated_at: new Date()
        });
        
        console.log(`✅ Transaction ${txnid} updated: ${transaction.status} → ${newStatus}`);
        
        // 4. RETURN STRING RESPONSE (as expected by client)
        res.status(200).send(status);
        
    } catch (error) {
        console.error('❌ PayU processing error:', error);
        res.status(500).send('error');
    }
});

// ENHANCED: Find transaction by multiple possible fields
async function findTransactionByTxnId(txnid) {
    const connection = await mysql.createConnection(dbConfig);
    
    try {
        // Try multiple possible transaction ID fields
        const queries = [
            // Try exact txnid match
            'SELECT * FROM payment_history WHERE txnid = ? LIMIT 1',
            // Try transaction_id field
            'SELECT * FROM payment_history WHERE transaction_id = ? LIMIT 1',
            // Try id field (if txnid is actually the primary key)
            'SELECT * FROM payment_history WHERE id = ? LIMIT 1',
            // Try remark field (sometimes txnid is stored in remark)
            'SELECT * FROM payment_history WHERE remark LIKE ? LIMIT 1'
        ];
        
        for (const query of queries) {
            const searchValue = query.includes('LIKE') ? `%${txnid}%` : txnid;
            const [rows] = await connection.execute(query, [searchValue]);
            
            if (rows.length > 0) {
                console.log(`✅ Transaction found using query: ${query}`);
                return rows[0];
            }
        }
        
        console.log('❌ Transaction not found with any query');
        return null;
        
    } catch (error) {
        console.error('❌ Database query error:', error);
        throw error;
    } finally {
        await connection.end();
    }
}

// ENHANCED: Better status mapping
function mapPayUStatusEnhanced(payuStatus) {
    const statusLower = payuStatus.toLowerCase();
    
    const statusMapping = {
        // Success statuses
        'success': 'COMPLETED',
        'successful': 'COMPLETED',
        'completed': 'COMPLETED',
        'complete': 'COMPLETED',
        
        // Failure statuses
        'failed': 'REJECTED',
        'failure': 'REJECTED',
        'cancelled': 'REJECTED',
        'canceled': 'REJECTED',
        'rejected': 'REJECTED',
        'error': 'REJECTED',
        
        // Pending statuses
        'pending': 'PENDING',
        'processing': 'PENDING',
        'initiated': 'PENDING',
        'in_progress': 'PENDING',
        
        // Timeout/Unknown
        'timeout': 'REJECTED',
        'unknown': 'REJECTED'
    };
    
    const mappedStatus = statusMapping[statusLower];
    
    if (mappedStatus) {
        console.log(`✅ Status mapped: ${payuStatus} → ${mappedStatus}`);
        return mappedStatus;
    } else {
        console.log(`⚠️ Unknown status: ${payuStatus}, defaulting to REJECTED`);
        return 'REJECTED';
    }
}

// ENHANCED: Better transaction status update
async function updateTransactionStatusEnhanced(transactionId, newStatus, additionalData = {}) {
    const connection = await mysql.createConnection(dbConfig);
    
    try {
        // First, get current transaction details
        const [currentRows] = await connection.execute(
            'SELECT * FROM payment_history WHERE id = ?',
            [transactionId]
        );
        
        if (currentRows.length === 0) {
            throw new Error(`Transaction ${transactionId} not found for update`);
        }
        
        const currentTransaction = currentRows[0];
        console.log(`📊 Current transaction status: ${currentTransaction.status}`);
        
        // Update the transaction with enhanced data
        await connection.execute(`
            UPDATE payment_history 
            SET status = ?, 
                remark = ?,
                payu_response = ?,
                payu_hash = ?,
                payu_status = ?,
                updated_at = NOW()
            WHERE id = ?
        `, [
            newStatus,
            getRemarkForStatus(newStatus),
            additionalData.payu_response || '',
            additionalData.payu_hash || '',
            additionalData.payu_status || '',
            transactionId
        ]);
        
        // Verify the update was successful
        const [updatedRows] = await connection.execute(
            'SELECT * FROM payment_history WHERE id = ?',
            [transactionId]
        );
        
        if (updatedRows.length > 0) {
            const updatedTransaction = updatedRows[0];
            console.log(`✅ Database updated successfully: ${currentTransaction.status} → ${updatedTransaction.status}`);
        } else {
            console.error('❌ Failed to verify database update');
        }
        
    } catch (error) {
        console.error('❌ Database update error:', error);
        throw error;
    } finally {
        await connection.end();
    }
}

// Enhanced remark generation
function getRemarkForStatus(status) {
    const remarkMapping = {
        'COMPLETED': 'Payment Successful - Wallet Balance Added',
        'REJECTED': 'Payment Failed - Transaction Cancelled',
        'PENDING': 'Payment Processing - Please Wait'
    };
    
    return remarkMapping[status] || 'Payment Status Updated';
}

// Health check endpoint
router.get('/payu/health', (req, res) => {
    res.json({
        status: 'healthy',
        service: 'PayU Response Handler',
        timestamp: new Date().toISOString()
    });
});

module.exports = router;

// Usage in your main server file (app.js or server.js):
// const payuHandler = require('./server_payu_response_handler_fixed');
// app.use('/api/v1', payuHandler);
