import 'dart:convert';
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:ecoplug/models/notification_history_item.dart';

/// Notification Storage Service
/// Handles persistent storage and retrieval of notification history
/// Provides efficient data management with automatic cleanup and optimization
class NotificationStorageService {
  static final NotificationStorageService _instance =
      NotificationStorageService._internal();
  factory NotificationStorageService() => _instance;
  NotificationStorageService._internal();

  static const String _storageKey = 'notification_history';
  static const String _lastCleanupKey = 'notification_last_cleanup';
  static const int _maxNotifications = 500; // Maximum notifications to store
  static const int _cleanupIntervalDays =
      7; // Cleanup old notifications every 7 days
  static const int _maxAgeDays = 30; // Keep notifications for 30 days

  SharedPreferences? _prefs;
  List<NotificationHistoryItem> _cachedNotifications = [];
  bool _isInitialized = false;

  /// Stream controller for notification updates
  final StreamController<List<NotificationHistoryItem>>
      _notificationStreamController =
      StreamController<List<NotificationHistoryItem>>.broadcast();

  /// Stream of notification updates
  Stream<List<NotificationHistoryItem>> get notificationStream =>
      _notificationStreamController.stream;

  /// Initialize the storage service
  Future<void> initialize() async {
    if (_isInitialized) {
      debugPrint('📱 Notification Storage Service already initialized');
      return;
    }

    try {
      debugPrint('📱 ===== INITIALIZING NOTIFICATION STORAGE SERVICE =====');

      _prefs = await SharedPreferences.getInstance();
      await _loadNotifications();
      await _performCleanupIfNeeded();

      _isInitialized = true;
      debugPrint('✅ Notification Storage Service initialized successfully');
      debugPrint(
          '📱 Loaded ${_cachedNotifications.length} notifications from storage');
    } catch (e) {
      debugPrint('❌ Error initializing Notification Storage Service: $e');
      rethrow;
    }
  }

  /// Add a new notification to storage
  Future<void> addNotification(NotificationHistoryItem notification) async {
    if (!_isInitialized) await initialize();

    try {
      debugPrint('📱 Adding notification: ${notification.title}');

      // Check for duplicates
      final existingIndex =
          _cachedNotifications.indexWhere((n) => n.id == notification.id);
      if (existingIndex != -1) {
        debugPrint(
            '⚠️ Notification with ID ${notification.id} already exists, updating');
        _cachedNotifications[existingIndex] = notification;
      } else {
        // Add to beginning of list (newest first)
        _cachedNotifications.insert(0, notification);
      }

      // Limit the number of stored notifications
      if (_cachedNotifications.length > _maxNotifications) {
        _cachedNotifications =
            _cachedNotifications.take(_maxNotifications).toList();
        debugPrint('📱 Trimmed notifications to $_maxNotifications items');
      }

      await _saveNotifications();
      _notificationStreamController.add(List.from(_cachedNotifications));

      debugPrint('✅ Notification added successfully');
    } catch (e) {
      debugPrint('❌ Error adding notification: $e');
    }
  }

  /// Get all notifications
  Future<List<NotificationHistoryItem>> getAllNotifications() async {
    if (!_isInitialized) await initialize();
    return List.from(_cachedNotifications);
  }

  /// Get notifications by type
  Future<List<NotificationHistoryItem>> getNotificationsByType(
      NotificationType type) async {
    if (!_isInitialized) await initialize();
    return _cachedNotifications.where((n) => n.type == type).toList();
  }

  /// Get unread notifications
  Future<List<NotificationHistoryItem>> getUnreadNotifications() async {
    if (!_isInitialized) await initialize();
    return _cachedNotifications.where((n) => !n.isRead).toList();
  }

  /// Get notifications by source
  Future<List<NotificationHistoryItem>> getNotificationsBySource(
      NotificationSource source) async {
    if (!_isInitialized) await initialize();
    return _cachedNotifications.where((n) => n.source == source).toList();
  }

  /// Mark notification as read
  Future<void> markAsRead(String notificationId) async {
    if (!_isInitialized) await initialize();

    try {
      final index =
          _cachedNotifications.indexWhere((n) => n.id == notificationId);
      if (index != -1) {
        _cachedNotifications[index] = _cachedNotifications[index].markAsRead();
        await _saveNotifications();
        _notificationStreamController.add(List.from(_cachedNotifications));
        debugPrint('✅ Notification $notificationId marked as read');
      }
    } catch (e) {
      debugPrint('❌ Error marking notification as read: $e');
    }
  }

  /// Mark all notifications as read
  Future<void> markAllAsRead() async {
    if (!_isInitialized) await initialize();

    try {
      _cachedNotifications =
          _cachedNotifications.map((n) => n.markAsRead()).toList();
      await _saveNotifications();
      _notificationStreamController.add(List.from(_cachedNotifications));
      debugPrint('✅ All notifications marked as read');
    } catch (e) {
      debugPrint('❌ Error marking all notifications as read: $e');
    }
  }

  /// Delete notification
  Future<void> deleteNotification(String notificationId) async {
    if (!_isInitialized) await initialize();

    try {
      _cachedNotifications.removeWhere((n) => n.id == notificationId);
      await _saveNotifications();
      _notificationStreamController.add(List.from(_cachedNotifications));
      debugPrint('✅ Notification $notificationId deleted');
    } catch (e) {
      debugPrint('❌ Error deleting notification: $e');
    }
  }

  /// Clear all notifications
  Future<void> clearAllNotifications() async {
    if (!_isInitialized) await initialize();

    try {
      _cachedNotifications.clear();
      await _saveNotifications();
      _notificationStreamController.add(List.from(_cachedNotifications));
      debugPrint('✅ All notifications cleared');
    } catch (e) {
      debugPrint('❌ Error clearing notifications: $e');
    }
  }

  /// Clear all sample/test notifications (production-safe cleanup)
  Future<void> clearSampleNotifications() async {
    if (!_isInitialized) await initialize();

    try {
      final originalCount = _cachedNotifications.length;

      // Remove notifications that match test/sample patterns
      _cachedNotifications.removeWhere((notification) {
        final title = notification.title.toLowerCase();
        final body = notification.body.toLowerCase();
        final data = notification.data ?? {};

        // Check for test/sample patterns
        return title.contains('test') ||
            title.contains('sample') ||
            title.contains('debug') ||
            body.contains('test') ||
            body.contains('sample') ||
            data.containsKey('session_id') &&
                data['session_id'].toString().contains('test') ||
            data.containsKey('transaction_id') &&
                data['transaction_id'].toString().contains('txn_001') ||
            data.containsKey('station_id') &&
                data['station_id'].toString().contains('koramangala_001') ||
            data.containsKey('offer_code') &&
                data['offer_code'].toString().contains('WEEKEND15');
      });

      final removedCount = originalCount - _cachedNotifications.length;

      if (removedCount > 0) {
        await _saveNotifications();
        _notificationStreamController.add(List.from(_cachedNotifications));
        debugPrint('✅ Removed $removedCount sample notifications');
      } else {
        debugPrint('✅ No sample notifications found to remove');
      }
    } catch (e) {
      debugPrint('❌ Error clearing sample notifications: $e');
    }
  }

  /// Get notification count
  Future<int> getNotificationCount() async {
    if (!_isInitialized) await initialize();
    return _cachedNotifications.length;
  }

  /// Get unread notification count
  Future<int> getUnreadCount() async {
    if (!_isInitialized) await initialize();
    return _cachedNotifications.where((n) => !n.isRead).length;
  }

  /// Load notifications from storage
  Future<void> _loadNotifications() async {
    try {
      final String? notificationsJson = _prefs?.getString(_storageKey);
      if (notificationsJson != null) {
        final List<dynamic> notificationsList = jsonDecode(notificationsJson);
        _cachedNotifications = notificationsList
            .map((json) => NotificationHistoryItem.fromJson(json))
            .toList();

        // Sort by timestamp (newest first)
        _cachedNotifications.sort((a, b) => b.timestamp.compareTo(a.timestamp));

        debugPrint(
            '📱 Loaded ${_cachedNotifications.length} notifications from storage');
      } else {
        debugPrint('📱 No stored notifications found');
      }
    } catch (e) {
      debugPrint('❌ Error loading notifications: $e');
      _cachedNotifications = [];
    }
  }

  /// Save notifications to storage
  Future<void> _saveNotifications() async {
    try {
      final List<Map<String, dynamic>> notificationsJson =
          _cachedNotifications.map((n) => n.toJson()).toList();
      await _prefs?.setString(_storageKey, jsonEncode(notificationsJson));
    } catch (e) {
      debugPrint('❌ Error saving notifications: $e');
    }
  }

  /// Perform cleanup if needed
  Future<void> _performCleanupIfNeeded() async {
    try {
      final int? lastCleanup = _prefs?.getInt(_lastCleanupKey);
      final int now = DateTime.now().millisecondsSinceEpoch;

      if (lastCleanup == null ||
          (now - lastCleanup) > (_cleanupIntervalDays * 24 * 60 * 60 * 1000)) {
        await _performCleanup();
        await _prefs?.setInt(_lastCleanupKey, now);
        debugPrint('✅ Notification cleanup completed');
      }
    } catch (e) {
      debugPrint('❌ Error during cleanup: $e');
    }
  }

  /// Perform cleanup of old notifications
  Future<void> _performCleanup() async {
    try {
      final DateTime cutoffDate =
          DateTime.now().subtract(Duration(days: _maxAgeDays));
      final int originalCount = _cachedNotifications.length;

      _cachedNotifications.removeWhere(
          (notification) => notification.timestamp.isBefore(cutoffDate));

      if (_cachedNotifications.length != originalCount) {
        await _saveNotifications();
        debugPrint(
            '📱 Cleaned up ${originalCount - _cachedNotifications.length} old notifications');
      }
    } catch (e) {
      debugPrint('❌ Error during notification cleanup: $e');
    }
  }

  /// Get storage statistics
  Map<String, dynamic> getStorageStats() {
    return {
      'total_notifications': _cachedNotifications.length,
      'unread_notifications':
          _cachedNotifications.where((n) => !n.isRead).length,
      'fcm_notifications': _cachedNotifications
          .where((n) => n.source == NotificationSource.fcm)
          .length,
      'local_notifications': _cachedNotifications
          .where((n) => n.source == NotificationSource.local)
          .length,
      'charging_notifications': _cachedNotifications
          .where((n) => n.type == NotificationType.charging)
          .length,
      'payment_notifications': _cachedNotifications
          .where((n) => n.type == NotificationType.payment)
          .length,
      'is_initialized': _isInitialized,
      'max_notifications': _maxNotifications,
      'max_age_days': _maxAgeDays,
    };
  }

  /// Dispose of the service
  void dispose() {
    _notificationStreamController.close();
    debugPrint('📱 Notification Storage Service disposed');
  }
}
