# FCM Token Generation Test Guide

## 🎯 **Quick Start - Run FCM Token Tests**

### **Method 1: Use the Standalone FCM Token Test Screen**

Add this to your app to test FCM token generation:

```dart
import 'package:ecoplug/examples/fcm_token_test_screen.dart';

// Navigate to FCM token test screen
Navigator.push(context, MaterialPageRoute(
  builder: (context) => FCMTokenTestScreen(),
));
```

### **Method 2: Use the Complete Firebase Integration Test Screen**

```dart
import 'package:ecoplug/examples/firebase_integration_test_screen.dart';

// Navigate to complete Firebase test screen
Navigator.push(context, MaterialPageRoute(
  builder: (context) => FirebaseIntegrationTestScreen(),
));
```

### **Method 3: Use the FCM Token Test Widget**

Add this widget to any existing screen:

```dart
import 'package:ecoplug/widgets/fcm_token_test_widget.dart';

// Add to any screen
FCMTokenTestWidget()
```

### **Method 4: Programmatic Testing**

```dart
import 'package:ecoplug/tests/fcm_token_test_service.dart';

// Quick token test
final testService = FCMTokenTestService();
final result = await testService.quickTokenTest();

if (result['success'] == true) {
  final token = result['token'] as String;
  print('FCM Token: $token');
} else {
  print('Token generation failed: ${result['message']}');
}
```

## 🧪 **Test Features Available**

### **1. Quick Token Test**
- **Purpose**: Fast FCM token generation test
- **Duration**: ~1-2 seconds
- **Returns**: Token string, length, validation status

### **2. Comprehensive Test Suite**
- **Purpose**: Complete FCM system validation
- **Duration**: ~5-10 seconds
- **Tests**:
  - ✅ Firebase Core initialization
  - ✅ FCM service availability
  - ✅ Token generation
  - ✅ Token validation
  - ✅ Token refresh functionality
  - ✅ Notification permissions

### **3. Token Validation**
- **Length Check**: Validates token is 100+ characters
- **Format Check**: Validates token format
- **Pattern Check**: Validates FCM token patterns

### **4. Token Refresh Test**
- **Purpose**: Tests token refresh capability
- **Process**: Deletes current token → Generates new token
- **Validation**: Ensures new token is different

## 📱 **Expected Test Results**

### **✅ Successful Token Generation**
```
🔥 ✅ FCM Token generated successfully!
🔥 Token length: 152
🔥 Token preview: eF5rHjHgTkuGiuhV9VWnOZ:APA91b...
🔥 Generation time: 1247ms
```

### **📊 Test Summary Example**
```
Overall Status: PASSED
Tests: 6/6 passed (100%)
FCM Token Ready: Yes
```

### **🔍 Individual Test Results**
- **Firebase Core Test**: ✅ PASSED
- **FCM Service Test**: ✅ PASSED  
- **Token Generation Test**: ✅ PASSED
- **Token Validation Test**: ✅ PASSED
- **Token Refresh Test**: ✅ PASSED
- **Permissions Test**: ⚠️ WARNING (if permissions not granted)

## 🎯 **How to Use Generated FCM Token**

### **1. Copy Token from Test Interface**
- Use "Copy Full Token" button in any test interface
- Token is automatically copied to clipboard

### **2. Test with Firebase Console**
1. Go to: https://console.firebase.google.com/
2. Select project: `ecoplug-9ab21`
3. Navigate to: Cloud Messaging → Send your first message
4. Enter notification details:
   - **Title**: "Test FCM Notification"
   - **Body**: "Testing token generation"
5. **Target**: Single device
6. **FCM Token**: Paste your copied token
7. **Send the message**

### **3. Test with Backend Team**
Share the FCM token with your backend team for server-side testing:

```json
{
  "to": "YOUR_FCM_TOKEN_HERE",
  "notification": {
    "title": "Test from Backend",
    "body": "FCM integration is working!"
  },
  "data": {
    "type": "test_notification",
    "timestamp": "2024-01-01T00:00:00Z"
  }
}
```

### **4. Test with curl Command**
```bash
curl -X POST https://fcm.googleapis.com/fcm/send \
  -H "Authorization: key=YOUR_SERVER_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "to": "YOUR_FCM_TOKEN",
    "notification": {
      "title": "Test Notification",
      "body": "Testing FCM integration"
    }
  }'
```

## 🔧 **Troubleshooting**

### **❌ Token Generation Failed**

**Possible Causes:**
1. Firebase not initialized
2. Google Services configuration missing
3. Network connectivity issues
4. App permissions not granted

**Solutions:**
1. Check Firebase initialization in `main.dart`
2. Verify `google-services.json` is correct
3. Check internet connection
4. Grant notification permissions

### **⚠️ Token Generated but Notifications Not Received**

**Check:**
1. **Notification Permissions**: Use permissions test
2. **App State**: Test in foreground, background, and terminated states
3. **Server Key**: Verify backend team has correct Firebase server key
4. **Token Validity**: Use token refresh test

### **🔄 Token Refresh Issues**

**Common Issues:**
1. Same token returned (cached)
2. Network timeout during refresh
3. Firebase service unavailable

**Solutions:**
1. Wait longer between delete and regenerate
2. Check network connectivity
3. Restart app and try again

## 📊 **Test Results Interpretation**

### **Status Meanings:**
- **✅ PASSED**: Test completed successfully
- **⚠️ WARNING**: Test completed with warnings (usually permissions)
- **❌ FAILED**: Test failed - needs attention
- **⏭️ SKIPPED**: Test skipped due to dependency failure

### **Key Metrics:**
- **Token Length**: Should be 100+ characters (typically 152)
- **Generation Time**: Should be under 5 seconds
- **Success Rate**: Should be 100% for core tests
- **FCM Ready**: Should be `true` for production use

## 🚀 **Production Readiness Checklist**

### **Before Going Live:**
- ✅ FCM token generation works consistently
- ✅ Token refresh functionality works
- ✅ Notifications received in all app states
- ✅ Backend integration tested
- ✅ Notification permissions granted
- ✅ Firebase project configuration verified

### **Monitoring in Production:**
- Track token generation success rate
- Monitor token refresh frequency
- Log notification delivery rates
- Track permission grant rates

## 📞 **Support**

### **If Tests Fail:**
1. **Check Firebase Configuration**: Use Firebase Configuration Test
2. **Verify Project Settings**: Ensure project ID matches `ecoplug-9ab21`
3. **Check Network**: Ensure device has internet connectivity
4. **Review Logs**: Check debug console for detailed error messages
5. **Contact Backend Team**: For server-side configuration issues

### **Common Error Messages:**
- `"Firebase not initialized"` → Check `main.dart` Firebase initialization
- `"Token is null or empty"` → Check network and Firebase configuration
- `"FCM not supported"` → Check device compatibility
- `"Permissions denied"` → Grant notification permissions

Your FCM token generation system is now ready for comprehensive testing! 🎉🔥
