import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/services.dart';

import '../models/place_suggestion.dart';
import '../providers/places_provider.dart';
import '../services/route_search_history_service.dart';
import '../utils/app_themes.dart';

/// Callback for when a location is selected
typedef LocationSelectedCallback = void Function(PlaceSuggestion suggestion);

/// Callback for when a recent route is selected
typedef RecentRouteSelectedCallback = void Function(RouteSearchHistory route);

/// Reusable location search widget with Google Places integration
class LocationSearchWidget extends ConsumerStatefulWidget {
  final String hintText;
  final IconData prefixIcon;
  final Color iconColor;
  final LocationSelectedCallback onLocationSelected;
  final bool showCurrentLocation;
  final bool showRecentRoutes;
  final RecentRouteSelectedCallback? onRecentRouteSelected;

  const LocationSearchWidget({
    super.key,
    required this.hintText,
    required this.prefixIcon,
    required this.iconColor,
    required this.onLocationSelected,
    this.showCurrentLocation = true,
    this.showRecentRoutes = false,
    this.onRecentRouteSelected,
  });

  @override
  ConsumerState<LocationSearchWidget> createState() => _LocationSearchWidgetState();
}

class _LocationSearchWidgetState extends ConsumerState<LocationSearchWidget> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  final RouteSearchHistoryService _historyService = RouteSearchHistoryService();
  List<RouteSearchHistory> _recentRoutes = [];
  bool _isLoadingHistory = false;

  @override
  void initState() {
    super.initState();

    // Load recent routes if enabled
    if (widget.showRecentRoutes) {
      _loadRecentRoutes();
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  /// Load recent routes from storage
  Future<void> _loadRecentRoutes() async {
    if (!widget.showRecentRoutes) return;

    setState(() {
      _isLoadingHistory = true;
    });

    try {
      final routes = await _historyService.getRouteSearchHistory();
      if (mounted) {
        setState(() {
          _recentRoutes = routes;
          _isLoadingHistory = false;
        });
      }
    } catch (e) {
      debugPrint('❌ HISTORY: Error loading recent routes: $e');
      if (mounted) {
        setState(() {
          _isLoadingHistory = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final searchState = ref.watch(placeSearchProvider);
    final currentLocationAsync = ref.watch(currentLocationProvider);
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Column(
      children: [
        // Search TextField
        Container(
          decoration: BoxDecoration(
            color: isDarkMode ? AppThemes.darkCard : Colors.grey[200],
            borderRadius: BorderRadius.circular(10),
          ),
          child: TextField(
            controller: _searchController,
            focusNode: _focusNode,
            autofocus: true,
            decoration: InputDecoration(
              hintText: widget.hintText,
              prefixIcon: Icon(
                widget.prefixIcon,
                color: widget.iconColor,
              ),
              suffixIcon: _searchController.text.isNotEmpty
                      ? IconButton(
                          icon: const Icon(Icons.clear),
                          onPressed: () {
                            _searchController.clear();
                            ref.read(placeSearchProvider.notifier).clearSearch();
                          },
                        )
                      : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
                borderSide: BorderSide.none,
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
                borderSide: BorderSide.none,
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(10),
                borderSide: BorderSide.none,
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 16,
              ),
            ),
            onChanged: (value) {
              ref.read(placeSearchProvider.notifier).searchPlaces(value);
            },
          ),
        ),

        const SizedBox(height: 16),

        // Search Results
        Expanded(
          child: _buildSearchResults(searchState, currentLocationAsync, isDarkMode),
        ),
      ],
    );
  }

  Widget _buildSearchResults(
    PlaceSearchState searchState,
    AsyncValue<PlaceSuggestion?> currentLocationAsync,
    bool isDarkMode,
  ) {
    // Show recent routes and current location when no search query
    if (searchState.query.isEmpty) {
      return _buildEmptyStateWithOptions(currentLocationAsync, isDarkMode);
    }

    // Show search results
    if (searchState.suggestions.isNotEmpty) {
      return ListView.builder(
        itemCount: searchState.suggestions.length,
        itemBuilder: (context, index) {
          final suggestion = searchState.suggestions[index];
          return _buildLocationTile(
            suggestion,
            Icons.location_on,
            widget.iconColor,
            isDarkMode,
          );
        },
      );
    }

    // Show error message
    if (searchState.error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 48,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              searchState.error!,
              textAlign: TextAlign.center,
              style: TextStyle(
                color: isDarkMode ? Colors.red[300] : Colors.red,
              ),
            ),
          ],
        ),
      );
    }

    // Show empty state without loading indicators
    if (searchState.query.isNotEmpty) {
      return const Center(
        child: Text('No locations found'),
      );
    }

    return const Center(
      child: Text('Start typing to search for locations'),
    );
  }

  /// Build empty state with recent routes and current location options
  Widget _buildEmptyStateWithOptions(
    AsyncValue<PlaceSuggestion?> currentLocationAsync,
    bool isDarkMode,
  ) {
    final List<Widget> children = [];

    // Add recent routes section if enabled and available
    if (widget.showRecentRoutes && _recentRoutes.isNotEmpty) {
      children.add(_buildRecentRoutesSection(isDarkMode));
      children.add(const SizedBox(height: 16));
    }

    // Add current location option if enabled
    if (widget.showCurrentLocation) {
      children.add(_buildCurrentLocationSection(currentLocationAsync, isDarkMode));
    }

    // If no options available, show default message
    if (children.isEmpty) {
      return const Center(
        child: Text('Start typing to search for locations'),
      );
    }

    return ListView(
      children: children,
    );
  }

  /// Build recent routes section
  Widget _buildRecentRoutesSection(bool isDarkMode) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            children: [
              Icon(
                Icons.history,
                size: 18,
                color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
              ),
              const SizedBox(width: 8),
              Text(
                'Recent Routes',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: isDarkMode ? Colors.grey[300] : Colors.grey[700],
                ),
              ),
            ],
          ),
        ),
        ..._recentRoutes.map((route) => _buildRecentRouteTile(route, isDarkMode)),
      ],
    );
  }

  /// Build current location section
  Widget _buildCurrentLocationSection(
    AsyncValue<PlaceSuggestion?> currentLocationAsync,
    bool isDarkMode,
  ) {
    return currentLocationAsync.when(
      data: (currentLocation) {
        if (currentLocation != null) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (widget.showRecentRoutes && _recentRoutes.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Row(
                    children: [
                      Icon(
                        Icons.my_location,
                        size: 18,
                        color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Current Location',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: isDarkMode ? Colors.grey[300] : Colors.grey[700],
                        ),
                      ),
                    ],
                  ),
                ),
              _buildLocationTile(
                currentLocation,
                Icons.my_location,
                Colors.blue,
                isDarkMode,
              ),
            ],
          );
        }
        return const Center(
          child: Text('Start typing to search for locations'),
        );
      },
      loading: () => const Center(
        child: Text('Start typing to search for locations'),
      ),
      error: (error, stack) => const Center(
        child: Text('Start typing to search for locations'),
      ),
    );
  }

  /// Build recent route tile
  Widget _buildRecentRouteTile(RouteSearchHistory route, bool isDarkMode) {
    return ListTile(
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: AppThemes.primaryColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          Icons.route,
          color: AppThemes.primaryColor,
          size: 20,
        ),
      ),
      title: Text(
        route.shortDisplayText,
        style: TextStyle(
          fontWeight: FontWeight.w500,
          color: isDarkMode ? Colors.white : Colors.black87,
          fontSize: 14,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      subtitle: Text(
        'Tap to use this route',
        style: TextStyle(
          color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
          fontSize: 12,
        ),
      ),
      trailing: Icon(
        Icons.arrow_forward_ios,
        size: 14,
        color: isDarkMode ? Colors.grey[500] : Colors.grey[400],
      ),
      onTap: () {
        HapticFeedback.selectionClick();
        if (widget.onRecentRouteSelected != null) {
          widget.onRecentRouteSelected!(route);
        }
      },
    );
  }

  Widget _buildLocationTile(
    PlaceSuggestion suggestion,
    IconData icon,
    Color iconColor,
    bool isDarkMode,
  ) {
    return ListTile(
      leading: Icon(icon, color: iconColor),
      title: Text(
        suggestion.mainText,
        style: TextStyle(
          fontWeight: FontWeight.w500,
          color: isDarkMode ? Colors.white : Colors.black87,
        ),
      ),
      subtitle: suggestion.secondaryText.isNotEmpty
          ? Text(
              suggestion.secondaryText,
              style: TextStyle(
                color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
              ),
            )
          : null,
      onTap: () async {
        // If suggestion already has coordinates, use it directly
        if (suggestion.coordinates != null) {
          widget.onLocationSelected(suggestion);
          return;
        }

        // Otherwise, get place details to fetch coordinates
        final placeDetails = await ref
            .read(placeSearchProvider.notifier)
            .getPlaceDetails(suggestion.placeId);

        if (placeDetails != null) {
          widget.onLocationSelected(placeDetails);
        } else {
          // Show error if place details couldn't be fetched
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Failed to get location details'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      },
    );
  }
}
