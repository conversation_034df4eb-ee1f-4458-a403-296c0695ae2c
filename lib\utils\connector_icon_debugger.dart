import 'package:flutter/foundation.dart' show debugPrint;

/// A utility class to debug connector icon issues
class ConnectorIconDebugger {
  /// Trace connector icon URLs through the app
  static void traceConnectorIcons(dynamic data, {String source = 'Unknown'}) {
    try {
      debugPrint('=== CONNECTOR ICON TRACE (Source: $source) ===');
      
      // Handle different data types
      if (data is Map<String, dynamic>) {
        _traceMapData(data, source: source);
      } else if (data is List) {
        _traceListData(data, source: source);
      } else {
        debugPrint('Unsupported data type for icon tracing: ${data.runtimeType}');
      }
      
      debugPrint('=== END CONNECTOR ICON TRACE ===');
    } catch (e) {
      debugPrint('Error tracing connector icons: $e');
    }
  }

  /// Trace connector icons in a list
  static void _traceListData(List data, {String source = 'Unknown'}) {
    debugPrint('Tracing ${data.length} items in list...');
    
    for (int i = 0; i < data.length; i++) {
      final item = data[i];
      if (item is Map<String, dynamic>) {
        debugPrint('--- Item $i ---');
        _traceMapData(item, source: '$source[Item $i]');
      }
    }
  }

  /// Trace connector icons in a map
  static void _traceMapData(Map<String, dynamic> data, {String source = 'Unknown'}) {
    // Check for direct connector icon fields
    _checkForIconFields(data);
    
    // Check for evses structure
    if (data.containsKey('evses')) {
      debugPrint('Found evses structure, checking for connectors...');
      final evses = data['evses'];
      
      if (evses is Map<String, dynamic>) {
        evses.forEach((key, value) {
          if (value is Map<String, dynamic> && value.containsKey('connectors')) {
            final connectors = value['connectors'];
            if (connectors is List) {
              debugPrint('Found ${connectors.length} connectors in EVSE $key');
              _traceListData(connectors, source: 'EVSE $key');
            }
          }
        });
      } else if (evses is List) {
        _traceListData(evses, source: 'EVSEs List');
      }
    }
    
    // Check for connectors array
    if (data.containsKey('connectors')) {
      final connectors = data['connectors'];
      if (connectors is List) {
        debugPrint('Found ${connectors.length} connectors in direct connectors array');
        _traceListData(connectors, source: 'Direct Connectors');
      }
    }
    
    // Check for types array (connector types)
    if (data.containsKey('types')) {
      final types = data['types'];
      if (types is List) {
        debugPrint('Found ${types.length} connector types');
        _traceListData(types, source: 'Connector Types');
      }
    }
  }

  /// Check for icon-related fields in a map
  static void _checkForIconFields(Map<String, dynamic> data) {
    final iconFields = [
      'icon_url',
      'iconUrl',
      'icon',
      'image_url',
      'imageUrl',
      'image',
      'connector_icon',
      'connectorIcon',
      'type_icon',
      'typeIcon',
    ];
    
    for (final field in iconFields) {
      if (data.containsKey(field)) {
        final value = data[field];
        debugPrint('Found icon field "$field": $value');
      }
    }
    
    // Check for connector type information
    if (data.containsKey('type')) {
      debugPrint('Connector type: ${data['type']}');
    }
    
    if (data.containsKey('connector_type')) {
      debugPrint('Connector type: ${data['connector_type']}');
    }
  }
}
