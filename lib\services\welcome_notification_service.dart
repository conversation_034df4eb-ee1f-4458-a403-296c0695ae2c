import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:ecoplug/config/notification_config.dart';
import 'package:ecoplug/services/notification_navigation_service.dart';
import 'package:ecoplug/services/local_notification_manager.dart';

/// Welcome Notification Service for EcoPlug
/// Handles welcome notifications that trigger after successful user login
/// Supports all app states: foreground, background, and terminated
class WelcomeNotificationService {
  static final WelcomeNotificationService _instance =
      WelcomeNotificationService._internal();
  factory WelcomeNotificationService() => _instance;
  WelcomeNotificationService._internal();

  final LocalNotificationManager _notificationManager =
      LocalNotificationManager();
  final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();
  bool _isInitialized = false;

  static const String _lastWelcomeKey = 'last_welcome_notification';
  static const String _welcomeCountKey = 'welcome_notification_count';

  /// Initialize the welcome notification service
  Future<void> initialize() async {
    if (_isInitialized) {
      debugPrint('🎉 Welcome Notification Service already initialized');
      return;
    }

    try {
      debugPrint('🎉 ===== INITIALIZING WELCOME NOTIFICATION SERVICE =====');

      // Initialize centralized notification manager
      await _notificationManager.initialize();

      // Initialize local notifications for direct display
      await _initializeLocalNotifications();

      // Create welcome notification channel
      await _createWelcomeNotificationChannel();

      _isInitialized = true;
      debugPrint('✅ Welcome Notification Service initialized successfully');
    } catch (e) {
      debugPrint('❌ Error initializing Welcome Notification Service: $e');
      debugPrint('❌ Stack trace: ${StackTrace.current}');
      rethrow;
    }
  }

  /// Initialize local notifications for welcome messages
  Future<void> _initializeLocalNotifications() async {
    debugPrint('🎉 Initializing local notifications for welcome messages...');

    // BRANDING: Use EcoPlug launcher icon for consistent branding
    const androidSettings =
        AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission:
          false, // Already requested via main notification service
      requestBadgePermission: false,
      requestSoundPermission: false,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onWelcomeNotificationTapped,
    );

    debugPrint('✅ Welcome notification local notifications initialized');
  }

  /// Create Android notification channel for welcome messages
  Future<void> _createWelcomeNotificationChannel() async {
    final channelConfig = NotificationConfig.getChannelConfig('user_welcome');
    if (channelConfig == null) {
      debugPrint('❌ Welcome channel configuration not found');
      return;
    }

    final channel = channelConfig.toAndroidChannel();

    await _localNotifications
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(channel);

    debugPrint('✅ Welcome notification channel created');
  }

  /// Handle welcome notification tap
  Future<void> _onWelcomeNotificationTapped(
      NotificationResponse response) async {
    debugPrint('🎉 Welcome notification tapped');

    if (response.payload != null) {
      try {
        // Navigate to dashboard when welcome notification is tapped
        final navigationService = NotificationNavigationService();
        await navigationService.storeNavigationIntent(
            'dashboard', response.payload);
        await navigationService.processPendingNavigationIntents();
      } catch (e) {
        debugPrint('❌ Error handling welcome notification tap: $e');
      }
    }
  }

  /// Show welcome notification after successful login
  /// This is a LOCAL notification that works in all app states
  Future<void> showWelcomeNotification({
    String? userName,
    bool isFirstLogin = false,
  }) async {
    try {
      debugPrint('🎉 ===== SHOWING LOCAL WELCOME NOTIFICATION =====');
      debugPrint('🎉 User Name: $userName');
      debugPrint('🎉 Is First Login: $isFirstLogin');
      debugPrint('🎉 Service Initialized: $_isInitialized');
      debugPrint('🎉 Notification Type: LOCAL (flutter_local_notifications)');

      // Ensure service is initialized
      if (!_isInitialized) {
        debugPrint('🎉 Service not initialized, initializing now...');
        await initialize();
      }

      // Verify notifications are enabled
      final notificationsEnabled = await areNotificationsEnabled();
      if (!notificationsEnabled) {
        debugPrint('⚠️ Notifications are not enabled, but proceeding anyway');
      }

      // Check if we should show the welcome notification
      if (!await _shouldShowWelcomeNotification(isFirstLogin)) {
        debugPrint('🎉 Welcome notification skipped based on frequency rules');
        return;
      }

      // Get notification type configuration
      final typeConfig =
          NotificationConfig.getNotificationType('welcome_login');
      if (typeConfig == null) {
        debugPrint('❌ Welcome notification type configuration not found');
        return;
      }

      // Get channel configuration
      final channelConfig =
          NotificationConfig.getChannelConfig(typeConfig.channel);
      if (channelConfig == null) {
        debugPrint('❌ Welcome channel configuration not found');
        return;
      }

      // Customize title and body based on user info
      final title = typeConfig.title;
      final body = userName != null
          ? 'Welcome back, $userName! ${typeConfig.defaultBody}'
          : typeConfig.defaultBody;

      debugPrint('🎉 Notification Title: $title');
      debugPrint('🎉 Notification Body: $body');

      // Create Android notification details with enhanced settings for all app states
      final androidDetails = AndroidNotificationDetails(
        channelConfig.id,
        channelConfig.name,
        channelDescription: channelConfig.description,
        importance: Importance.high, // High importance for immediate display
        priority: Priority.high,
        icon: '@mipmap/launcher_icon', // BRANDING: Actual EcoPlug app logo
        largeIcon: const DrawableResourceAndroidBitmap('@mipmap/launcher_icon'),
        color: channelConfig.ledColor,
        colorized: true,
        playSound: channelConfig.playSound,
        enableVibration: channelConfig.enableVibration,
        ledColor: channelConfig.ledColor,
        ledOnMs: NotificationTiming.ledOnMs,
        ledOffMs: NotificationTiming.ledOffMs,
        autoCancel: true, // Dismissible after user sees it
        category: AndroidNotificationCategory.social,
        visibility: NotificationVisibility.public,
        ticker: 'Welcome to EcoPlug!',
        when: DateTime.now().millisecondsSinceEpoch,
        showWhen: true,
        groupKey: channelConfig.groupId,
        setAsGroupSummary: false,
        groupAlertBehavior: GroupAlertBehavior.all, // Show in all states
        timeoutAfter: 30000, // Auto-dismiss after 30 seconds
        fullScreenIntent: false, // Don't interrupt user
        ongoing: false, // Allow dismissal
        onlyAlertOnce: false, // Always alert
        styleInformation: BigTextStyleInformation(
          isFirstLogin
              ? 'Welcome to EcoPlug! 🎉 Start your eco-friendly charging journey today! 🌱⚡\n\nDiscover nearby charging stations, track your charging sessions, and contribute to a greener future.'
              : 'Welcome back to EcoPlug! 😊⚡ Continue your eco-friendly charging journey! 🌱\n\nCheck out new charging stations, view your charging history, and keep making a difference.',
          htmlFormatBigText: false,
          contentTitle: title,
          htmlFormatContentTitle: false,
          summaryText: 'EcoPlug - Your Green Charging Companion',
          htmlFormatSummaryText: false,
        ),
      );

      // Create iOS notification details with enhanced settings
      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
        subtitle: 'Your Green Charging Companion',
        threadIdentifier: 'welcome_notifications',
        categoryIdentifier: 'WELCOME_CATEGORY',
        interruptionLevel: InterruptionLevel.active,
      );

      // Create payload with user context and timestamp
      final payload =
          'welcome_login_${DateTime.now().millisecondsSinceEpoch}_${userName ?? 'user'}_${isFirstLogin ? 'first' : 'return'}';

      debugPrint(
          '🎉 Showing local notification with ID: ${NotificationIds.welcomeLogin}');

      // Create notification details for direct display
      final notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      // Show the welcome notification directly to ensure it appears in Android notification tray
      await _localNotifications.show(
        NotificationIds.welcomeLogin,
        title,
        body,
        notificationDetails,
        payload: payload,
      );

      debugPrint(
          '🎉 ✅ Welcome notification sent to Android notification tray: $title');
      debugPrint('🎉 📱 Check Android notification tray for: "$title"');

      // Update notification tracking
      await _updateWelcomeNotificationTracking();

      debugPrint('✅ LOCAL welcome notification shown successfully');
      debugPrint('🎉 Title: $title');
      debugPrint('🎉 Body: $body');
      debugPrint('🎉 Payload: $payload');
    } catch (e) {
      debugPrint('❌ Error showing welcome notification: $e');
      debugPrint('❌ Stack trace: ${StackTrace.current}');
      // Don't rethrow - login should succeed even if notification fails
    }
  }

  /// Check if we should show the welcome notification based on frequency rules
  Future<bool> _shouldShowWelcomeNotification(bool isFirstLogin) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Always show for first login
      if (isFirstLogin) {
        debugPrint('🎉 First login detected - showing welcome notification');
        return true;
      }

      // Check last welcome notification time
      final lastWelcome = prefs.getInt(_lastWelcomeKey) ?? 0;
      final now = DateTime.now().millisecondsSinceEpoch;
      final timeSinceLastWelcome = now - lastWelcome;

      // Show welcome notification if:
      // 1. Never shown before, OR
      // 2. Last shown more than 24 hours ago (for returning users)
      const welcomeCooldown = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

      if (lastWelcome == 0 || timeSinceLastWelcome > welcomeCooldown) {
        debugPrint(
            '🎉 Welcome notification allowed - last shown: ${DateTime.fromMillisecondsSinceEpoch(lastWelcome)}');
        return true;
      }

      debugPrint(
          '🎉 Welcome notification skipped - shown recently (${Duration(milliseconds: timeSinceLastWelcome).inHours} hours ago)');
      return false;
    } catch (e) {
      debugPrint('❌ Error checking welcome notification frequency: $e');
      // Default to showing notification on error
      return true;
    }
  }

  /// Update welcome notification tracking
  Future<void> _updateWelcomeNotificationTracking() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final now = DateTime.now().millisecondsSinceEpoch;

      // Update last welcome time
      await prefs.setInt(_lastWelcomeKey, now);

      // Increment welcome count
      final currentCount = prefs.getInt(_welcomeCountKey) ?? 0;
      await prefs.setInt(_welcomeCountKey, currentCount + 1);

      debugPrint(
          '🎉 Welcome notification tracking updated - count: ${currentCount + 1}');
    } catch (e) {
      debugPrint('❌ Error updating welcome notification tracking: $e');
    }
  }

  /// Get welcome notification statistics
  Future<Map<String, dynamic>> getWelcomeStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastWelcome = prefs.getInt(_lastWelcomeKey) ?? 0;
      final welcomeCount = prefs.getInt(_welcomeCountKey) ?? 0;

      return {
        'last_welcome_time': lastWelcome > 0
            ? DateTime.fromMillisecondsSinceEpoch(lastWelcome).toIso8601String()
            : null,
        'welcome_count': welcomeCount,
        'service_initialized': _isInitialized,
      };
    } catch (e) {
      debugPrint('❌ Error getting welcome stats: $e');
      return {'error': e.toString()};
    }
  }

  /// Clear welcome notification (if still showing)
  Future<void> clearWelcomeNotification() async {
    try {
      await _notificationManager
          .cancelNotification(NotificationIds.welcomeLogin);
      debugPrint('🎉 Welcome notification cleared');
    } catch (e) {
      debugPrint('❌ Error clearing welcome notification: $e');
    }
  }

  /// Reset welcome notification tracking (for testing)
  Future<void> resetWelcomeTracking() async {
    if (kDebugMode) {
      try {
        final prefs = await SharedPreferences.getInstance();
        await prefs.remove(_lastWelcomeKey);
        await prefs.remove(_welcomeCountKey);
        debugPrint('🎉 Welcome notification tracking reset');
      } catch (e) {
        debugPrint('❌ Error resetting welcome tracking: $e');
      }
    }
  }

  /// Check if notifications are enabled
  Future<bool> areNotificationsEnabled() async {
    final status = _notificationManager.getStatus();
    return status['permissions_granted'] == true;
  }

  /// Test method to verify welcome notifications appear in Android notification tray
  Future<void> testWelcomeNotification() async {
    try {
      debugPrint('🧪 ===== TESTING WELCOME NOTIFICATION DISPLAY =====');

      // Ensure service is initialized
      if (!_isInitialized) {
        await initialize();
      }

      // Show test welcome notification
      await showWelcomeNotification(
        userName: 'Test User',
        isFirstLogin: false,
      );

      debugPrint(
          '🧪 ✅ Test welcome notification sent to Android notification tray');
      debugPrint(
          '🧪 📱 Check your Android notification tray for "Welcome to EcoPlug! 😊⚡"');
      debugPrint('🧪 🎉 This notification should appear with welcome message');
    } catch (e) {
      debugPrint('🧪 ❌ Error testing welcome notification: $e');
    }
  }

  /// Get service status
  bool get isInitialized => _isInitialized;
}
