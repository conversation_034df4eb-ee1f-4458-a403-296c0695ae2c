import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ecoplug/providers/notification_provider.dart';
import 'package:ecoplug/services/auth_notification_service.dart';

/// Test widget for welcome notification functionality
/// Shows welcome notification status and provides test buttons
class WelcomeNotificationTestWidget extends ConsumerStatefulWidget {
  const WelcomeNotificationTestWidget({super.key});

  @override
  ConsumerState<WelcomeNotificationTestWidget> createState() => _WelcomeNotificationTestWidgetState();
}

class _WelcomeNotificationTestWidgetState extends ConsumerState<WelcomeNotificationTestWidget> {
  final AuthNotificationService _authService = AuthNotificationService();
  Map<String, dynamic>? _welcomeStats;
  Map<String, dynamic>? _authStatus;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadStats();
  }

  Future<void> _loadStats() async {
    setState(() => _isLoading = true);
    try {
      final welcomeStats = await _authService.getWelcomeNotificationStats();
      final authStatus = await _authService.getAuthNotificationStatus();
      
      setState(() {
        _welcomeStats = welcomeStats;
        _authStatus = authStatus;
      });
    } catch (e) {
      debugPrint('Error loading stats: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final notificationNotifier = ref.watch(notificationProvider.notifier);
    final theme = Theme.of(context);

    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.waving_hand,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Welcome Notifications 😊⚡',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: _loadStats,
                  icon: const Icon(Icons.refresh),
                  tooltip: 'Refresh Stats',
                ),
              ],
            ),
            const SizedBox(height: 16),

            if (_isLoading)
              const Center(child: CircularProgressIndicator())
            else ...[
              // Welcome Stats
              if (_welcomeStats != null) ...[
                _buildStatsSection('Welcome Notification Stats', _welcomeStats!, theme),
                const SizedBox(height: 16),
              ],

              // Auth Status
              if (_authStatus != null) ...[
                _buildStatsSection('Auth Notification Status', _authStatus!, theme),
                const SizedBox(height: 16),
              ],

              // Test Buttons
              _buildTestButtons(notificationNotifier, theme),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatsSection(String title, Map<String, dynamic> stats, ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: theme.textTheme.bodyLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: theme.colorScheme.surfaceVariant,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: stats.entries.map((entry) {
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 2),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      flex: 2,
                      child: Text(
                        _formatKey(entry.key),
                        style: theme.textTheme.bodySmall?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      flex: 3,
                      child: Text(
                        _formatValue(entry.value),
                        style: theme.textTheme.bodySmall,
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildTestButtons(NotificationNotifier notifier, ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Test Welcome Notifications',
          style: theme.textTheme.bodyLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            ElevatedButton.icon(
              onPressed: () => _testWelcomeNotification(notifier, isFirstLogin: true),
              icon: const Icon(Icons.star),
              label: const Text('First Login'),
            ),
            ElevatedButton.icon(
              onPressed: () => _testWelcomeNotification(notifier, isFirstLogin: false),
              icon: const Icon(Icons.login),
              label: const Text('Return User'),
            ),
            ElevatedButton.icon(
              onPressed: () => _testWelcomeNotification(notifier, userName: 'John Doe'),
              icon: const Icon(Icons.person),
              label: const Text('With Name'),
            ),
            OutlinedButton.icon(
              onPressed: () => _clearWelcomeNotification(notifier),
              icon: const Icon(Icons.clear),
              label: const Text('Clear'),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            OutlinedButton.icon(
              onPressed: () => _simulateLogin('user123', 'John Doe'),
              icon: const Icon(Icons.login),
              label: const Text('Simulate Login'),
            ),
            OutlinedButton.icon(
              onPressed: () => _simulateLogout('user123'),
              icon: const Icon(Icons.logout),
              label: const Text('Simulate Logout'),
            ),
            OutlinedButton.icon(
              onPressed: () => _resetTracking('user123'),
              icon: const Icon(Icons.restore),
              label: const Text('Reset Tracking'),
            ),
          ],
        ),
      ],
    );
  }

  Future<void> _testWelcomeNotification(
    NotificationNotifier notifier, {
    String? userName,
    bool isFirstLogin = false,
  }) async {
    try {
      await notifier.showWelcomeNotification(
        userName: userName,
        isFirstLogin: isFirstLogin,
      );
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Welcome notification sent! ${isFirstLogin ? '(First Login)' : '(Return User)'}'),
            backgroundColor: Colors.green,
          ),
        );
      }
      
      // Refresh stats after showing notification
      await Future.delayed(const Duration(milliseconds: 500));
      await _loadStats();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _clearWelcomeNotification(NotificationNotifier notifier) async {
    try {
      await notifier.clearWelcomeNotification();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Welcome notification cleared!'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _simulateLogin(String userId, String userName) async {
    try {
      await _authService.onLoginSuccess(
        userId: userId,
        userName: userName,
        userEmail: '<EMAIL>',
      );
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Login simulation completed!'),
            backgroundColor: Colors.blue,
          ),
        );
      }
      
      await _loadStats();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _simulateLogout(String userId) async {
    try {
      await _authService.onLogout(userId: userId);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Logout simulation completed!'),
            backgroundColor: Colors.purple,
          ),
        );
      }
      
      await _loadStats();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _resetTracking(String userId) async {
    try {
      await _authService.resetLoginTracking(userId);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Tracking reset completed!'),
            backgroundColor: Colors.teal,
          ),
        );
      }
      
      await _loadStats();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  String _formatKey(String key) {
    return key
        .split('_')
        .map((word) => word[0].toUpperCase() + word.substring(1))
        .join(' ');
  }

  String _formatValue(dynamic value) {
    if (value == null) return 'null';
    if (value is bool) return value ? 'Yes' : 'No';
    if (value is Map) return 'Object (${value.length} items)';
    if (value is List) return 'Array (${value.length} items)';
    return value.toString();
  }
}
