import 'package:flutter/foundation.dart';

/// Response model for the paginated stations API
class PaginatedStationsResponse {
  String? message;
  bool? success;
  List<PaginatedStation>? data;
  int currentPage = 1;
  int totalPages = 1;

  PaginatedStationsResponse({
    this.message,
    this.success,
    this.data,
    this.currentPage = 1,
    this.totalPages = 1,
  });

  PaginatedStationsResponse.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    success = json['success'];
    currentPage = json['current_page'] ?? json['page'] ?? 1;
    totalPages = json['total_pages'] ?? json['pages'] ?? 1;
    if (json['data'] != null) {
      data = <PaginatedStation>[];

      // Handle case where data is a Map instead of a List
      if (json['data'] is Map) {
        debugPrint('PaginatedStationsResponse: data is a Map, not a List');

        // If data is a Map, we need to extract the stations from it
        final Map<String, dynamic> dataMap =
            json['data'] as Map<String, dynamic>;

        // Check if the Map contains a 'data' field that is a List
        if (dataMap.containsKey('data') && dataMap['data'] is List) {
          debugPrint(
              'PaginatedStationsResponse: found nested data List in Map');
          final List<dynamic> stationsList = dataMap['data'] as List<dynamic>;
          for (var v in stationsList) {
            data!.add(PaginatedStation.fromJson(v));
          }
        } else {
          // If there's no nested 'data' field, try to process each entry in the Map
          debugPrint(
              'PaginatedStationsResponse: processing Map entries as stations');
          dataMap.forEach((key, v) {
            if (v is Map<String, dynamic>) {
              data!.add(PaginatedStation.fromJson(v));
            }
          });
        }
      } else if (json['data'] is List) {
        // Original behavior for when data is a List
        debugPrint('PaginatedStationsResponse: data is a List as expected');
        final List<dynamic> dataList = json['data'] as List<dynamic>;
        for (var v in dataList) {
          data!.add(PaginatedStation.fromJson(v));
        }
      } else {
        debugPrint(
            'PaginatedStationsResponse: data is neither a Map nor a List: ${json['data'].runtimeType}');
      }
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['message'] = message;
    data['success'] = success;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

/// Model for paginated station data
class PaginatedStation {
  double? longitude;
  double? latitude;
  int? stationId;
  String? uid; // Changed to nullable
  String? name;
  String? address;
  String? city;
  dynamic types; // Can be a Map or List
  String? status;
  double? distance;
  double? rating;
  int? reviewCount;
  String? imageUrl;

  PaginatedStation({
    this.longitude,
    this.latitude,
    this.stationId,
    this.uid, // Made nullable
    this.name,
    this.address,
    this.city,
    this.types,
    this.status,
    this.distance,
    this.rating,
    this.reviewCount,
    this.imageUrl,
  });

  PaginatedStation.fromJson(Map<String, dynamic> json) {
    longitude = json['longitude'] is double
        ? json['longitude']
        : double.tryParse(json['longitude'].toString());
    latitude = json['latitude'] is double
        ? json['latitude']
        : double.tryParse(json['latitude'].toString());
    stationId = json['station_id'];

    // CRITICAL: Only extract UID from actual UID fields, never from database IDs
    // This prevents false data where station details show wrong station info
    final extractedUid =
        json['uid']?.toString() ?? json['station_uid']?.toString();

    // DELETED: Default values removed - only real API data
    if (extractedUid == null || extractedUid.isEmpty) {
      final stationName = json['name'] ?? '';
      final stationId = json['station_id'] ?? '';
      debugPrint(
          'INFO: PaginatedStation.fromJson missing UID for station: $stationName (ID: $stationId)');
      debugPrint('Available fields: ${json.keys.join(", ")}');
      uid = ''; // Keep empty for missing UIDs
    } else {
      debugPrint(
          'Extracted UID: $extractedUid for station: ${json["name"] ?? ""}');
      uid = extractedUid; // Accept any non-empty UID
    }

    name = json['name'];
    address = json['address'];
    city = json['city'];
    types = json['types']; // Store as dynamic
    status = json['status'];
    distance = json['distance'] is double
        ? json['distance']
        : double.tryParse(json['distance']?.toString() ?? '0.0');
    rating = json['rating'] is double
        ? json['rating']
        : double.tryParse(json['rating']?.toString() ?? '0.0');
    reviewCount = json['review_count'];
    imageUrl = json['image_url'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['longitude'] = longitude;
    data['latitude'] = latitude;
    data['station_id'] = stationId;
    data['uid'] = uid; // Include uid in toJson
    data['name'] = name;
    data['address'] = address;
    data['city'] = city;
    data['types'] = types;
    data['status'] = status;
    data['distance'] = distance;
    data['rating'] = rating;
    data['review_count'] = reviewCount;
    data['image_url'] = imageUrl;
    return data;
  }

  /// Helper method to get connector types as a list
  /// Handles both array and object formats for the "types" field
  List<ConnectorType> getConnectorTypes() {
    List<ConnectorType> result = [];

    try {
      if (types == null) {
        // If types is null, return an empty list
        debugPrint('Station ${name ?? "Unknown"}: types field is null');
        return result;
      }

      debugPrint(
          'Station ${name ?? "Unknown"}: types field is of type ${types.runtimeType}');

      // Handle different types of 'types' field
      if (types is Map<String, dynamic>) {
        // Handle map format (e.g., {"0": {"name": "CCS2", "icon": "url"}, ...})
        debugPrint(
            'Station ${name ?? "Unknown"}: Processing types as Map<String, dynamic> with keys: ${(types as Map).keys.join(', ')}');

        (types as Map<String, dynamic>).forEach((key, value) {
          if (value is Map) {
            try {
              // Convert to Map<String, dynamic> for type safety
              final Map<String, dynamic> typeData =
                  Map<String, dynamic>.from(value);
              result.add(ConnectorType.fromJson(typeData));
            } catch (e) {
              debugPrint('Error parsing connector type from map value: $e');
            }
          }
        });
      } else if (types is Map) {
        // Handle generic Map format
        debugPrint(
            'Station ${name ?? "Unknown"}: Processing types as generic Map with keys: ${(types as Map).keys.join(', ')}');

        (types as Map).forEach((key, value) {
          if (value is Map) {
            try {
              // Convert to Map<String, dynamic> for type safety
              final Map<String, dynamic> typeData = {};
              value.forEach((k, v) {
                typeData[k.toString()] = v;
              });
              result.add(ConnectorType.fromJson(typeData));
            } catch (e) {
              debugPrint(
                  'Error parsing connector type from generic map value: $e');
            }
          }
        });
      } else if (types is List<Map<String, dynamic>>) {
        // Handle list of maps format with specific type
        debugPrint(
            'Station ${name ?? "Unknown"}: Processing types as List<Map<String, dynamic>> with ${(types as List).length} items');

        for (var item in types as List<Map<String, dynamic>>) {
          try {
            result.add(ConnectorType.fromJson(item));
          } catch (e) {
            debugPrint('Error parsing connector type from typed list item: $e');
          }
        }
      } else if (types is List) {
        // Handle generic list format
        debugPrint(
            'Station ${name ?? "Unknown"}: Processing types as generic List with ${(types as List).length} items');

        for (var item in types as List) {
          try {
            if (item is Map) {
              // Convert to Map<String, dynamic> for type safety
              final Map<String, dynamic> typeData = {};
              item.forEach((k, v) {
                typeData[k.toString()] = v;
              });
              result.add(ConnectorType.fromJson(typeData));
            }
          } catch (e) {
            debugPrint(
                'Error parsing connector type from generic list item: $e');
          }
        }
      } else {
        // Handle unexpected type
        debugPrint(
            'Station ${name ?? "Unknown"}: Unexpected types field type: ${types.runtimeType}');
      }
    } catch (e, stackTrace) {
      debugPrint(
          '❌ CRITICAL ERROR in getConnectorTypes for station ${name ?? "Unknown"}: $e');
      debugPrint('   Stack trace: $stackTrace');
      debugPrint('   Raw types value: $types');
      debugPrint('   Types runtime type: ${types.runtimeType}');
      // Return empty list to prevent app crash
      return result;
    }

    debugPrint(
        'Station ${name ?? "Unknown"}: Extracted ${result.length} connector types');
    return result;
  }
}

/// Model for connector type
class ConnectorType {
  String? name;
  String? icon;
  String? power; // Add power field
  int? maxElectricPower; // Add maxElectricPower field
  int? guns; // Add guns field
  int? availableGuns; // Add available guns field

  ConnectorType({
    this.name,
    this.icon,
    this.power,
    this.maxElectricPower,
    this.guns,
    this.availableGuns,
  });

  ConnectorType.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    icon = json['icon'];
    power = json['power']?.toString();

    // Extract maxElectricPower with robust parsing - ONLY REAL API DATA
    if (json['maxElectricPower'] != null) {
      if (json['maxElectricPower'] is int) {
        maxElectricPower = json['maxElectricPower'];
      } else if (json['maxElectricPower'] is double) {
        maxElectricPower = json[
            'maxElectricPower']; // BULLETPROOF: Store as dynamic, no conversion
      } else if (json['maxElectricPower'] is String) {
        maxElectricPower = int.tryParse(json['maxElectricPower']);
      }
    } else if (json['max_electric_power'] != null) {
      // Try alternative field name
      if (json['max_electric_power'] is int) {
        maxElectricPower = json['max_electric_power'];
      } else if (json['max_electric_power'] is double) {
        maxElectricPower = json[
            'max_electric_power']; // BULLETPROOF: Store as dynamic, no conversion
      } else if (json['max_electric_power'] is String) {
        maxElectricPower = int.tryParse(json['max_electric_power']);
      }
    }
    // CRITICAL: No default values - keep as null when API doesn't provide data

    // Parse guns data, handling different data types
    if (json['guns'] != null) {
      guns = json['guns'] is int
          ? json['guns']
          : int.tryParse(json['guns'].toString());
    }

    // Parse available guns data, handling different data types
    if (json['available_guns'] != null) {
      availableGuns = json['available_guns'] is int
          ? json['available_guns']
          : int.tryParse(json['available_guns'].toString());
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['name'] = name;
    data['icon'] = icon;
    data['power'] = power;
    data['maxElectricPower'] = maxElectricPower;
    data['guns'] = guns;
    data['available_guns'] = availableGuns;
    return data;
  }
}
