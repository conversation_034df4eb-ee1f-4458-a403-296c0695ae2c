import 'package:flutter/material.dart';

class EnergyFlowWidget extends StatelessWidget {
  final AnimationController animationController;
  final String connectorType;
  final String? status; // Add status parameter for color logic

  const EnergyFlowWidget({
    super.key,
    required this.animationController,
    required this.connectorType,
    this.status, // Optional status for automatic color selection
  });

  // Color constants for connector status
  static const Color _chargingLimeGreen = Color(0xFF8cc051); // Lime green for charging connectors
  static const Color _gunConnectedYellow = Color(0xFFFFEB3B); // Yellow for gun connected
  static const Color _unifiedBadgeColor = Color(0xFF1E88E5); // Blue for other statuses

  /// Get connector color based on status with priority logic
  /// Priority: charging (lime green) > gun connected (yellow) > offline (red) > default (blue)
  Color _getConnectorColor(String? status) {
    if (status == null || status.isEmpty) {
      return _unifiedBadgeColor; // Default blue color
    }

    final statusLower = status.toLowerCase().trim();

    // Priority 1: If status is "charging" - use lime green
    if (statusLower == 'charging') {
      return _chargingLimeGreen;
    }

    // Priority 2: If status is "gun connected" - use yellow
    if (statusLower == 'gun connected') {
      return _gunConnectedYellow;
    }

    // Priority 3: If status is "offline" - use red
    if (statusLower == 'offline') {
      return Colors.red;
    }

    // Priority 4: For all other statuses - use blue
    return _unifiedBadgeColor;
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: animationController,
      builder: (context, child) {
        return Container(
          height: 100,
          width: double.infinity,
          decoration: BoxDecoration(
            color: const Color(0xFF102A40),
            borderRadius: BorderRadius.circular(16),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
          child: Stack(
            children: [
              // Connector icon
              Positioned(
                left: 0,
                top: 0,
                bottom: 0,
                child: _buildConnectorIcon(),
              ),

              // Energy flow line
              Positioned(
                left: 60,
                right: 60,
                top: 0,
                bottom: 0,
                child: CustomPaint(
                  painter: EnergyFlowPainter(
                    progress: animationController.value,
                  ),
                  size: const Size(double.infinity, 80),
                ),
              ),

              // Battery/Vehicle icon
              Positioned(
                right: 0,
                top: 0,
                bottom: 0,
                child: _buildVehicleIcon(),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildConnectorIcon() {
    String iconPath = 'assets/images/connector_icons/';
    final connectorColor = _getConnectorColor(status);

    // Determine the correct connector icon based on type
    switch (connectorType.toLowerCase()) {
      case 'ccs2':
      case 'ccs':
      case 'ccs_combo':
        iconPath += 'ccs2.png';
        break;
      case 'chademo':
        iconPath += 'chademo.png';
        break;
      case 'type2':
      case 'type 2':
        iconPath += 'type2.png';
        break;
      case 'gbt':
      case 'gb/t':
        iconPath += 'gbt.png';
        break;
      default:
        iconPath += 'generic.png';
    }

    return Container(
      width: 50,
      height: 50,
      padding: const EdgeInsets.all(5),
      decoration: BoxDecoration(
        color: connectorColor.withValues(alpha: 0.2), // Use status-based color with transparency
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: connectorColor.withValues(alpha: 0.5), // Status-based border
          width: 2,
        ),
      ),
      child: Image.asset(
        iconPath,
        fit: BoxFit.contain,
        color: connectorColor, // Apply status-based color to the icon
      ),
    );
  }

  Widget _buildVehicleIcon() {
    return Container(
      width: 50,
      height: 50,
      padding: const EdgeInsets.all(5),
      decoration: BoxDecoration(
        color: const Color(0xFF1A3A54),
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Icon(
        Icons.electric_car,
        color: Colors.white,
        size: 30,
      ),
    );
  }
}

class EnergyFlowPainter extends CustomPainter {
  final double progress;

  EnergyFlowPainter({required this.progress});

  @override
  void paint(Canvas canvas, Size size) {
    final double height = size.height;
    final double width = size.width;

    // Draw the base line
    final Paint basePaint = Paint()
      ..color = const Color(0xFF143552)
      ..strokeWidth = 8
      ..strokeCap = StrokeCap.round;

    canvas.drawLine(
      Offset(0, height / 2),
      Offset(width, height / 2),
      basePaint,
    );

    // Draw the energy particles
    final int numParticles = 5;
    final double particleSpacing = width / numParticles;

    for (int i = 0; i < numParticles; i++) {
      final particleOffset = (i * particleSpacing) + (progress * width);
      final normalizedOffset = particleOffset % width;

      final Paint particlePaint = Paint()
        ..color = const Color(0xFF00A3FF)
        ..strokeWidth = 12
        ..strokeCap = StrokeCap.round;

      canvas.drawLine(
        Offset(normalizedOffset, height / 2),
        Offset(normalizedOffset + 20, height / 2),
        particlePaint,
      );

      // Draw glow effect
      final Paint glowPaint = Paint()
        ..color = const Color(0x8000A3FF)
        ..strokeWidth = 18
        ..strokeCap = StrokeCap.round
        ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 8);

      canvas.drawLine(
        Offset(normalizedOffset, height / 2),
        Offset(normalizedOffset + 20, height / 2),
        glowPaint,
      );
    }
  }

  @override
  bool shouldRepaint(EnergyFlowPainter oldDelegate) {
    return oldDelegate.progress != progress;
  }
}
