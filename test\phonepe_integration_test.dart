import 'package:flutter_test/flutter_test.dart';
import 'package:ecoplug/services/payment/phonepe_service.dart';

void main() {
  group('PhonePe Integration Tests', () {
    test('PaymentResult factory methods should create correct result types', () {
      // Test success result
      final successResult = PaymentResult.success({'transactionId': 'test123'});
      expect(successResult.type, PaymentResultType.success);
      expect(successResult.message, 'Payment completed successfully');
      expect(successResult.data?['transactionId'], 'test123');

      // Test failed result
      final failedResult = PaymentResult.failed('Payment failed');
      expect(failedResult.type, PaymentResultType.failed);
      expect(failedResult.message, 'Payment failed');

      // Test interrupted result (replaces cancelled)
      final interruptedResult = PaymentResult.interrupted();
      expect(interruptedResult.type, PaymentResultType.interrupted);
      expect(interruptedResult.message, 'Payment interrupted by user');
      expect(interruptedResult.isUserCancelled, true);

      // Test timeout result
      final timeoutResult = PaymentResult.timeout();
      expect(timeoutResult.type, PaymentResultType.timeout);
      expect(timeoutResult.message, 'Payment request timed out. Please try again.');

      // Test network error result
      final networkErrorResult = PaymentResult.networkError();
      expect(networkErrorResult.type, PaymentResultType.networkError);
      expect(networkErrorResult.message, 'Network error occurred. Please check your connection and try again.');

      // Test app crash result
      final appCrashResult = PaymentResult.appCrash();
      expect(appCrashResult.type, PaymentResultType.appCrash);
      expect(appCrashResult.message, 'PhonePe app encountered an error. Please try again.');

      // Test invalid response result
      final invalidResponseResult = PaymentResult.invalidResponse('Invalid data');
      expect(invalidResponseResult.type, PaymentResultType.invalidResponse);
      expect(invalidResponseResult.message, 'Invalid response from payment gateway');

      // Test unknown result
      final unknownResult = PaymentResult.unknown('Unknown error');
      expect(unknownResult.type, PaymentResultType.unknown);
      expect(unknownResult.message, 'Unknown error occurred during payment');
    });

    test('PaymentResultType enum should have all required values', () {
      // Test that all required enum values exist
      expect(PaymentResultType.success, isNotNull);
      expect(PaymentResultType.failed, isNotNull);
      expect(PaymentResultType.interrupted, isNotNull);
      expect(PaymentResultType.timeout, isNotNull);
      expect(PaymentResultType.networkError, isNotNull);
      expect(PaymentResultType.appCrash, isNotNull);
      expect(PaymentResultType.invalidResponse, isNotNull);
      expect(PaymentResultType.unknown, isNotNull);
      expect(PaymentResultType.pending, isNotNull);

      // Verify enum values count (should be 9 total)
      expect(PaymentResultType.values.length, 9);
    });

    test('PhonePe SDK initialization parameters should be validated', () {
      // Test that required parameters are present
      expect('SANDBOX'.isNotEmpty, true);
      expect('TEST_MERCHANT'.isNotEmpty, true);
      expect('TEST_FLOW'.isNotEmpty, true);

      // Test environment values
      expect(['SANDBOX', 'PRODUCTION'].contains('SANDBOX'), true);
      expect(['SANDBOX', 'PRODUCTION'].contains('PRODUCTION'), true);

      // Test boolean parameter
      expect(true is bool, true);
      expect(false is bool, true);
    });

    test('Payment result should handle error codes correctly', () {
      // Test failed result with error code
      final failedWithCode = PaymentResult.failed(
        'Payment declined',
        errorCode: 'INSUFFICIENT_FUNDS',
        data: {'balance': 0},
      );
      
      expect(failedWithCode.type, PaymentResultType.failed);
      expect(failedWithCode.message, 'Payment declined');
      expect(failedWithCode.errorCode, 'INSUFFICIENT_FUNDS');
      expect(failedWithCode.data?['balance'], 0);
    });

    test('Payment result should handle user cancellation flags correctly', () {
      // Test interrupted result (user cancelled)
      final interruptedResult = PaymentResult.interrupted();
      expect(interruptedResult.isUserCancelled, true);

      // Test failed result (not user cancelled)
      final failedResult = PaymentResult.failed('Technical error');
      expect(failedResult.isUserCancelled, false);

      // Test success result (not user cancelled)
      final successResult = PaymentResult.success({});
      expect(successResult.isUserCancelled, false);
    });

    test('PhonePe service should handle callback registration', () {
      // Test that callback registration doesn't throw
      expect(() {
        PhonePeService.registerServerNotificationCallback((result, transactionId) async {
          // Callback implementation for testing
        });
      }, returnsNormally);
    });

    test('Payment result data should be properly structured', () {
      final testData = {
        'transactionId': 'TXN123',
        'amount': 100.0,
        'currency': 'INR',
        'timestamp': DateTime.now().toIso8601String(),
      };

      final result = PaymentResult.success(testData);
      
      expect(result.data?['transactionId'], 'TXN123');
      expect(result.data?['amount'], 100.0);
      expect(result.data?['currency'], 'INR');
      expect(result.data?['timestamp'], isNotNull);
    });

    test('Error handling should provide meaningful messages', () {
      // Test various error scenarios
      final networkError = PaymentResult.networkError();
      expect(networkError.message, contains('Network'));

      final timeoutError = PaymentResult.timeout();
      expect(timeoutError.message, contains('timed out'));

      final appCrashError = PaymentResult.appCrash();
      expect(appCrashError.message, contains('error'));

      final invalidResponseError = PaymentResult.invalidResponse('Malformed JSON');
      expect(invalidResponseError.message, contains('Invalid response'));
      expect(invalidResponseError.data?['details'], 'Malformed JSON');
    });
  });
}
