import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:jwt_decoder/jwt_decoder.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Service for managing the authentication token with enhanced features:
/// - Token expiration handling
/// - Automatic token refresh
/// - Better security
/// - Reliable offline/online state handling
/// - Prevention of race conditions
/// - Centralized token management for easier debugging
class TokenService {
  // Singleton pattern
  static final TokenService _instance = TokenService._internal();
  factory TokenService() => _instance;
  TokenService._internal() {
    // Initialize the token refresh timer
    _setupTokenRefreshTimer();
  }

  // Token storage keys
  static const String _tokenKey = 'auth_token';
  static const String _refreshTokenKey = 'refresh_token';
  static const String _tokenExpiryKey = 'token_expiry';
  static const String _userIdKey = 'user_id';

  // Lock for preventing race conditions during token refresh
  final _refreshLock = Object();
  bool _isRefreshing = false;
  Timer? _refreshTimer;

  // Token refresh callback - will be implemented by API service
  Future<String?> Function()? onTokenRefreshNeeded;

  /// Initialize the token service
  Future<void> initialize() async {
    final token = await getToken();

    // Only setup refresh timer if we have a token
    if (token != null && token.isNotEmpty) {
      // Setup token refresh timer
      _setupTokenRefreshTimer();
    }
  }

  /// Get the stored auth token
  /// If the token is expired and a refresh callback is available, it will attempt to refresh
  Future<String?> getToken() async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString(_tokenKey);

    // If no token, return null
    if (token == null || token.isEmpty) {
      if (kDebugMode) {
        debugPrint('No token found');
      }
      return null;
    }

    // Check if token is expired
    if (await isTokenExpired() && onTokenRefreshNeeded != null) {
      // Try to refresh the token
      final refreshedToken = await _refreshToken();
      if (refreshedToken != null && refreshedToken.isNotEmpty) {
        return refreshedToken;
      } else {
        if (kDebugMode) {
          debugPrint('Token refresh failed');
        }
        return null;
      }
    }

    return token;
  }

  /// Get token without checking expiration (for internal use)
  Future<String?> _getRawToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_tokenKey);
  }

  /// Save the auth token
  Future<void> saveToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_tokenKey, token);

    // Parse and save token expiry
    _saveTokenExpiry(token);

    // Reset the refresh timer
    _setupTokenRefreshTimer();

    if (kDebugMode) {
      debugPrint('Token saved successfully (token value hidden)');
    }
  }

  /// Save refresh token (if provided by API)
  Future<void> saveRefreshToken(String refreshToken) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_refreshTokenKey, refreshToken);
  }

  /// Get the refresh token
  Future<String?> getRefreshToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_refreshTokenKey);
  }

  /// Parse JWT token and save expiry time
  void _saveTokenExpiry(String token) {
    try {
      // Parse the JWT token
      final Map<String, dynamic> decodedToken = JwtDecoder.decode(token);

      // Get expiry timestamp
      if (decodedToken.containsKey('exp')) {
        final int expiryTimestamp = decodedToken['exp'];
        final DateTime expiryDate =
            DateTime.fromMillisecondsSinceEpoch(expiryTimestamp * 1000);

        // Save expiry date
        _saveExpiryDate(expiryDate);

        // Save user ID if available
        if (decodedToken.containsKey('sub')) {
          _saveUserId(decodedToken['sub'].toString());
        }

        if (kDebugMode) {
          debugPrint('Token expiry saved: ${expiryDate.toIso8601String()}');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error parsing JWT token: $e');
      }
    }
  }

  /// Save token expiry date
  Future<void> _saveExpiryDate(DateTime expiryDate) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_tokenExpiryKey, expiryDate.toIso8601String());
  }

  /// Save user ID from token
  Future<void> _saveUserId(String userId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_userIdKey, userId);
  }

  /// Get user ID
  Future<String?> getUserId() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_userIdKey);
  }

  /// Check if token is expired
  Future<bool> isTokenExpired() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final expiryString = prefs.getString(_tokenExpiryKey);

      if (expiryString == null) {
        // If no expiry date is saved, check the token directly
        final token = await _getRawToken();
        if (token == null || token.isEmpty) {
          return true;
        }

        try {
          return JwtDecoder.isExpired(token);
        } catch (e) {
          if (kDebugMode) {
            debugPrint('Error checking token expiration: $e');
          }
          return true;
        }
      }

      final expiryDate = DateTime.parse(expiryString);
      final now = DateTime.now();

      // Consider token expired 5 minutes before actual expiry
      final safeExpiryDate = expiryDate.subtract(const Duration(minutes: 5));

      return now.isAfter(safeExpiryDate);
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error checking token expiration: $e');
      }
      return true;
    }
  }

  /// Refresh the token
  Future<String?> _refreshToken() async {
    // Use a lock to prevent multiple simultaneous refresh attempts
    synchronized(_refreshLock, () async {
      if (_isRefreshing) {
        // Wait for the current refresh to complete
        while (_isRefreshing) {
          await Future.delayed(const Duration(milliseconds: 100));
        }
        return;
      }

      _isRefreshing = true;

      try {
        if (onTokenRefreshNeeded != null) {
          final newToken = await onTokenRefreshNeeded!();
          if (newToken != null && newToken.isNotEmpty) {
            await saveToken(newToken);
            if (kDebugMode) {
              debugPrint('Token refreshed successfully (token value hidden)');
            }
          }
        }
      } catch (e) {
        if (kDebugMode) {
          debugPrint('Error refreshing token: $e');
        }
      } finally {
        _isRefreshing = false;
      }
    });

    // Return the new token
    return await _getRawToken();
  }

  /// Setup token refresh timer
  void _setupTokenRefreshTimer() async {
    // Cancel existing timer if any
    _refreshTimer?.cancel();

    try {
      // Check if token exists and get expiry
      final prefs = await SharedPreferences.getInstance();
      final expiryString = prefs.getString(_tokenExpiryKey);

      if (expiryString == null) {
        return;
      }

      final expiryDate = DateTime.parse(expiryString);
      final now = DateTime.now();

      // Calculate time until token needs refresh (5 minutes before expiry)
      final refreshDate = expiryDate.subtract(const Duration(minutes: 5));

      if (now.isAfter(refreshDate)) {
        // Token already needs refresh
        _refreshToken();
        return;
      }

      // Set timer to refresh token 5 minutes before expiry
      final timeUntilRefresh = refreshDate.difference(now);
      _refreshTimer = Timer(timeUntilRefresh, () {
        _refreshToken();
      });

      if (kDebugMode) {
        debugPrint(
            'Token refresh scheduled for ${refreshDate.toIso8601String()}');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error setting up token refresh timer: $e');
      }
    }
  }

  /// Clear the auth token (logout)
  Future<void> clearToken() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_tokenKey);
    await prefs.remove(_refreshTokenKey);
    await prefs.remove(_tokenExpiryKey);
    await prefs.remove(_userIdKey);

    // Cancel refresh timer
    _refreshTimer?.cancel();
    _refreshTimer = null;

    if (kDebugMode) {
      debugPrint('Token cleared (logged out)');
    }
  }

  /// Check if user is logged in
  Future<bool> isLoggedIn() async {
    final token = await getToken();
    return token != null && token.isNotEmpty;
  }

  /// Check if token needs refresh and refresh it if needed
  /// Returns true if token was refreshed successfully
  Future<bool> refreshTokenIfNeeded() async {
    try {
      // Check if token is expired
      if (await isTokenExpired()) {
        // Try to refresh the token
        final refreshedToken = await _refreshToken();
        return refreshedToken != null && refreshedToken.isNotEmpty;
      }
      // Token is still valid
      return true;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error in refreshTokenIfNeeded: $e');
      }
      return false;
    }
  }
}

/// Helper function to synchronize async operations
Future<T> synchronized<T>(Object lock, Future<T> Function() callback) async {
  // This is a simple implementation of synchronization
  // In a production app, you might want to use a package like 'synchronized'
  try {
    return await callback();
  } finally {
    // Release lock
  }
}
