import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../models/place_suggestion.dart';
import '../services/places_service.dart';

/// Provider for PlacesService
final placesServiceProvider = Provider<PlacesService>((ref) {
  return PlacesService();
});

/// State class for place search
class PlaceSearchState {
  final List<PlaceSuggestion> suggestions;
  final bool isLoading;
  final String? error;
  final String query;

  const PlaceSearchState({
    this.suggestions = const [],
    this.isLoading = false,
    this.error,
    this.query = '',
  });

  PlaceSearchState copyWith({
    List<PlaceSuggestion>? suggestions,
    bool? isLoading,
    String? error,
    String? query,
  }) {
    return PlaceSearchState(
      suggestions: suggestions ?? this.suggestions,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      query: query ?? this.query,
    );
  }
}

/// Notifier for managing place search state
class PlaceSearchNotifier extends StateNotifier<PlaceSearchState> {
  final PlacesService _placesService;
  Timer? _debounceTimer;

  PlaceSearchNotifier(this._placesService) : super(const PlaceSearchState());

  /// Search for places with debouncing
  void searchPlaces(String query) {
    // Cancel previous timer
    _debounceTimer?.cancel();

    // Update query immediately
    state = state.copyWith(query: query, error: null);

    // If query is empty, clear suggestions
    if (query.trim().isEmpty) {
      state = state.copyWith(suggestions: [], isLoading: false);
      return;
    }

    // Set loading state
    state = state.copyWith(isLoading: true);

    // Debounce the search
    _debounceTimer = Timer(const Duration(milliseconds: 500), () async {
      await _performSearch(query);
    });
  }

  /// Perform the actual search
  Future<void> _performSearch(String query) async {
    try {
      final suggestions = await _placesService.searchPlaces(query);

      // Only update if this is still the current query
      if (state.query == query) {
        state = state.copyWith(
          suggestions: suggestions,
          isLoading: false,
          error: null,
        );
      }
    } catch (e) {
      debugPrint('❌ SEARCH ERROR: $e');
      if (state.query == query) {
        state = state.copyWith(
          isLoading: false,
          error: 'Failed to search places: $e',
        );
      }
    }
  }

  /// Get place details for a suggestion
  Future<PlaceSuggestion?> getPlaceDetails(String placeId) async {
    try {
      return await _placesService.getPlaceDetails(placeId);
    } catch (e) {
      debugPrint('❌ PLACE DETAILS ERROR: $e');
      state = state.copyWith(error: 'Failed to get place details: $e');
      return null;
    }
  }

  /// Get current location suggestion
  Future<PlaceSuggestion?> getCurrentLocationSuggestion() async {
    try {
      return await _placesService.getCurrentLocationSuggestion();
    } catch (e) {
      debugPrint('❌ CURRENT LOCATION ERROR: $e');
      state = state.copyWith(error: 'Failed to get current location: $e');
      return null;
    }
  }

  /// Clear search results
  void clearSearch() {
    _debounceTimer?.cancel();
    state = const PlaceSearchState();
  }

  /// Clear search state when navigating away from pages
  void clearSearchOnNavigation() {
    _debounceTimer?.cancel();
    state = const PlaceSearchState(
      suggestions: [],
      isLoading: false,
      error: null,
      query: '',
    );
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    super.dispose();
  }
}

/// Provider for place search state
final placeSearchProvider = StateNotifierProvider<PlaceSearchNotifier, PlaceSearchState>((ref) {
  final placesService = ref.watch(placesServiceProvider);
  return PlaceSearchNotifier(placesService);
});

/// Provider for current location suggestion
final currentLocationProvider = FutureProvider<PlaceSuggestion?>((ref) async {
  final placesService = ref.watch(placesServiceProvider);
  return await placesService.getCurrentLocationSuggestion();
});
