import 'dart:convert';

/// Model for bookmark response from API
class BookmarkResponse {
  final bool success;
  final String message;
  final List<Bookmark> data;

  BookmarkResponse({
    required this.success,
    required this.message,
    required this.data,
  });

  factory BookmarkResponse.fromJson(Map<String, dynamic> json) {
    return BookmarkResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? 'Unknown response',
      data: json['data'] != null
          ? (json['data'] as List).map((item) => Bookmark.fromJson(item)).toList()
          : [],
    );
  }
}

/// Model for a bookmark
class Bookmark {
  final String locationUid;
  final int status;

  Bookmark({
    required this.locationUid,
    required this.status,
  });

  factory Bookmark.fromJson(Map<String, dynamic> json) {
    return Bookmark(
      locationUid: json['location_uid'] ?? '',
      status: json['status'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'location_uid': locationUid,
      'status': status,
    };
  }

  String toJsonString() {
    return jsonEncode(toJson());
  }
}

/// Model for bookmark save request
class BookmarkSaveRequest {
  final String locationUid;
  final int status;

  BookmarkSaveRequest({
    required this.locationUid,
    required this.status,
  });

  Map<String, dynamic> toJson() {
    return {
      'location_uid': locationUid,
      'status': status,
    };
  }

  String toJsonString() {
    return jsonEncode(toJson());
  }
}
