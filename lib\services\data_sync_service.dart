import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:crypto/crypto.dart';

/// Smart data synchronization service for managing data freshness across the app
class DataSyncService {
  static final DataSyncService _instance = DataSyncService._internal();
  factory DataSyncService() => _instance;
  DataSyncService._internal();

  // Data freshness thresholds (in minutes)
  static const int walletDataThreshold = 2; // 2 minutes for wallet data
  static const int profileDataThreshold = 10; // 10 minutes for profile data
  static const int dashboardDataThreshold = 5; // 5 minutes for dashboard data
  static const int stationDataThreshold = 3; // 3 minutes for station data

  // Cache keys
  static const String _walletCacheKey = 'wallet_cache';
  static const String _profileCacheKey = 'profile_cache';
  static const String _dashboardCacheKey = 'dashboard_cache';
  static const String _stationCacheKey = 'station_cache';

  // Timestamp keys
  static const String _walletTimestampKey = 'wallet_timestamp';
  static const String _profileTimestampKey = 'profile_timestamp';
  static const String _dashboardTimestampKey = 'dashboard_timestamp';
  static const String _stationTimestampKey = 'station_timestamp';

  // Checksum keys for data integrity
  static const String _walletChecksumKey = 'wallet_checksum';
  static const String _profileChecksumKey = 'profile_checksum';
  static const String _dashboardChecksumKey = 'dashboard_checksum';
  static const String _stationChecksumKey = 'station_checksum';

  // Active refresh tracking to prevent duplicate calls
  final Set<String> _activeRefreshes = <String>{};
  
  // Debounce timers for refresh requests
  final Map<String, Timer> _debounceTimers = <String, Timer>{};

  /// Check if data is fresh based on timestamp and threshold
  Future<bool> isDataFresh(String dataType) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final timestampKey = _getTimestampKey(dataType);
      final threshold = _getThreshold(dataType);
      
      final lastUpdate = prefs.getInt(timestampKey) ?? 0;
      final now = DateTime.now().millisecondsSinceEpoch;
      final ageInMinutes = (now - lastUpdate) / (1000 * 60);
      
      final isFresh = ageInMinutes < threshold;
      
      debugPrint('📊 Data freshness check for $dataType:');
      debugPrint('📊 - Age: ${ageInMinutes.toStringAsFixed(1)} minutes');
      debugPrint('📊 - Threshold: $threshold minutes');
      debugPrint('📊 - Is fresh: $isFresh');
      
      return isFresh;
    } catch (e) {
      debugPrint('📊 Error checking data freshness for $dataType: $e');
      return false; // Assume stale on error
    }
  }

  /// Get cached data for a specific type
  Future<Map<String, dynamic>?> getCachedData(String dataType) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = _getCacheKey(dataType);
      final cachedDataString = prefs.getString(cacheKey);
      
      if (cachedDataString != null && cachedDataString.isNotEmpty) {
        final cachedData = json.decode(cachedDataString) as Map<String, dynamic>;
        debugPrint('📊 Retrieved cached data for $dataType: ${cachedData.keys.length} keys');
        return cachedData;
      }
      
      debugPrint('📊 No cached data found for $dataType');
      return null;
    } catch (e) {
      debugPrint('📊 Error retrieving cached data for $dataType: $e');
      return null;
    }
  }

  /// Save data to cache with timestamp and checksum
  Future<void> saveDataToCache(String dataType, Map<String, dynamic> data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = _getCacheKey(dataType);
      final timestampKey = _getTimestampKey(dataType);
      final checksumKey = _getChecksumKey(dataType);
      
      // Save data
      await prefs.setString(cacheKey, json.encode(data));
      
      // Save timestamp
      await prefs.setInt(timestampKey, DateTime.now().millisecondsSinceEpoch);
      
      // Calculate and save checksum for data integrity
      final checksum = _calculateChecksum(data);
      await prefs.setString(checksumKey, checksum);
      
      debugPrint('📊 Saved data to cache for $dataType with checksum: $checksum');
    } catch (e) {
      debugPrint('📊 Error saving data to cache for $dataType: $e');
    }
  }

  /// Check if data has changed by comparing checksums
  Future<bool> hasDataChanged(String dataType, Map<String, dynamic> newData) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final checksumKey = _getChecksumKey(dataType);
      final oldChecksum = prefs.getString(checksumKey);
      final newChecksum = _calculateChecksum(newData);
      
      final hasChanged = oldChecksum != newChecksum;
      
      debugPrint('📊 Data change check for $dataType:');
      debugPrint('📊 - Old checksum: $oldChecksum');
      debugPrint('📊 - New checksum: $newChecksum');
      debugPrint('📊 - Has changed: $hasChanged');
      
      return hasChanged;
    } catch (e) {
      debugPrint('📊 Error checking data changes for $dataType: $e');
      return true; // Assume changed on error
    }
  }

  /// Debounced refresh to prevent rapid successive calls
  void requestRefresh(String dataType, VoidCallback refreshCallback, {int debounceMs = 500}) {
    // Cancel existing timer for this data type
    _debounceTimers[dataType]?.cancel();
    
    // Create new debounced timer
    _debounceTimers[dataType] = Timer(Duration(milliseconds: debounceMs), () {
      if (!_activeRefreshes.contains(dataType)) {
        debugPrint('📊 Executing debounced refresh for $dataType');
        refreshCallback();
      } else {
        debugPrint('📊 Skipping refresh for $dataType - already in progress');
      }
    });
    
    debugPrint('📊 Scheduled debounced refresh for $dataType in ${debounceMs}ms');
  }

  /// Mark refresh as active to prevent duplicates
  void markRefreshActive(String dataType) {
    _activeRefreshes.add(dataType);
    debugPrint('📊 Marked refresh as active for $dataType');
  }

  /// Mark refresh as complete
  void markRefreshComplete(String dataType) {
    _activeRefreshes.remove(dataType);
    debugPrint('📊 Marked refresh as complete for $dataType');
  }

  /// Check if refresh is currently active
  bool isRefreshActive(String dataType) {
    return _activeRefreshes.contains(dataType);
  }

  /// Force invalidate cache for a data type
  Future<void> invalidateCache(String dataType) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = _getCacheKey(dataType);
      final timestampKey = _getTimestampKey(dataType);
      final checksumKey = _getChecksumKey(dataType);
      
      await prefs.remove(cacheKey);
      await prefs.remove(timestampKey);
      await prefs.remove(checksumKey);
      
      debugPrint('📊 Invalidated cache for $dataType');
    } catch (e) {
      debugPrint('📊 Error invalidating cache for $dataType: $e');
    }
  }

  /// Clear all cached data
  Future<void> clearAllCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = [
        _walletCacheKey, _profileCacheKey, _dashboardCacheKey, _stationCacheKey,
        _walletTimestampKey, _profileTimestampKey, _dashboardTimestampKey, _stationTimestampKey,
        _walletChecksumKey, _profileChecksumKey, _dashboardChecksumKey, _stationChecksumKey,
      ];
      
      for (final key in keys) {
        await prefs.remove(key);
      }
      
      _activeRefreshes.clear();
      _debounceTimers.values.forEach((timer) => timer.cancel());
      _debounceTimers.clear();
      
      debugPrint('📊 Cleared all cached data');
    } catch (e) {
      debugPrint('📊 Error clearing all cache: $e');
    }
  }

  /// Get cache key for data type
  String _getCacheKey(String dataType) {
    switch (dataType) {
      case 'wallet': return _walletCacheKey;
      case 'profile': return _profileCacheKey;
      case 'dashboard': return _dashboardCacheKey;
      case 'station': return _stationCacheKey;
      default: return '${dataType}_cache';
    }
  }

  /// Get timestamp key for data type
  String _getTimestampKey(String dataType) {
    switch (dataType) {
      case 'wallet': return _walletTimestampKey;
      case 'profile': return _profileTimestampKey;
      case 'dashboard': return _dashboardTimestampKey;
      case 'station': return _stationTimestampKey;
      default: return '${dataType}_timestamp';
    }
  }

  /// Get checksum key for data type
  String _getChecksumKey(String dataType) {
    switch (dataType) {
      case 'wallet': return _walletChecksumKey;
      case 'profile': return _profileChecksumKey;
      case 'dashboard': return _dashboardChecksumKey;
      case 'station': return _stationChecksumKey;
      default: return '${dataType}_checksum';
    }
  }

  /// Get freshness threshold for data type
  int _getThreshold(String dataType) {
    switch (dataType) {
      case 'wallet': return walletDataThreshold;
      case 'profile': return profileDataThreshold;
      case 'dashboard': return dashboardDataThreshold;
      case 'station': return stationDataThreshold;
      default: return 5; // Default 5 minutes
    }
  }

  /// Calculate MD5 checksum for data integrity
  String _calculateChecksum(Map<String, dynamic> data) {
    final jsonString = json.encode(data);
    final bytes = utf8.encode(jsonString);
    final digest = md5.convert(bytes);
    return digest.toString();
  }

  /// Dispose resources
  void dispose() {
    _debounceTimers.values.forEach((timer) => timer.cancel());
    _debounceTimers.clear();
    _activeRefreshes.clear();
  }
}

/// Data types enum for type safety
class DataTypes {
  static const String wallet = 'wallet';
  static const String profile = 'profile';
  static const String dashboard = 'dashboard';
  static const String station = 'station';
}
