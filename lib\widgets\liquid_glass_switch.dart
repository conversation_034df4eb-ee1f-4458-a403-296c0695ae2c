import 'package:flutter/material.dart';
import 'package:liquid_glass_renderer/liquid_glass_renderer.dart';

/// Liquid Glass Toggle Switch Widget
/// A premium toggle switch with liquid glass effect for modern UI design
class LiquidGlassSwitch extends StatefulWidget {
  final bool value;
  final ValueChanged<bool> onChanged;
  final Color activeColor;
  final Color inactiveColor;
  final Color activeTrackColor;
  final Color inactiveTrackColor;
  final double width;
  final double height;

  const LiquidGlassSwitch({
    super.key,
    required this.value,
    required this.onChanged,
    required this.activeColor,
    this.inactiveColor = Colors.grey,
    required this.activeTrackColor,
    this.inactiveTrackColor = const Color(0xFFE4E4E4),
    this.width = 52.0,
    this.height = 28.0,
  });

  @override
  State<LiquidGlassSwitch> createState() => _LiquidGlassSwitchState();
}

class _LiquidGlassSwitchState extends State<LiquidGlassSwitch>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    if (widget.value) {
      _animationController.value = 1.0;
    }
  }

  @override
  void didUpdateWidget(LiquidGlassSwitch oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.value != oldWidget.value) {
      if (widget.value) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return GestureDetector(
      onTap: () => widget.onChanged(!widget.value),
      child: SizedBox(
        width: widget.width,
        height: widget.height,
        child: Stack(
          children: [
            // Track with enhanced liquid glass effect for professional appearance
            LiquidGlass(
              settings: LiquidGlassSettings(
                thickness:
                    widget.value ? 16 : 12, // Dynamic thickness based on state
                glassColor: widget.value
                    ? (isDarkMode
                        ? const Color(
                            0x30FFFFFF) // Enhanced white tint when active
                        : const Color(
                            0x25000000)) // Enhanced dark tint when active
                    : (isDarkMode
                        ? const Color(0x10FFFFFF) // Subtle tint when inactive
                        : const Color(0x10000000)),
                lightIntensity:
                    widget.value ? 2.2 : 1.0, // Dramatic change when active
                ambientStrength:
                    widget.value ? 0.6 : 0.3, // Enhanced ambient when active
                blend: widget.value ? 45 : 30, // Smoother blending when active
                lightAngle: widget.value ? 1.0 : 0.7, // Dynamic light angle
              ),
              shape: LiquidRoundedSuperellipse(
                borderRadius: Radius.circular(widget.height / 2), // Pill shape
              ),
              glassContainsChild: true, // Child rendered within glass
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
                width: widget.width,
                height: widget.height,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(widget.height / 2),
                  // Enhanced transparency and gradient effects
                  color: widget.value
                      ? widget.activeTrackColor
                          .withAlpha(160) // More transparent for glass effect
                      : widget.inactiveTrackColor.withAlpha(140),
                  // Subtle inner shadow for depth
                  boxShadow: [
                    // Inner shadow effect
                    BoxShadow(
                      color: widget.value
                          ? widget.activeTrackColor.withAlpha(80)
                          : Colors.black.withAlpha(20),
                      blurRadius: 4.0,
                      spreadRadius: -2.0,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                // Add subtle gradient overlay for enhanced glass effect
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(widget.height / 2),
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.white.withAlpha(
                            widget.value ? 40 : 20), // Highlight on top
                        Colors.transparent,
                        Colors.black.withAlpha(
                            widget.value ? 20 : 10), // Shadow on bottom
                      ],
                      stops: const [0.0, 0.5, 1.0],
                    ),
                  ),
                ),
              ),
            ),

            // Thumb with enhanced liquid glass effect - clean professional design
            AnimatedBuilder(
              animation: _animation,
              builder: (context, child) {
                final thumbSize = widget.height - 4.0;
                final thumbPosition =
                    _animation.value * (widget.width - thumbSize - 4.0) + 2.0;

                return Positioned(
                  left: thumbPosition,
                  top: 2.0,
                  child: LiquidGlass(
                    settings: LiquidGlassSettings(
                      thickness: widget.value
                          ? 20
                          : 14, // More pronounced thickness when active
                      glassColor: widget.value
                          ? (isDarkMode
                              ? const Color(
                                  0x40FFFFFF) // Enhanced white tint when active
                              : const Color(
                                  0x35000000)) // Enhanced dark tint when active
                          : (isDarkMode
                              ? const Color(
                                  0x15FFFFFF) // Subtle tint when inactive
                              : const Color(0x15000000)),
                      lightIntensity:
                          widget.value ? 2.8 : 1.2, // Dramatic intensity change
                      ambientStrength: widget.value
                          ? 0.7
                          : 0.3, // Enhanced ambient when active
                      blend: widget.value
                          ? 60
                          : 35, // Maximum blending when active
                      lightAngle:
                          widget.value ? 1.2 : 0.8, // Dynamic light angle
                    ),
                    shape: LiquidRoundedSuperellipse(
                      borderRadius:
                          Radius.circular(thumbSize / 2), // Perfect circle
                    ),
                    glassContainsChild: true,
                    child: Container(
                      width: thumbSize,
                      height: thumbSize,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        // Enhanced transparency for glass effect - no solid colors
                        color: widget.value
                            ? widget.activeColor.withAlpha(
                                200) // More transparent for glass effect
                            : widget.inactiveColor.withAlpha(180),
                        // Enhanced shadow system for depth and state indication
                        boxShadow: [
                          // Primary shadow - colored when active
                          BoxShadow(
                            color: widget.value
                                ? widget.activeColor.withAlpha(
                                    120) // Vibrant colored shadow when active
                                : Colors.black.withAlpha(
                                    30), // Subtle shadow when inactive
                            blurRadius: widget.value
                                ? 12.0
                                : 6.0, // Enhanced blur when active
                            spreadRadius: widget.value
                                ? 3.0
                                : 1.0, // Larger spread when active
                            offset: const Offset(0, 3),
                          ),
                          // Secondary inner glow effect when active
                          if (widget.value)
                            BoxShadow(
                              color: widget.activeColor.withAlpha(60),
                              blurRadius: 8.0,
                              spreadRadius: 1.0,
                              offset: const Offset(0, 1),
                            ),
                          // Subtle highlight on top for glass effect
                          BoxShadow(
                            color:
                                Colors.white.withAlpha(widget.value ? 80 : 40),
                            blurRadius: 4.0,
                            spreadRadius: -2.0,
                            offset: const Offset(0, -1),
                          ),
                        ],
                      ),
                      // Clean design - no icons, pure glass transparency effect
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
