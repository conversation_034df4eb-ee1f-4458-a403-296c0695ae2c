import 'dart:convert';

/// Notification History Item Model
/// Represents a single notification in the notification history
/// Supports both FCM and local notifications with comprehensive metadata
class NotificationHistoryItem {
  final String id;
  final String title;
  final String body;
  final DateTime timestamp;
  final NotificationSource source;
  final NotificationType type;
  final bool isRead;
  final Map<String, dynamic>? data;
  final String? imageUrl;
  final String? actionUrl;
  final int? priority;
  final String? channelId;

  const NotificationHistoryItem({
    required this.id,
    required this.title,
    required this.body,
    required this.timestamp,
    required this.source,
    required this.type,
    this.isRead = false,
    this.data,
    this.imageUrl,
    this.actionUrl,
    this.priority,
    this.channelId,
  });

  /// Create from FCM RemoteMessage
  factory NotificationHistoryItem.fromFCM({
    required String messageId,
    required String title,
    required String body,
    required DateTime timestamp,
    Map<String, dynamic>? data,
    String? imageUrl,
  }) {
    return NotificationHistoryItem(
      id: messageId,
      title: title,
      body: body,
      timestamp: timestamp,
      source: NotificationSource.fcm,
      type: _determineTypeFromData(data),
      data: data,
      imageUrl: imageUrl,
      priority: 1,
      channelId: 'fcm_messages',
    );
  }

  /// Create from Local Notification
  factory NotificationHistoryItem.fromLocal({
    required int notificationId,
    required String title,
    required String body,
    required DateTime timestamp,
    required NotificationType type,
    String? channelId,
    Map<String, dynamic>? data,
    String? actionUrl,
    int? priority,
  }) {
    return NotificationHistoryItem(
      id: 'local_${notificationId}_${timestamp.millisecondsSinceEpoch}',
      title: title,
      body: body,
      timestamp: timestamp,
      source: NotificationSource.local,
      type: type,
      channelId: channelId,
      data: data,
      actionUrl: actionUrl,
      priority: priority ?? 0,
    );
  }

  /// Create from JSON
  factory NotificationHistoryItem.fromJson(Map<String, dynamic> json) {
    return NotificationHistoryItem(
      id: json['id'] as String,
      title: json['title'] as String,
      body: json['body'] as String,
      timestamp: DateTime.fromMillisecondsSinceEpoch(json['timestamp'] as int),
      source: NotificationSource.values[json['source'] as int],
      type: NotificationType.values[json['type'] as int],
      isRead: json['isRead'] as bool? ?? false,
      data: json['data'] as Map<String, dynamic>?,
      imageUrl: json['imageUrl'] as String?,
      actionUrl: json['actionUrl'] as String?,
      priority: json['priority'] as int?,
      channelId: json['channelId'] as String?,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'body': body,
      'timestamp': timestamp.millisecondsSinceEpoch,
      'source': source.index,
      'type': type.index,
      'isRead': isRead,
      'data': data,
      'imageUrl': imageUrl,
      'actionUrl': actionUrl,
      'priority': priority,
      'channelId': channelId,
    };
  }

  /// Create a copy with updated fields
  NotificationHistoryItem copyWith({
    String? id,
    String? title,
    String? body,
    DateTime? timestamp,
    NotificationSource? source,
    NotificationType? type,
    bool? isRead,
    Map<String, dynamic>? data,
    String? imageUrl,
    String? actionUrl,
    int? priority,
    String? channelId,
  }) {
    return NotificationHistoryItem(
      id: id ?? this.id,
      title: title ?? this.title,
      body: body ?? this.body,
      timestamp: timestamp ?? this.timestamp,
      source: source ?? this.source,
      type: type ?? this.type,
      isRead: isRead ?? this.isRead,
      data: data ?? this.data,
      imageUrl: imageUrl ?? this.imageUrl,
      actionUrl: actionUrl ?? this.actionUrl,
      priority: priority ?? this.priority,
      channelId: channelId ?? this.channelId,
    );
  }

  /// Mark as read
  NotificationHistoryItem markAsRead() {
    return copyWith(isRead: true);
  }

  /// Get formatted time string
  String get formattedTime {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
    }
  }

  /// Get notification icon based on type
  String get iconPath {
    switch (type) {
      case NotificationType.charging:
        return 'assets/icons/charging.png';
      case NotificationType.payment:
        return 'assets/icons/payment.png';
      case NotificationType.station:
        return 'assets/icons/station.png';
      case NotificationType.welcome:
        return 'assets/icons/welcome.png';
      case NotificationType.offer:
        return 'assets/icons/offer.png';
      case NotificationType.maintenance:
        return 'assets/icons/maintenance.png';
      case NotificationType.general:
      default:
        return 'assets/icons/notification.png';
    }
  }

  /// Determine notification type from FCM data
  static NotificationType _determineTypeFromData(Map<String, dynamic>? data) {
    if (data == null) return NotificationType.general;

    final typeString = data['type'] as String?;
    if (typeString == null) return NotificationType.general;

    switch (typeString.toLowerCase()) {
      case 'charging':
      case 'charging_started':
      case 'charging_progress':
      case 'charging_complete':
        return NotificationType.charging;
      case 'payment':
      case 'payment_success':
      case 'payment_failed':
        return NotificationType.payment;
      case 'station':
      case 'station_available':
      case 'station_alert':
        return NotificationType.station;
      case 'welcome':
      case 'welcome_login':
        return NotificationType.welcome;
      case 'offer':
      case 'promotion':
      case 'promotion_offer':
        return NotificationType.offer;
      case 'maintenance':
      case 'system_update':
        return NotificationType.maintenance;
      default:
        return NotificationType.general;
    }
  }

  @override
  String toString() {
    return 'NotificationHistoryItem(id: $id, title: $title, type: $type, source: $source, isRead: $isRead)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NotificationHistoryItem && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// Notification Source Enum
enum NotificationSource {
  fcm,     // Firebase Cloud Messaging
  local,   // Local notifications
  system,  // System notifications
}

/// Notification Type Enum
enum NotificationType {
  general,
  charging,
  payment,
  station,
  welcome,
  offer,
  maintenance,
}

/// Extension for NotificationSource
extension NotificationSourceExtension on NotificationSource {
  String get displayName {
    switch (this) {
      case NotificationSource.fcm:
        return 'Push Notification';
      case NotificationSource.local:
        return 'App Notification';
      case NotificationSource.system:
        return 'System Notification';
    }
  }
}

/// Extension for NotificationType
extension NotificationTypeExtension on NotificationType {
  String get displayName {
    switch (this) {
      case NotificationType.general:
        return 'General';
      case NotificationType.charging:
        return 'Charging';
      case NotificationType.payment:
        return 'Payment';
      case NotificationType.station:
        return 'Station';
      case NotificationType.welcome:
        return 'Welcome';
      case NotificationType.offer:
        return 'Offer';
      case NotificationType.maintenance:
        return 'Maintenance';
    }
  }

  String get categoryFilter {
    switch (this) {
      case NotificationType.charging:
        return 'charging';
      case NotificationType.payment:
        return 'payment';
      case NotificationType.station:
        return 'station';
      case NotificationType.welcome:
        return 'welcome';
      case NotificationType.offer:
        return 'offer';
      case NotificationType.maintenance:
        return 'maintenance';
      case NotificationType.general:
      default:
        return 'general';
    }
  }
}
