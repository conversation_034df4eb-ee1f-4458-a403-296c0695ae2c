import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart'; // Import Riverpod
// import 'package:provider/provider.dart'; // Remove Provider
import '../providers/map_marker_provider.dart'; // Import the new Riverpod provider

/// Widget for displaying cached marker images
class CachedMarkerImage extends ConsumerWidget {
  // Change to ConsumerWidget
  final String imageUrl;
  final double size;
  final Widget? placeholder;
  final Widget? errorWidget;

  const CachedMarkerImage({
    super.key,
    required this.imageUrl,
    this.size = 5.0,
    this.placeholder,
    this.errorWidget,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Add WidgetRef ref
    // Access the MapMarkerNotifier using ref.read
    final mapMarkerNotifier = ref.read(mapMarkerNotifierProvider.notifier);

    return FutureBuilder<Uint8List?>(
      future:
          mapMarkerNotifier.getMarkerImage(imageUrl), // Use the notifier method
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return placeholder ?? Container(); // DELETED: No default placeholder
        }

        if (snapshot.hasError || snapshot.data == null) {
          return errorWidget ?? Container(); // DELETED: No default error widget
        }

        return Image.memory(
          snapshot.data!,
          width: size,
          height: size,
          fit: BoxFit.contain,
        );
      },
    );
  }

  // DELETED: No default placeholder or error widgets
}
