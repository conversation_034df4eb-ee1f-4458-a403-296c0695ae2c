<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Failed</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: #f5f5f5;
        }
        .container {
            text-align: center;
            padding: 2rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            max-width: 90%;
            width: 100%;
        }
        .error-icon {
            font-size: 4rem;
            color: #f44336;
            margin-bottom: 1rem;
        }
        h1 {
            color: #333;
            margin-bottom: 1rem;
        }
        p {
            color: #666;
            margin-bottom: 1.5rem;
        }
        .btn {
            display: inline-block;
            padding: 0.8rem 1.5rem;
            background-color: #f44336;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-weight: bold;
            transition: background-color 0.3s;
        }
        .btn:hover {
            background-color: #d32f2f;
        }
        .try-again {
            background-color: #2196F3;
            margin-left: 10px;
        }
        .try-again:hover {
            background-color: #1976D2;
        }
        @media (max-width: 480px) {
            .container {
                padding: 1rem;
            }
            h1 {
                font-size: 1.5rem;
            }
            .btn {
                margin-bottom: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="error-icon">✕</div>
        <h1>Payment Failed</h1>
        <p>Sorry, there was an issue processing your payment.</p>
        <p id="errorMessage" style="color: #f44336; font-weight: bold;"></p>
        <div>
            <a href="#" class="btn" onclick="closeWindow()">Return to App</a>
            <a href="#" class="btn try-again" onclick="retryPayment()">Try Again</a>
        </div>
    </div>

    <script>
        // Function to send message back to Flutter app
        function closeWindow() {
            // This will be replaced with the actual Flutter WebView message
            if (window.flutter_inappwebview) {
                window.flutter_inappwebview.callHandler('paymentCallback', {
                    status: 'failed',
                    message: 'Payment was cancelled by user',
                    transactionId: getUrlParameter('txnid'),
                    amount: getUrlParameter('amount'),
                    error: getUrlParameter('error')
                });
            } else if (window.parent && window.parent.flutter_inappwebview) {
                // For iOS
                window.parent.flutter_inappwebview.callHandler('paymentCallback', {
                    status: 'failed',
                    message: 'Payment was cancelled by user',
                    transactionId: getUrlParameter('txnid'),
                    amount: getUrlParameter('amount'),
                    error: getUrlParameter('error')
                });
            } else {
                // Fallback for testing in browser
                console.log('Payment failed', {
                    status: 'failed',
                    transactionId: getUrlParameter('txnid'),
                    amount: getUrlParameter('amount'),
                    error: getUrlParameter('error')
                });
                alert('Payment failed! You can close this window.');
            }
        }

        // Function to retry payment
        function retryPayment() {
            if (window.flutter_inappwebview) {
                window.flutter_inappwebview.callHandler('paymentRetry');
            } else if (window.parent && window.parent.flutter_inappwebview) {
                window.parent.flutter_inappwebview.callHandler('paymentRetry');
            } else {
                console.log('Retry payment');
                closeWindow();
            }
        }

        // Helper function to get URL parameters
        function getUrlParameter(name) {
            name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
            const regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
            const results = regex.exec(location.search);
            return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
        }

        // When page loads
        document.addEventListener('DOMContentLoaded', function() {
            const errorMessage = document.getElementById('errorMessage');
            const error = getUrlParameter('error');
            
            if (error) {
                errorMessage.textContent = `Error: ${error}`;
            } else {
                errorMessage.textContent = 'The payment was not completed successfully.';
            }
            
            // Auto-close after a delay if no interaction
            setTimeout(function() {
                if (window.flutter_inappwebview || (window.parent && window.parent.flutter_inappwebview)) {
                    closeWindow();
                }
            }, 10000);
        });
    </script>
</body>
</html>
