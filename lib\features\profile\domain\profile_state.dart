import '../../../shared/models/user.dart';

/// Profile state class
class ProfileState {
  final bool isLoading;
  final User? user;
  final String? errorMessage;

  const ProfileState({
    this.isLoading = false,
    this.user,
    this.errorMessage,
  });

  // Initial state
  factory ProfileState.initial() => const ProfileState();

  // Loading state
  factory ProfileState.loading() => const ProfileState(isLoading: true);

  // Loaded state
  factory ProfileState.loaded(User user) => ProfileState(
        user: user,
      );

  // Error state
  factory ProfileState.error(String message) => ProfileState(
        errorMessage: message,
      );

  // Copy with method
  ProfileState copyWith({
    bool? isLoading,
    User? user,
    String? errorMessage,
  }) {
    return ProfileState(
      isLoading: isLoading ?? this.isLoading,
      user: user ?? this.user,
      errorMessage: errorMessage,
    );
  }
}
