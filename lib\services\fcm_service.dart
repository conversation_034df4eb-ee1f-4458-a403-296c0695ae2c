import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:ecoplug/services/notification_navigation_service.dart';
import 'package:ecoplug/config/notification_config.dart';
import 'package:ecoplug/utils/notification_utils.dart';
import '../core/api/api_service.dart';

/// Firebase Cloud Messaging Service for EcoPlug
/// Handles FCM token management, message receiving, and integration with local notifications
class FCMService {
  static final FCMService _instance = FCMService._internal();
  factory FCMService() => _instance;
  FCMService._internal();

  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();

  bool _isInitialized = false;
  String? _fcmToken;

  /// Initialize FCM service
  Future<void> initialize() async {
    if (_isInitialized) {
      debugPrint('🔥 FCM Service already initialized');
      return;
    }

    try {
      debugPrint('🔥 ===== INITIALIZING FCM SERVICE =====');

      // Request notification permissions
      await _requestPermissions();

      // Initialize local notifications for FCM
      await _initializeLocalNotifications();

      // Get and store FCM token
      await _getFCMToken();

      // Set up message handlers
      await _setupMessageHandlers();

      // Listen for token refresh
      _firebaseMessaging.onTokenRefresh.listen(_onTokenRefresh);

      _isInitialized = true;
      debugPrint('✅ FCM Service initialized successfully');
    } catch (e) {
      debugPrint('❌ Error initializing FCM Service: $e');
      debugPrint('❌ Stack trace: ${StackTrace.current}');
      rethrow;
    }
  }

  /// Request notification permissions
  Future<void> _requestPermissions() async {
    debugPrint('🔥 Requesting FCM permissions...');

    final settings = await _firebaseMessaging.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );

    debugPrint('🔥 FCM Permission status: ${settings.authorizationStatus}');

    if (settings.authorizationStatus == AuthorizationStatus.authorized) {
      debugPrint('✅ FCM permissions granted');
    } else if (settings.authorizationStatus ==
        AuthorizationStatus.provisional) {
      debugPrint('⚠️ FCM provisional permissions granted');
    } else {
      debugPrint('❌ FCM permissions denied');
    }
  }

  /// Initialize local notifications for FCM messages
  Future<void> _initializeLocalNotifications() async {
    debugPrint('🔥 Initializing local notifications for FCM...');

    // BRANDING: Use actual EcoPlug app launcher icon for consistent branding
    const androidSettings =
        AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: false, // Already requested via FCM
      requestBadgePermission: false,
      requestSoundPermission: false,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onLocalNotificationTapped,
    );

    // Create notification channel for FCM messages
    if (Platform.isAndroid) {
      await _createFCMNotificationChannel();
    }
  }

  /// Create Android notification channel for FCM using shared utility
  Future<void> _createFCMNotificationChannel() async {
    try {
      debugPrint(
          '🔥 Creating FCM notification channels using shared utility...');

      // Create both charging and FCM channels using shared utility
      final chargingSuccess =
          await NotificationUtils.createChargingNotificationChannel(
              _localNotifications);

      final fcmSuccess = await NotificationUtils.createFCMNotificationChannel(
          _localNotifications);

      if (chargingSuccess && fcmSuccess) {
        debugPrint('✅ All FCM notification channels created successfully');
      } else {
        debugPrint('⚠️ Some FCM notification channels failed to create');
        debugPrint(
            '   Charging channel: ${chargingSuccess ? 'Success' : 'Failed'}');
        debugPrint('   FCM channel: ${fcmSuccess ? 'Success' : 'Failed'}');
      }
    } catch (e) {
      debugPrint('❌ Error creating FCM notification channels: $e');
    }
  }

  /// Get FCM token
  Future<String?> _getFCMToken() async {
    try {
      _fcmToken = await _firebaseMessaging.getToken();
      debugPrint('🔥 FCM Token: ${_fcmToken?.substring(0, 20)}...');

      if (_fcmToken != null) {
        await _storeFCMToken(_fcmToken!);
        // TODO: Send token to your backend server
        await _sendTokenToServer(_fcmToken!);
      }

      return _fcmToken;
    } catch (e) {
      debugPrint('❌ Error getting FCM token: $e');
      return null;
    }
  }

  /// Store FCM token locally
  Future<void> _storeFCMToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('fcm_token', token);
    debugPrint('💾 FCM token stored locally');
  }

  /// Send FCM token to backend server
  Future<void> _sendTokenToServer(String token) async {
    try {
      debugPrint('📤 Sending FCM token to server...');

      // Import the API service and config
      final apiService = ApiService();

      // Prepare token data
      final tokenData = {
        'fcm_token': token,
        'platform': 'android', // or 'ios' based on platform
        'app_version': '1.0.0',
        'device_info': {
          'timestamp': DateTime.now().toIso8601String(),
        }
      };

      // Send token to server using the new endpoint
      final response = await apiService.post(
        '/user/notifications/update-token',
        data: tokenData,
      );

      if (response != null && response['success'] == true) {
        debugPrint('✅ FCM token sent to server successfully');
      } else {
        debugPrint('❌ Server returned error for FCM token update');
        debugPrint('❌ Response: $response');
      }
    } catch (e) {
      debugPrint('❌ Error sending FCM token to server: $e');
    }
  }

  /// Handle token refresh
  Future<void> _onTokenRefresh(String token) async {
    debugPrint('🔄 FCM token refreshed: ${token.substring(0, 20)}...');
    _fcmToken = token;
    await _storeFCMToken(token);
    await _sendTokenToServer(token);
  }

  /// Setup message handlers
  Future<void> _setupMessageHandlers() async {
    debugPrint('🔥 Setting up FCM message handlers...');

    // Handle foreground messages
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

    // Handle background messages (when app is in background but not terminated)
    FirebaseMessaging.onMessageOpenedApp.listen(_handleBackgroundMessage);

    // Handle messages when app is launched from terminated state
    final initialMessage = await _firebaseMessaging.getInitialMessage();
    if (initialMessage != null) {
      debugPrint('🔥 App launched from FCM notification');
      _handleBackgroundMessage(initialMessage);
    }

    debugPrint('✅ FCM message handlers setup complete');
  }

  /// Handle foreground messages (app is open and active)
  Future<void> _handleForegroundMessage(RemoteMessage message) async {
    debugPrint('🔥 ===== FCM FOREGROUND MESSAGE RECEIVED =====');
    debugPrint('🔥 Message ID: ${message.messageId}');
    debugPrint('🔥 From: ${message.from}');
    debugPrint('🔥 Title: ${message.notification?.title}');
    debugPrint('🔥 Body: ${message.notification?.body}');
    debugPrint('🔥 Data Payload: ${message.data}');
    debugPrint('🔥 Timestamp: ${DateTime.now().toIso8601String()}');

    // Validate and analyze the message data
    _validateMessageData(message);

    // Check if this is a charging notification
    final isChargingNotification = _isChargingNotification(message);
    debugPrint('🔥 Is Charging Notification: $isChargingNotification');

    if (isChargingNotification) {
      // For charging notifications in foreground, only update existing notification
      // Don't create new notifications to prevent spam
      debugPrint(
          '🔥 Charging notification received in foreground - updating existing notification');
      await _updateExistingChargingNotification(message);
    } else {
      // Show local notification for non-charging messages
      await _showLocalNotification(message);
    }
  }

  /// Handle background messages (app opened from notification)
  Future<void> _handleBackgroundMessage(RemoteMessage message) async {
    debugPrint('🔥 Handling background FCM message: ${message.messageId}');
    debugPrint('🔥 Data: ${message.data}');

    // Navigate based on message data
    await _handleNotificationNavigation(message.data);
  }

  /// Show local notification for FCM message
  Future<void> _showLocalNotification(RemoteMessage message) async {
    try {
      final notification = message.notification;
      if (notification == null) return;

      final channelConfig = NotificationConfig.getChannelConfig('fcm_messages');
      if (channelConfig == null) {
        debugPrint('❌ FCM channel configuration is not sucessfully  found');
        return;
      }

      final androidDetails = AndroidNotificationDetails(
        channelConfig.id,
        channelConfig.name,
        channelDescription: channelConfig.description,
        importance: channelConfig.importance,
        priority: Priority.high,
        icon: '@mipmap/launcher_icon', // BRANDING: Actual EcoPlug app logo
        largeIcon: const DrawableResourceAndroidBitmap('@mipmap/launcher_icon'),
        color: channelConfig.ledColor,
        playSound: channelConfig.playSound,
        enableVibration: channelConfig.enableVibration,
        ledColor: channelConfig.ledColor,
        ledOnMs: NotificationTiming.ledOnMs,
        ledOffMs: NotificationTiming.ledOffMs,
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      final details = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _localNotifications.show(
        NotificationIds.fcmMessage + message.hashCode.abs() % 1000,
        notification.title,
        notification.body,
        details,
        payload: jsonEncode(message.data),
      );

      debugPrint('✅ Local notification shown for FCM message');
    } catch (e) {
      debugPrint('❌ Error showing local notification: $e');
    }
  }

  /// Handle local notification tap
  Future<void> _onLocalNotificationTapped(NotificationResponse response) async {
    debugPrint('🔥 FCM local notification tapped');

    if (response.payload != null) {
      try {
        final data = jsonDecode(response.payload!) as Map<String, dynamic>;
        await _handleNotificationNavigation(data);
      } catch (e) {
        debugPrint('❌ Error parsing notification payload: $e');
      }
    }
  }

  /// Check if FCM message is a charging notification
  bool _isChargingNotification(RemoteMessage message) {
    final data = message.data;
    final title = message.notification?.title ?? '';
    final body = message.notification?.body ?? '';

    return data['type'] == 'charging' ||
        data['category'] == 'charging' ||
        data['action'] == 'charging_update' ||
        title.toLowerCase().contains('charging') ||
        body.toLowerCase().contains('charging') ||
        data.containsKey('soc') ||
        data.containsKey('charge_percentage');
  }

  /// Update existing charging notification instead of creating new ones
  Future<void> _updateExistingChargingNotification(
      RemoteMessage message) async {
    try {
      debugPrint('🔥 Updating existing charging notification');

      // Extract charging data from FCM message
      final data = message.data;
      final notification = message.notification;

      // Extract SOC and other charging data
      final soc = data['soc'] ?? data['charge_percentage'] ?? '0';
      final power = data['power'] ?? data['current_power'] ?? '0.0 kW';
      final energy = data['energy'] ?? data['energy_delivered'] ?? '0.00 kWh';
      final cost = data['cost'] ?? data['current_price'] ?? '₹0.00';
      final co2 = data['co2'] ?? data['co2_saved'] ?? '0.0 kg';
      final timer = data['timer'] ?? data['charging_timer'] ?? '00:00:00';

      // Build comprehensive notification body with all data
      final title = notification?.title ?? 'EcoPlug Charging Update';
      final body =
          _buildChargingNotificationBody(soc, power, energy, cost, co2, timer);

      debugPrint(
          '🔥 Charging data - SOC: $soc, Power: $power, Energy: $energy');
      debugPrint('🔥 Notification body: $body');

      // REMOVED: Local notification details - FCM handles notifications automatically
      debugPrint(
          '🔔 Progress: ${int.tryParse(soc.toString().replaceAll('%', '')) ?? 0}%');
      debugPrint('🔔 FCM notification will be displayed by system');

      // REMOVED: Local notification generation for charging sessions
      // FCM messages are handled by the system notification tray automatically
      debugPrint(
          '✅ FCM charging notification received - handled by system tray');
      debugPrint('🔔 Title: $title');
      debugPrint('🔔 Body: $body');
      debugPrint('🔔 Data: $data');
    } catch (e) {
      debugPrint('❌ Error updating charging notification: $e');
    }
  }

  /// Build comprehensive charging notification body with all data
  String _buildChargingNotificationBody(String soc, String power, String energy,
      String cost, String co2, String timer) {
    final socValue = soc.toString().replaceAll('%', '');
    return 'Battery: $socValue% • Power: $power • Energy: $energy • Cost: $cost • Time: $timer';
  }

  /// Validate and analyze FCM message data for debugging
  void _validateMessageData(RemoteMessage message) {
    debugPrint('🔍 ===== FCM MESSAGE DATA VALIDATION =====');

    final data = message.data;

    // Check for charging data fields
    final chargingFields = {
      'soc': data['soc'],
      'charge_percentage': data['charge_percentage'],
      'power': data['power'],
      'current_power': data['current_power'],
      'energy': data['energy'],
      'energy_delivered': data['energy_delivered'],
      'cost': data['cost'],
      'current_price': data['current_price'],
      'timer': data['timer'],
      'charging_timer': data['charging_timer'],
      'transaction_id': data['transaction_id'],
      'session_id': data['session_id'],
    };

    debugPrint('🔍 Charging Data Fields:');
    chargingFields.forEach((key, value) {
      if (value != null && value.isNotEmpty) {
        debugPrint('🔍   ✅ $key: $value');
      } else {
        debugPrint('🔍   ❌ $key: NOT_FOUND');
      }
    });

    // Check for real vs default values
    final soc = data['soc'] ?? data['charge_percentage'];
    if (soc != null) {
      if (soc == '0' || soc == '30' || soc.isEmpty) {
        debugPrint(
            '⚠️ WARNING: SOC appears to be default/fallback value: $soc');
      } else {
        debugPrint('✅ Real SOC data detected: $soc');
      }
    } else {
      debugPrint('❌ CRITICAL: No SOC data found in FCM message');
    }

    // Check message source
    if (message.from != null) {
      debugPrint('🔍 Message from topic: ${message.from}');
      if (message.from!.contains('Charging_')) {
        debugPrint('✅ Message from charging topic');
      } else {
        debugPrint('⚠️ Message not from charging topic');
      }
    }

    debugPrint('🔍 ===== END VALIDATION =====');
  }

  /// Handle notification navigation
  Future<void> _handleNotificationNavigation(Map<String, dynamic> data) async {
    debugPrint('🔥 Handling FCM notification navigation: $data');

    final navigationService = NotificationNavigationService();

    // Enhanced navigation logic for charging notifications
    final type = data['type'] as String?;
    final action = data['action'] as String?;
    final sessionId = data['session_id'] as String?;
    final stationId = data['station_id'] as String?;
    final fromFcm = data['from_fcm'] as bool? ?? false;

    // Check if this is a charging-related notification
    if (action == 'open_charging_session' ||
        type == 'charging' ||
        type == 'charging_complete' ||
        type == 'charging_started' ||
        type == 'charging_progress' ||
        fromFcm) {
      debugPrint(
          '🔥 Charging notification detected - navigating to charging session');

      // Build comprehensive payload for charging session
      final chargingPayload = {
        'from_notification': true,
        'from_fcm': fromFcm,
        'session_id': sessionId,
        'charge_percentage': data['soc'] ?? data['charge_percentage'] ?? 0.0,
        'is_charging': true,
        'notification_data': data,
      };

      await navigationService.storeNavigationIntent(
          'charging_session', jsonEncode(chargingPayload));
    } else {
      // Handle other notification types
      switch (type) {
        case 'station_available':
          if (stationId != null) {
            await navigationService.storeNavigationIntent(
                'station_details', stationId);
          }
          break;
        case 'wallet_update':
        case 'payment_success':
        case 'low_balance':
          await navigationService.storeNavigationIntent('wallet', null);
          break;
        case 'trip_reminder':
          await navigationService.storeNavigationIntent('trip', null);
          break;
        default:
          await navigationService.storeNavigationIntent('dashboard', null);
      }
    }

    // Process stored navigation intents immediately
    await navigationService.processPendingNavigationIntents();
  }

  /// Get current FCM token
  Future<String?> getToken() async {
    if (_fcmToken != null) return _fcmToken;
    return await _getFCMToken();
  }

  /// Subscribe to topic
  Future<void> subscribeToTopic(String topic) async {
    try {
      await _firebaseMessaging.subscribeToTopic(topic);
      debugPrint('✅ Subscribed to FCM topic: $topic');
    } catch (e) {
      debugPrint('❌ Error subscribing to topic $topic: $e');
    }
  }

  /// Unsubscribe from topic
  Future<void> unsubscribeFromTopic(String topic) async {
    try {
      await _firebaseMessaging.unsubscribeFromTopic(topic);
      debugPrint('✅ Unsubscribed from FCM topic: $topic');
    } catch (e) {
      debugPrint('❌ Error unsubscribing from topic $topic: $e');
    }
  }

  /// Check if FCM is available
  bool get isAvailable => _isInitialized;

  /// Get stored FCM token
  Future<String?> getStoredToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('fcm_token');
  }
}

/// Background message handler (must be top-level function)
@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  debugPrint('🔥 Background FCM message received: ${message.messageId}');
  debugPrint('🔥 Title: ${message.notification?.title}');
  debugPrint('🔥 Body: ${message.notification?.body}');
  debugPrint('🔥 Data: ${message.data}');

  // Handle background message processing here
  // Note: This runs in a separate isolate, so avoid heavy operations
}
