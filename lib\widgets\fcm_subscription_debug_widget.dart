import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/fcm_subscription_service.dart';
import '../services/fcm_service.dart';

/// Debug widget to show FCM subscription status and test functionality
/// Shows subscription status, FCM token, and provides test buttons
class FCMSubscriptionDebugWidget extends StatefulWidget {
  const FCMSubscriptionDebugWidget({super.key});

  @override
  State<FCMSubscriptionDebugWidget> createState() => _FCMSubscriptionDebugWidgetState();
}

class _FCMSubscriptionDebugWidgetState extends State<FCMSubscriptionDebugWidget> {
  final FCMSubscriptionService _subscriptionService = FCMSubscriptionService();
  final FCMService _fcmService = FCMService();
  
  bool _isLoading = false;
  Map<String, dynamic>? _testResults;
  String? _fcmToken;
  bool _isSubscribed = false;
  String? _currentTransactionId;

  @override
  void initState() {
    super.initState();
    _loadSubscriptionStatus();
  }

  /// Load current subscription status
  Future<void> _loadSubscriptionStatus() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Initialize services if needed
      await _subscriptionService.initialize();
      await _fcmService.initialize();

      // Get current status
      _fcmToken = _subscriptionService.fcmToken ?? await _fcmService.getToken();
      _isSubscribed = _subscriptionService.isSubscribed;
      _currentTransactionId = _subscriptionService.currentTransactionId;

      debugPrint('🔍 Debug Widget - FCM Token: ${_fcmToken?.substring(0, 20)}...');
      debugPrint('🔍 Debug Widget - Is Subscribed: $_isSubscribed');
      debugPrint('🔍 Debug Widget - Transaction ID: $_currentTransactionId');
    } catch (e) {
      debugPrint('❌ Error loading subscription status: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// Test subscription functionality
  Future<void> _testSubscription() async {
    setState(() {
      _isLoading = true;
      _testResults = null;
    });

    try {
      debugPrint('🧪 Starting FCM subscription test...');
      final results = await _subscriptionService.testSubscription();
      
      setState(() {
        _testResults = results;
      });

      // Show results in a dialog
      if (mounted) {
        _showTestResultsDialog(results);
      }
    } catch (e) {
      debugPrint('❌ Error testing subscription: $e');
      setState(() {
        _testResults = {
          'error': true,
          'message': e.toString(),
        };
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
      
      // Reload status after test
      await _loadSubscriptionStatus();
    }
  }

  /// Show test results in a dialog
  void _showTestResultsDialog(Map<String, dynamic> results) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('🧪 FCM Subscription Test Results'),
          content: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildResultItem('FCM Token Available', results['fcm_token_available']),
                _buildResultItem('FCM Token', results['fcm_token'] ?? 'N/A'),
                _buildResultItem('Subscription Success', results['subscription_success']),
                _buildResultItem('Unsubscription Success', results['unsubscription_success']),
                _buildResultItem('Test Transaction ID', results['test_transaction_id']),
                _buildResultItem('Timestamp', results['timestamp']),
                if (results['error'] == true) ...[
                  const SizedBox(height: 8),
                  Text(
                    'Error: ${results['message']}',
                    style: const TextStyle(color: Colors.red, fontSize: 12),
                  ),
                ],
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Close'),
            ),
            TextButton(
              onPressed: () {
                // Copy results to clipboard
                final resultsText = results.entries
                    .map((e) => '${e.key}: ${e.value}')
                    .join('\n');
                Clipboard.setData(ClipboardData(text: resultsText));
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Results copied to clipboard')),
                );
              },
              child: const Text('Copy'),
            ),
          ],
        );
      },
    );
  }

  /// Build a result item widget
  Widget _buildResultItem(String label, dynamic value) {
    Color valueColor = Colors.black87;
    if (value is bool) {
      valueColor = value ? Colors.green : Colors.red;
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
            ),
          ),
          Expanded(
            child: Text(
              value.toString(),
              style: TextStyle(color: valueColor, fontSize: 12),
            ),
          ),
        ],
      ),
    );
  }

  /// Copy FCM token to clipboard
  void _copyFCMToken() {
    if (_fcmToken != null) {
      Clipboard.setData(ClipboardData(text: _fcmToken!));
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('FCM Token copied to clipboard')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '🔔 FCM Subscription Debug',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            if (_isLoading) ...[
              const Center(
                child: CircularProgressIndicator(),
              ),
            ] else ...[
              // Subscription Status
              _buildStatusRow('Subscription Status', _isSubscribed ? 'Active' : 'Inactive'),
              _buildStatusRow('Transaction ID', _currentTransactionId ?? 'None'),
              
              // FCM Token
              Row(
                children: [
                  const Text('FCM Token: ', style: TextStyle(fontWeight: FontWeight.bold)),
                  Expanded(
                    child: Text(
                      _fcmToken != null ? '${_fcmToken!.substring(0, 20)}...' : 'Not available',
                      style: const TextStyle(fontSize: 12),
                    ),
                  ),
                  if (_fcmToken != null)
                    IconButton(
                      icon: const Icon(Icons.copy, size: 16),
                      onPressed: _copyFCMToken,
                      tooltip: 'Copy full token',
                    ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // Action Buttons
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _testSubscription,
                      child: const Text('Test Subscription'),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _loadSubscriptionStatus,
                      child: const Text('Refresh Status'),
                    ),
                  ),
                ],
              ),
              
              // Test Results
              if (_testResults != null) ...[
                const SizedBox(height: 16),
                const Text(
                  'Last Test Results:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildResultItem('Subscription', _testResults!['subscription_success']),
                      _buildResultItem('Unsubscription', _testResults!['unsubscription_success']),
                      if (_testResults!['error'] == true)
                        Text(
                          'Error: ${_testResults!['message']}',
                          style: const TextStyle(color: Colors.red, fontSize: 12),
                        ),
                    ],
                  ),
                ),
              ],
            ],
          ],
        ),
      ),
    );
  }

  /// Build a status row
  Widget _buildStatusRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Text('$label: ', style: const TextStyle(fontWeight: FontWeight.bold)),
          Text(value),
        ],
      ),
    );
  }
}
