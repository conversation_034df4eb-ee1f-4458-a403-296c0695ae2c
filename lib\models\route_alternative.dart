import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'route_model.dart';

/// Enum for route optimization types
enum RouteOptimization {
  fastest,
  shortest,
  mostChargingStations,
  balanced,
}

/// Extension for RouteOptimization enum
extension RouteOptimizationExtension on RouteOptimization {
  String get displayName {
    switch (this) {
      case RouteOptimization.fastest:
        return 'Fastest';
      case RouteOptimization.shortest:
        return 'Shortest';
      case RouteOptimization.mostChargingStations:
        return 'Most Charging Stations';
      case RouteOptimization.balanced:
        return 'Balanced';
    }
  }

  String get description {
    switch (this) {
      case RouteOptimization.fastest:
        return 'Optimized for minimum travel time';
      case RouteOptimization.shortest:
        return 'Optimized for minimum distance';
      case RouteOptimization.mostChargingStations:
        return 'Route with maximum charging options';
      case RouteOptimization.balanced:
        return 'Best balance of time, distance, and charging';
    }
  }

  String get icon {
    switch (this) {
      case RouteOptimization.fastest:
        return '⚡';
      case RouteOptimization.shortest:
        return '📏';
      case RouteOptimization.mostChargingStations:
        return '🔋';
      case RouteOptimization.balanced:
        return '⚖️';
    }
  }
}

/// Model for a single route alternative
class RouteAlternative {
  final String id;
  final RouteModel route;
  final RouteOptimization optimization;
  final int chargingStationsCount;
  final double score; // Overall score for ranking
  final Map<String, dynamic> metadata;

  const RouteAlternative({
    required this.id,
    required this.route,
    required this.optimization,
    required this.chargingStationsCount,
    required this.score,
    this.metadata = const {},
  });

  /// Create RouteAlternative from Google Directions API response
  factory RouteAlternative.fromDirectionsResponse(
    Map<String, dynamic> routeData,
    int index, {
    RouteOptimization optimization = RouteOptimization.balanced,
    int chargingStationsCount = 0,
  }) {
    final route = RouteModel.fromSingleRoute(routeData);
    final score = calculateScore(route, optimization, chargingStationsCount);

    return RouteAlternative(
      id: 'route_$index',
      route: route,
      optimization: optimization,
      chargingStationsCount: chargingStationsCount,
      score: score,
      metadata: {
        'index': index,
        'created_at': DateTime.now().toIso8601String(),
      },
    );
  }

  /// Calculate score for route ranking
  static double calculateScore(
    RouteModel route,
    RouteOptimization optimization,
    int chargingStationsCount,
  ) {
    // Extract numeric values from duration and distance
    final durationMinutes = _extractMinutes(route.duration);
    final distanceKm = _extractKilometers(route.distance);

    double score = 0.0;

    switch (optimization) {
      case RouteOptimization.fastest:
        // Lower duration = higher score
        score = 1000.0 / (durationMinutes + 1);
        break;
      case RouteOptimization.shortest:
        // Lower distance = higher score
        score = 1000.0 / (distanceKm + 1);
        break;
      case RouteOptimization.mostChargingStations:
        // More charging stations = higher score
        score = chargingStationsCount * 10.0;
        break;
      case RouteOptimization.balanced:
        // Balanced scoring
        final timeScore = 300.0 / (durationMinutes + 1);
        final distanceScore = 300.0 / (distanceKm + 1);
        final chargingScore = chargingStationsCount * 5.0;
        score = timeScore + distanceScore + chargingScore;
        break;
    }

    return score;
  }

  /// Extract minutes from duration string (e.g., "1 hour 30 mins" -> 90)
  static double _extractMinutes(String duration) {
    try {
      final hourMatch = RegExp(r'(\d+)\s*hour').firstMatch(duration);
      final minMatch = RegExp(r'(\d+)\s*min').firstMatch(duration);

      double minutes = 0.0;
      if (hourMatch != null) {
        minutes += double.parse(hourMatch.group(1)!) * 60;
      }
      if (minMatch != null) {
        minutes += double.parse(minMatch.group(1)!);
      }

      return minutes > 0 ? minutes : 60.0; // Default to 60 minutes if parsing fails
    } catch (e) {
      return 60.0; // Default fallback
    }
  }

  /// Extract kilometers from distance string (e.g., "15.2 km" -> 15.2)
  static double _extractKilometers(String distance) {
    try {
      final kmMatch = RegExp(r'(\d+\.?\d*)\s*km').firstMatch(distance);
      if (kmMatch != null) {
        return double.parse(kmMatch.group(1)!);
      }

      final mMatch = RegExp(r'(\d+)\s*m').firstMatch(distance);
      if (mMatch != null) {
        return double.parse(mMatch.group(1)!) / 1000.0; // Convert meters to km
      }

      return 10.0; // Default fallback
    } catch (e) {
      return 10.0; // Default fallback
    }
  }

  /// Get route summary with optimization info
  String get summaryWithOptimization {
    return '${optimization.icon} ${route.summary} (${optimization.displayName})';
  }

  /// Get detailed route info
  String get detailedInfo {
    return '${route.distance} • ${route.duration} • $chargingStationsCount charging stations';
  }

  @override
  String toString() {
    return 'RouteAlternative(id: $id, optimization: $optimization, score: $score, stations: $chargingStationsCount)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is RouteAlternative && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// Container for multiple route alternatives
class RouteAlternatives {
  final List<RouteAlternative> alternatives;
  final RouteAlternative? selectedRoute;
  final LatLng origin;
  final LatLng destination;
  final DateTime createdAt;

  const RouteAlternatives({
    required this.alternatives,
    this.selectedRoute,
    required this.origin,
    required this.destination,
    required this.createdAt,
  });

  /// Create RouteAlternatives from Google Directions API response with multiple routes
  factory RouteAlternatives.fromDirectionsResponse(
    Map<String, dynamic> response,
    LatLng origin,
    LatLng destination,
  ) {
    final routes = response['routes'] as List;
    if (routes.isEmpty) {
      throw Exception('No routes found');
    }

    final List<RouteAlternative> alternatives = [];

    // Create alternatives for each route
    for (int i = 0; i < routes.length; i++) {
      final routeData = routes[i] as Map<String, dynamic>;

      // Determine optimization type based on route characteristics
      RouteOptimization optimization = RouteOptimization.balanced;
      if (i == 0) {
        optimization = RouteOptimization.fastest; // First route is usually fastest
      } else if (routes.length > 2 && i == 1) {
        optimization = RouteOptimization.shortest; // Second route might be shortest
      }

      final alternative = RouteAlternative.fromDirectionsResponse(
        routeData,
        i,
        optimization: optimization,
      );

      alternatives.add(alternative);
    }

    // Sort by score (highest first)
    alternatives.sort((a, b) => b.score.compareTo(a.score));

    return RouteAlternatives(
      alternatives: alternatives,
      selectedRoute: alternatives.isNotEmpty ? alternatives.first : null,
      origin: origin,
      destination: destination,
      createdAt: DateTime.now(),
    );
  }

  /// Get the best route for a specific optimization
  RouteAlternative? getBestRouteFor(RouteOptimization optimization) {
    return alternatives
        .where((alt) => alt.optimization == optimization)
        .fold<RouteAlternative?>(null, (best, current) {
      if (best == null) return current;
      return current.score > best.score ? current : best;
    });
  }

  /// Get route by ID
  RouteAlternative? getRouteById(String id) {
    try {
      return alternatives.firstWhere((alt) => alt.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Check if we have multiple alternatives
  bool get hasMultipleAlternatives => alternatives.length > 1;

  /// Get the currently selected route or the best one
  RouteAlternative get currentRoute => selectedRoute ?? alternatives.first;

  @override
  String toString() {
    return 'RouteAlternatives(count: ${alternatives.length}, selected: ${selectedRoute?.id})';
  }
}
