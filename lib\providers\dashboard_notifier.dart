import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart' as geo;
import '../models/station.dart';
import '../services/api_bridge.dart';

// Define the state class for the dashboard
class DashboardState {
  // Reference location for fetching nearby stations (initially India center, later user location or marker)
  final LatLng currentReferenceLocation;

  // All station markers for map display
  final List<Map<String, dynamic>> allMapMarkers;

  // Nearest stations to the reference location
  final AsyncValue<List<Station>> nearestStations;

  // Control for the sheet expansion state
  final bool isSheetExpanded;

  // ID of the currently selected marker (for highlighting)
  final String? selectedMarkerId;

  // Store the last camera position to preserve map state between navigation
  final CameraPosition lastCameraPosition;

  // Store the formatted nearest stations data to avoid unnecessary API calls
  final List<Map<String, dynamic>> formattedNearestStations;

  // Flag to track if data has been loaded at least once
  final bool dataLoaded;

  // Cache timestamp for data freshness
  final DateTime? lastDataFetch;

  // Sheet expansion state
  final double sheetPosition;

  // Flag to track if this is the initial app load
  final bool isInitialLoad;

  DashboardState({
    required this.currentReferenceLocation,
    required this.allMapMarkers,
    required this.nearestStations,
    required this.isSheetExpanded,
    this.selectedMarkerId,
    required this.lastCameraPosition,
    required this.formattedNearestStations,
    required this.dataLoaded,
    this.lastDataFetch,
    this.sheetPosition = 0.28, // Default to minimum sheet size
    this.isInitialLoad = true,
  });

  // Create an initial state
  factory DashboardState.initial() {
    return DashboardState(
      currentReferenceLocation: const LatLng(0.0, 0.0),
      allMapMarkers: [],
      nearestStations: const AsyncValue.loading(),
      isSheetExpanded: false,
      selectedMarkerId: null,
      lastCameraPosition: const CameraPosition(
        target: LatLng(0.0, 0.0),
        zoom: 5.0,
      ),
      formattedNearestStations: [],
      dataLoaded: false,
      lastDataFetch: null,
      sheetPosition: 0.28,
      isInitialLoad: true,
    );
  }

  // Helper method to create a copy with some updated values
  DashboardState copyWith({
    LatLng? currentReferenceLocation,
    List<Map<String, dynamic>>? allMapMarkers,
    AsyncValue<List<Station>>? nearestStations,
    bool? isSheetExpanded,
    String? selectedMarkerId,
    CameraPosition? lastCameraPosition,
    List<Map<String, dynamic>>? formattedNearestStations,
    bool? dataLoaded,
    DateTime? lastDataFetch,
    double? sheetPosition,
    bool? isInitialLoad,
  }) {
    return DashboardState(
      currentReferenceLocation:
          currentReferenceLocation ?? this.currentReferenceLocation,
      allMapMarkers: allMapMarkers ?? this.allMapMarkers,
      nearestStations: nearestStations ?? this.nearestStations,
      isSheetExpanded: isSheetExpanded ?? this.isSheetExpanded,
      selectedMarkerId: selectedMarkerId ?? this.selectedMarkerId,
      lastCameraPosition: lastCameraPosition ?? this.lastCameraPosition,
      formattedNearestStations:
          formattedNearestStations ?? this.formattedNearestStations,
      dataLoaded: dataLoaded ?? this.dataLoaded,
      lastDataFetch: lastDataFetch ?? this.lastDataFetch,
      sheetPosition: sheetPosition ?? this.sheetPosition,
      isInitialLoad: isInitialLoad ?? this.isInitialLoad,
    );
  }
}

// Define the dashboard notifier
class DashboardNotifier extends StateNotifier<DashboardState> {
  final ApiBridge _apiBridge;

  DashboardNotifier({required ApiBridge apiBridge})
      : _apiBridge = apiBridge,
        super(DashboardState.initial()) {
    // Initialize when the notifier is created - load all markers immediately
    initialize();
  }

  // Initialize the dashboard state with optimized parallel loading
  Future<void> initialize() async {
    try {
      final stopwatch = Stopwatch()..start();
      debugPrint('🚀 ===== STARTING PARALLEL DASHBOARD INITIALIZATION =====');

      // Set the state to loading
      state = state.copyWith(
        nearestStations: const AsyncValue.loading(),
      );

      // Get the current reference location for nearest stations
      final latitude = state.currentReferenceLocation.latitude;
      final longitude = state.currentReferenceLocation.longitude;

      // PERFORMANCE OPTIMIZATION: Execute all API calls in parallel
      final parallelTasks = [
        // Task 1: Station markers for map display
        () async {
          debugPrint('📍 Starting station markers API call...');
          final markers = await _apiBridge.getApiStationMarkers();
          debugPrint('📍 Station markers loaded: ${markers.length} markers');
          return {'type': 'markers', 'data': markers};
        }(),

        // Task 2: Nearest stations for list display
        () async {
          debugPrint('🗺️ Starting nearest stations API call...');
          final stations = await _apiBridge.getNearbyStations(
            latitude,
            longitude,
            radius: 50.0, // 50km radius
          );
          debugPrint(
              '🗺️ Nearest stations loaded: ${stations.length} stations');
          return {'type': 'stations', 'data': stations};
        }(),
      ];

      // Wait for all API calls to complete in parallel
      final results = await Future.wait(parallelTasks);

      // Process results from parallel API calls
      List<dynamic> stations = [];
      List<dynamic> nearestStations = [];

      for (final result in results) {
        final resultMap = result as Map<String, dynamic>;
        if (resultMap['type'] == 'markers') {
          stations = resultMap['data'] as List<dynamic>;
        } else if (resultMap['type'] == 'stations') {
          nearestStations = resultMap['data'] as List<dynamic>;
        }
      }

      // Process station markers for map display
      final formattedStations = stations.map((marker) {
        return _apiBridge.convertMarkerToMapFormat(marker);
      }).toList();

      // Convert the stations to the format expected by the UI
      final formattedNearestStations = nearestStations.map((station) {
        // Determine status based on the station status
        String status = station.status ?? 'Available';

        // Get connector types as a comma-separated string
        final connectorTypeStr = station.getConnectorTypesString();

        // Calculate distance from the reference location
        double distance = station.distance ?? 0.0;

        return {
          'id': station.id.toString(),
          'name': station.name, // Real name from API - required field
          'latitude': station.latitude,
          'longitude': station.longitude,
          'address': station.address, // Real address from API - required field
          'city': station.city, // Real city from API - null if not provided
          'uid': station.uid, // Real UID from API - required field
          'distance': distance,
          'status': status,
          'availability': status,
          'connectorType': connectorTypeStr,
          'freeGuns': status == 'Available' ? 1 : 0,
          'mapPinUrl': status == 'Available'
              ? 'https://api2.eeil.online/mapicons/ecoplug_available.png'
              : (status == 'In Use'
                  ? 'https://api2.eeil.online/mapicons/ecoplug_charging.png'
                  : 'https://api2.eeil.online/mapicons/ecoplug_unavailable.png'),
          'focusedMapPinUrl':
              'https://api2.eeil.online/mapicons/ecoplug_focus.png',
        };
      }).toList();

      // Update the state with all fetched data at once
      state = state.copyWith(
        allMapMarkers: formattedStations,
        nearestStations: AsyncValue.data(nearestStations.cast<Station>()),
        formattedNearestStations:
            formattedNearestStations.cast<Map<String, dynamic>>(),
        dataLoaded: true,
        lastDataFetch: DateTime.now(),
        isInitialLoad: false,
      );

      stopwatch.stop();
      debugPrint(
          '🚀 ===== DASHBOARD INITIALIZED IN ${stopwatch.elapsedMilliseconds}ms =====');
      debugPrint('📍 Loaded ${formattedStations.length} map markers');
      debugPrint('🗺️ Loaded ${nearestStations.length} nearest stations');
    } catch (e) {
      debugPrint('Error initializing dashboard: $e');
      // Handle error - fallback to empty list but don't crash if
      state = state.copyWith(
        allMapMarkers: [],
        nearestStations: AsyncValue.error(e, StackTrace.current),
      );
    }
  }

  // Update the reference location to user's current location
  Future<void> updateReferenceToUserLocation() async {
    try {
      // Set the state to loading
      state = state.copyWith(
        nearestStations: const AsyncValue.loading(),
      );

      // Check if location services are enabled
      bool serviceEnabled = await geo.Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        throw Exception('Location services are disabled');
      }

      // Check location permission
      geo.LocationPermission permission =
          await geo.Geolocator.checkPermission();
      if (permission == geo.LocationPermission.denied) {
        permission = await geo.Geolocator.requestPermission();
        if (permission == geo.LocationPermission.denied) {
          throw Exception('Location permissions are denied');
        }
      }

      if (permission == geo.LocationPermission.deniedForever) {
        throw Exception('Location permissions are permanently denied');
      }

      // Get the user's current position with proper settings
      final position = await geo.Geolocator.getCurrentPosition(
        locationSettings: const geo.LocationSettings(
          accuracy: geo.LocationAccuracy.high,
          timeLimit: Duration(seconds: 10),
        ),
      );

      // Update the reference location
      state = state.copyWith(
        currentReferenceLocation: LatLng(position.latitude, position.longitude),
        isSheetExpanded: true,
        selectedMarkerId: null, // Clear any selected marker
      );

      // Fetch nearest stations with the new reference location
      await fetchNearestStations();
    } catch (e) {
      debugPrint('Error updating reference to user location: $e');
      state = state.copyWith(
        nearestStations: AsyncValue.error(e, StackTrace.current),
      );
    }
  }

  // Update the reference location to a marker's location
  Future<void> updateReferenceToMarkerLocation(Station station) async {
    try {
      // Update the state with the new reference location and marker ID
      state = state.copyWith(
        currentReferenceLocation: LatLng(station.latitude, station.longitude),
        isSheetExpanded: true,
        selectedMarkerId: station.id,
        nearestStations: const AsyncValue.loading(),
      );

      // Fetch nearest stations with the new reference location
      await fetchNearestStations();
    } catch (e) {
      debugPrint('Error updating reference to marker location: $e');
      state = state.copyWith(
        nearestStations: AsyncValue.error(e, StackTrace.current),
      );
    }
  }

  // Update the sheet expansion state
  void setSheetExpanded(bool expanded) {
    state = state.copyWith(isSheetExpanded: expanded);
  }

  // Fetch the nearest stations based on the current reference location
  Future<void> fetchNearestStations() async {
    try {
      // Set the state to loading
      state = state.copyWith(
        nearestStations: const AsyncValue.loading(),
      );

      // Get the current reference location
      final latitude = state.currentReferenceLocation.latitude;
      final longitude = state.currentReferenceLocation.longitude;

      // Fetch nearest stations using the ApiBridge
      final stations = await _apiBridge.getNearbyStations(
        latitude,
        longitude,
        radius: 50.0, // 50km radius
      );

      // Convert the stations to the format expected by the UI
      final formattedStations = stations.map((station) {
        // Determine status based on the station status
        String status = station.status ?? 'Status unknown';

        // Get connector types as a comma-separated string
        final connectorTypeStr = station.getConnectorTypesString();

        // Calculate distance from the reference location
        double distance = station.distance;

        return {
          'id': station.id.toString(),
          'name': station.name,
          'latitude': station.latitude,
          'longitude': station.longitude,
          'address': station.address,
          'city': station.city, // Real city from API - null if not provided
          'uid': station.uid, // Include the UID from the station
          'distance': distance,
          'status': status,
          'availability': status,
          'connectorType': connectorTypeStr,
          'freeGuns': status == 'Available' ? 1 : 0,
          'mapPinUrl': status == 'Available'
              ? 'https://api2.eeil.online/mapicons/ecoplug_available.png'
              : (status == 'In Use'
                  ? 'https://api2.eeil.online/mapicons/ecoplug_charging.png'
                  : 'https://api2.eeil.online/mapicons/ecoplug_unavailable.png'),
          'focusedMapPinUrl':
              'https://api2.eeil.online/mapicons/ecoplug_focus.png',
        };
      }).toList();

      // Update the state with the fetched stations and formatted stations
      state = state.copyWith(
        nearestStations: AsyncValue.data(stations),
        formattedNearestStations: formattedStations,
        dataLoaded: true,
      );

      debugPrint(
          'Stored ${formattedStations.length} formatted stations in state');
    } catch (e) {
      debugPrint('Error fetching nearest stations: $e');
      state = state.copyWith(
        nearestStations: AsyncValue.error(e, StackTrace.current),
      );
    }
  }

  // Update the camera position to preserve map state between navigation
  void updateCameraPosition(CameraPosition position) {
    state = state.copyWith(
      lastCameraPosition: position,
    );
    debugPrint(
        'Updated camera position in state: ${position.target.latitude}, ${position.target.longitude}, zoom: ${position.zoom}');
  }

  // Update map markers in state
  void updateMapMarkers(List<Map<String, dynamic>> markers) {
    state = state.copyWith(
      allMapMarkers: markers,
      dataLoaded: true,
    );
    debugPrint('Updated map markers in state: ${markers.length} markers');
  }

  // Update nearest stations in state
  void updateNearestStations(List<Map<String, dynamic>> stations) {
    state = state.copyWith(
      formattedNearestStations: stations,
      dataLoaded: true,
      lastDataFetch: DateTime.now(),
    );
    debugPrint(
        'Updated nearest stations in state: ${stations.length} stations');
  }

  // Update sheet position
  void updateSheetPosition(double position) {
    state = state.copyWith(sheetPosition: position);
  }

  // Mark as no longer initial load
  void markAsNotInitialLoad() {
    state = state.copyWith(isInitialLoad: false);
  }

  // Check if data is fresh (less than 5 minutes old)
  bool isDataFresh() {
    if (state.lastDataFetch == null) return false;
    return DateTime.now().difference(state.lastDataFetch!).inMinutes < 5;
  }

  // Force refresh data
  Future<void> forceRefresh() async {
    state = state.copyWith(
      dataLoaded: false,
      lastDataFetch: null,
    );
    await initialize();
  }
}

// Create a provider for the dashboard notifier
final dashboardNotifierProvider =
    StateNotifierProvider<DashboardNotifier, DashboardState>((ref) {
  return DashboardNotifier(apiBridge: ApiBridge());
});
