import 'dart:async';
import 'package:flutter/foundation.dart';
import '../repositories/station_repository.dart';
import '../services/station_data_cache.dart';
import '../services/marker_image_provider.dart';
import '../services/connectivity_service.dart';

/// Service to handle background refreshing of station data
class BackgroundRefreshService {
  // Singleton pattern
  static final BackgroundRefreshService _instance =
      BackgroundRefreshService._internal();
  factory BackgroundRefreshService() => _instance;
  BackgroundRefreshService._internal();

  // Services
  final StationRepository _stationRepository = StationRepository();
  final StationDataCache _stationDataCache = StationDataCache();
  final MarkerImageProvider _markerImageProvider = MarkerImageProvider();
  final ConnectivityService _connectivityService = ConnectivityService();

  // Timer for periodic refresh
  Timer? _refreshTimer;

  // Refresh interval (default: 15 minutes)
  Duration _refreshInterval = const Duration(minutes: 15);

  // Flag to track if refresh is in progress
  bool _isRefreshing = false;

  // Flag to track if service is initialized
  bool _isInitialized = false;

  // Last refresh time
  DateTime? _lastRefreshTime;

  /// Initialize the background refresh service
  void initialize({Duration? refreshInterval}) {
    if (_isInitialized) return;

    if (refreshInterval != null) {
      _refreshInterval = refreshInterval;
    }

    _isInitialized = true;

    // Start the refresh timer
    _startRefreshTimer();
  }

  /// Start the refresh timer
  void _startRefreshTimer() {
    // Cancel any existing timer
    _refreshTimer?.cancel();

    // Create a new timer
    _refreshTimer = Timer.periodic(_refreshInterval, (_) {
      refreshStationData();
    });
  }

  /// Stop the refresh timer
  void stopRefreshTimer() {
    _refreshTimer?.cancel();
    _refreshTimer = null;
  }

  /// Change the refresh interval
  void setRefreshInterval(Duration interval) {
    _refreshInterval = interval;

    // Restart the timer with the new interval
    if (_isInitialized) {
      _startRefreshTimer();
    }
  }

  /// Get the last refresh time
  DateTime? getLastRefreshTime() {
    return _lastRefreshTime;
  }

  /// Check if a refresh is currently in progress
  bool isRefreshing() {
    return _isRefreshing;
  }

  /// Refresh station data in the background
  Future<bool> refreshStationData() async {
    // Skip if already refreshing
    if (_isRefreshing) {
      return false;
    }

    _isRefreshing = true;

    try {
      // Check connectivity first
      final isConnected = await _connectivityService.checkConnectionManually();
      if (!isConnected) {
        _isRefreshing = false;
        return false;
      }

      // Fetch station markers
      debugPrint('Fetching station markers in background...');
      final response = await _stationRepository.getStationMarkers();

      if (response.success &&
          response.data != null &&
          response.data!.isNotEmpty) {
        debugPrint(
            'Successfully fetched ${response.data!.length} station markers');

        // Save to cache
        await _stationDataCache.saveStationMarkers(response.data!);
        debugPrint('Station markers saved to cache');

        // Preload marker images
        for (final marker in response.data!) {
          if (marker.mapPinUrl != null && marker.mapPinUrl!.isNotEmpty) {
            _markerImageProvider.getMarkerImage(marker.mapPinUrl!);
          }
          if (marker.focusedMapPinUrl != null &&
              marker.focusedMapPinUrl!.isNotEmpty) {
            _markerImageProvider.getMarkerImage(marker.focusedMapPinUrl!);
          }
        }

        // Update last refresh time
        _lastRefreshTime = DateTime.now();

        _isRefreshing = false;
        return true;
      } else {
        _isRefreshing = false;
        return false;
      }
    } catch (e) {
      _isRefreshing = false;
      return false;
    }
  }

  /// Force an immediate refresh
  Future<bool> forceRefresh() async {
    // Cancel the current timer
    _refreshTimer?.cancel();

    // Perform the refresh
    final result = await refreshStationData();

    // Restart the timer
    _startRefreshTimer();

    return result;
  }

  /// Dispose the service
  void dispose() {
    stopRefreshTimer();
    _isInitialized = false;
    debugPrint('BackgroundRefreshService disposed');
  }
}
