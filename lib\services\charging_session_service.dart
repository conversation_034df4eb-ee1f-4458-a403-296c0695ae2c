import 'dart:async';
import 'package:flutter/foundation.dart';
import '../core/api/api_service.dart';
import '../core/api/api_config.dart';
import '../models/api_response.dart';

import 'charging_parameters_service.dart'; // 🚨 NEW: Import for transaction ID storage
import 'persistent_charging_service.dart'; // 🚨 NEW: Import for persistent charging

/// Service for managing charging sessions
class ChargingSessionService {
  final ApiService _apiService;

  // Singleton instance
  static final ChargingSessionService _instance =
      ChargingSessionService._internal();
  factory ChargingSessionService() => _instance;

  ChargingSessionService._internal() : _apiService = ApiService();

  /// This method handles ALL possible Map types and eliminates casting errors permanently
  /// ENHANCED: Now handles null data fields gracefully for charging session responses
  Map<String, dynamic> _ultimateMapConverter(dynamic input, String context) {
    // ENHANCED NULL HANDLING: Handle null data fields gracefully
    if (input == null) {
      debugPrint('⚠️ [$context] Null input received - returning empty map');
      debugPrint(
          '⚠️ This is normal for some API responses where data field is null');
      return <String,
          dynamic>{}; // Return empty map instead of throwing exception
    }

    // Already correct type - return as-is
    if (input is Map<String, dynamic>) {
      debugPrint('✅ [$context] Input already Map<String, dynamic>');
      return input;
    }

    // Handle any Map type - convert to Map<String, dynamic>
    if (input is Map) {
      debugPrint(
          '🔄 [$context] Converting ${input.runtimeType} to Map<String, dynamic>');
      final result = <String, dynamic>{};
      input.forEach((key, value) {
        result[key?.toString() ?? 'null'] = value;
      });
      debugPrint('✅ [$context] Successfully converted to Map<String, dynamic>');
      return result;
    }

    // ENHANCED ERROR HANDLING: Provide more context for debugging
    debugPrint('❌ [$context] Input is not a Map type');
    debugPrint('❌ [$context] Input type: ${input.runtimeType}');
    debugPrint('❌ [$context] Input value: $input');
    throw Exception(
        '[$context] Input is not a Map: ${input.runtimeType}, value: $input');
  }

  /// BULLETPROOF AUTHORIZATION REFERENCE EXTRACTION UTILITY
  ///
  /// This centralized method handles all possible authorization reference field variations
  /// with comprehensive null safety, format validation, and error recovery.
  ///
  /// Parameters:
  /// - responseData: The response data map to search in
  /// - context: Context string for debugging purposes
  ///
  /// Returns:
  /// - String: Valid authorization reference if found
  /// - null: If no valid authorization reference is found
  String? _extractAuthorizationReference(
      Map<String, dynamic> responseData, String context) {
    try {
      debugPrint(
          '🔍 ===== [$context] BULLETPROOF AUTHORIZATION REFERENCE EXTRACTION =====');
      debugPrint(
          '🔍 [$context] Response Data Type: ${responseData.runtimeType}');
      debugPrint(
          '🔍 [$context] Response Data Keys: ${responseData.keys.toList()}');
      debugPrint(
          '🔍 [$context] Response Data Size: ${responseData.length} fields');

      // COMPREHENSIVE NULL SAFETY: Check if response data is valid
      if (responseData.isEmpty) {
        debugPrint(
            '❌ [$context] Response data is empty - cannot extract authorization reference');
        return null;
      }

      String? extractedAuthRef;

      // ENHANCED FIELD NAME VARIATIONS: Extended list of possible field names
      final List<String> possibleFieldNames = [
        'authorization_reference', // Standard field name
        'authorizationReference', // CamelCase variant
        'authorization_ref', // Shortened variant
        'auth_reference', // Alternative naming
        'auth_ref', // Short variant
        'authRef', // Compact camelCase
        'reference', // Simple variant
        'session_reference', // Session-specific variant
        'session_ref', // Session short variant
        'auth_token', // Token variant
        'authToken', // CamelCase token
        'authorization_token', // Full token name
        'authorizationToken', // CamelCase full token
        'session_token', // Session token
        'sessionToken', // CamelCase session token
        'charging_reference', // Charging-specific
        'chargingReference', // CamelCase charging
        'ocpp_reference', // OCPP-specific
        'ocppReference', // CamelCase OCPP
        'transaction_reference', // Transaction-specific
        'transactionReference', // CamelCase transaction
      ];

      debugPrint(
          '🔍 [$context] Searching in ${possibleFieldNames.length} possible field names...');

      // PRIMARY SEARCH: Direct field lookup
      for (final fieldName in possibleFieldNames) {
        final fieldValue = responseData[fieldName];
        debugPrint('🔍 [$context] Checking field "$fieldName": $fieldValue');

        if (fieldValue != null) {
          final stringValue = fieldValue.toString().trim();
          if (stringValue.isNotEmpty &&
              stringValue != 'null' &&
              stringValue != 'undefined') {
            extractedAuthRef = stringValue;
            debugPrint(
                '✅ [$context] Found authorization reference in field "$fieldName": "$extractedAuthRef"');
            break;
          } else {
            debugPrint(
                '⚠️ [$context] Field "$fieldName" exists but is empty or invalid: "$stringValue"');
          }
        } else {
          debugPrint('❌ [$context] Field "$fieldName" not found');
        }
      }

      // SECONDARY SEARCH: Nested object lookup
      if (extractedAuthRef == null) {
        debugPrint(
            '🔍 [$context] Primary search failed - checking nested objects...');

        final nestedObjects = [
          'session',
          'data',
          'auth',
          'authorization',
          'response',
          'result',
          'payload',
          'content',
          'info',
          'details'
        ];

        for (final nestedKey in nestedObjects) {
          final nestedObject = responseData[nestedKey];
          if (nestedObject is Map) {
            debugPrint(
                '🔍 [$context] Checking nested object "$nestedKey": $nestedObject');

            for (final fieldName in possibleFieldNames) {
              final fieldValue = nestedObject[fieldName];
              if (fieldValue != null) {
                final stringValue = fieldValue.toString().trim();
                if (stringValue.isNotEmpty &&
                    stringValue != 'null' &&
                    stringValue != 'undefined') {
                  extractedAuthRef = stringValue;
                  debugPrint(
                      '✅ [$context] Found authorization reference in nested field "$nestedKey.$fieldName": "$extractedAuthRef"');
                  break;
                }
              }
            }
            if (extractedAuthRef != null) break;
          }
        }
      }

      // TERTIARY SEARCH: Case-insensitive search for any field containing "auth" or "ref"
      if (extractedAuthRef == null) {
        debugPrint(
            '🔍 [$context] Nested search failed - performing case-insensitive search...');

        for (final entry in responseData.entries) {
          final key = entry.key.toLowerCase();
          if ((key.contains('auth') || key.contains('ref')) &&
              entry.value != null) {
            final stringValue = entry.value.toString().trim();
            if (stringValue.isNotEmpty &&
                stringValue != 'null' &&
                stringValue != 'undefined') {
              // Additional validation: check if it looks like a reference (not just any auth field)
              if (stringValue.length >= 3 &&
                  !stringValue.toLowerCase().contains('password')) {
                extractedAuthRef = stringValue;
                debugPrint(
                    '✅ [$context] Found authorization reference via case-insensitive search in field "${entry.key}": "$extractedAuthRef"');
                break;
              }
            }
          }
        }
      }

      // FORMAT VALIDATION AND CLEANING
      if (extractedAuthRef != null) {
        debugPrint(
            '🔍 [$context] Performing format validation and cleaning...');

        // Remove unwanted characters (quotes, extra whitespace, etc.)
        final originalValue = extractedAuthRef;
        extractedAuthRef =
            extractedAuthRef.replaceAll(RegExp(r'["\s\n\r\t]'), '');

        if (extractedAuthRef != originalValue) {
          debugPrint(
              '🔍 [$context] Cleaned authorization reference: "$originalValue" → "$extractedAuthRef"');
        }

        // Validate minimum length
        if (extractedAuthRef.length < 3) {
          debugPrint(
              '❌ [$context] Authorization reference too short (${extractedAuthRef.length} chars): "$extractedAuthRef"');
          extractedAuthRef = null;
        }

        // Validate against common invalid values
        final invalidValues = [
          'null',
          'undefined',
          'none',
          '',
          'error',
          'invalid',
          'false',
          'true',
          '0',
          '-1',
          'n/a',
          'na',
          'empty'
        ];

        if (extractedAuthRef != null &&
            invalidValues.contains(extractedAuthRef.toLowerCase())) {
          debugPrint(
              '❌ [$context] Authorization reference has invalid value: "$extractedAuthRef"');
          extractedAuthRef = null;
        }

        if (extractedAuthRef != null) {
          debugPrint(
              '✅ [$context] Authorization reference validated and cleaned: "$extractedAuthRef"');
          debugPrint(
              '✅ [$context] Final length: ${extractedAuthRef.length} characters');
        }
      }

      // FINAL RESULT LOGGING
      if (extractedAuthRef != null) {
        debugPrint(
            '🎉 [$context] SUCCESS: Authorization reference extracted: "$extractedAuthRef"');
        return extractedAuthRef;
      } else {
        debugPrint(
            '❌ [$context] FAILURE: No valid authorization reference found');
        debugPrint(
            '❌ [$context] Searched ${possibleFieldNames.length} field names');
        debugPrint(
            '❌ [$context] Available fields: ${responseData.keys.join(', ')}');
        debugPrint('❌ [$context] Response data: $responseData');
        return null;
      }
    } catch (e, stackTrace) {
      debugPrint(
          '❌ [$context] CRITICAL ERROR during authorization reference extraction: $e');
      debugPrint('❌ [$context] Stack trace: $stackTrace');
      debugPrint('❌ [$context] Response data that caused error: $responseData');
      return null;
    }
  }

  /// Start a charging session
  ///
  /// Parameters:
  /// - evseUid: The unique ID of the EVSE (Electric Vehicle Supply Equipment)
  /// - connectorId: The ID of the connector to use
  /// - chargingValue: The amount of units to charge
  /// - instantCharging: Whether to use the entire wallet balance
  /// - promoId: Optional promo ID
  /// - promoCode: Optional promo code
  Future<ApiResponse<Map<String, dynamic>>> startChargingSession({
    required String evseUid,
    required String connectorId,
    required double chargingValue,
    required bool instantCharging,
    String? promoId,
    String? promoCode,
  }) async {
    try {
      // Debug logging for charging session start
      debugPrint(
          '🔍 Starting charging session: EVSE "$evseUid", Value: $chargingValue');

      // Validate EVSE UID before API call
      if (evseUid.isEmpty || evseUid == 'unknown' || evseUid == 'null') {
        debugPrint('❌ Invalid EVSE UID: "$evseUid"');
        return ApiResponse<Map<String, dynamic>>(
          success: false,
          message:
              'Invalid EVSE UID: "$evseUid". Please check the connector configuration.',
        );
      }

      // Ensure EVSE UID is properly formatted for URL path
      final String cleanEvseUid = evseUid.trim();

      // CRITICAL: Prepare request parameters - EVSE UID goes ONLY in URL path, NOT in payload
      final Map<String, dynamic> params = {
        'charge_type': 'units',
        'charging_value': chargingValue,
        'instant_charging': instantCharging ? 1 : 0,
        'connector_id': connectorId,
      };

      // Add promo code and ID if available
      if (promoId != null && promoCode != null) {
        params['promo_id'] = promoId;
        params['promo_code'] = promoCode;
      }

      // Validate payload structure
      if (params.containsKey('evse_uid') || params.containsKey('evseUid')) {
        debugPrint('❌ EVSE UID found in payload - should be in URL path only');
        return ApiResponse<Map<String, dynamic>>(
          success: false,
          message:
              'Invalid payload structure: EVSE UID should not be in payload',
        );
      }

      // Prepare endpoint with EVSE UID in URL path
      final String fullEndpoint =
          '${ApiConfig.chargingSessionStart}/$cleanEvseUid';
      debugPrint('🔍 Endpoint: $fullEndpoint');

      // Check authentication token
      final token = await _apiService.getToken();
      if (token == null || token.isEmpty) {
        debugPrint('❌ No authentication token available');
        return ApiResponse<Map<String, dynamic>>(
          success: false,
          message: 'Authentication required. Please login again.',
        );
      }

      // Make the API call using the exact endpoint format from the provided URL
      final rawResponse = await _apiService.post(
        fullEndpoint,
        data: params,
      );

      // ULTIMATE MAP CONVERSION - PERMANENT FIX
      final Map<String, dynamic> response;
      try {
        response = _ultimateMapConverter(rawResponse, 'startChargingSession');
      } catch (e) {
        return ApiResponse<Map<String, dynamic>>(
          success: false,
          message: 'Response conversion failed: $e',
        );
      }

      // Log response fields for debugging
      debugPrint('  - Success Field: ${response['success']}');
      debugPrint('  - Message Field: ${response['message']}');
      debugPrint('  - Data Field: ${response['data']}');
      debugPrint('  - All Response Keys: ${response.keys.toList()}');

      // Check if the API call was successful
      if (response['success'] == true) {
        // FIXED: Handle the actual API response structure
        // The API returns: {"message": "Transaction start", "id": 19080, "success": true}
        // Transaction ID is at ROOT level, not in a nested 'data' field

        print('🔍 ===== TRANSACTION ID EXTRACTION (FIXED) =====');
        print('🔍 Full Response: $response');
        print('🔍 Response Keys: ${response.keys.toList()}');
        print('🔍 Transaction ID at root level: ${response['id']}');
        print('🔍 Transaction ID Type: ${response['id'].runtimeType}');
        print('🔍 Message: ${response['message']}');
        print('🔍 Success: ${response['success']}');

        // Extract transaction ID from ROOT level (not from data field)
        final dynamic transactionId = response['id'];

        if (transactionId != null) {
          print(
              '✅ Transaction ID successfully extracted from root: $transactionId');

          // Create response data with the transaction ID for consistency
          final Map<String, dynamic> responseData = {
            'id': transactionId,
            'message': response['message'] ?? 'Transaction start',
            'success': response['success'] ?? true,
          };

          print('✅ Response data created: $responseData');

          return ApiResponse<Map<String, dynamic>>(
            success: true,
            message: response['message'] ?? 'Transaction start',
            data: responseData,
          );
        } else {
          print('❌ CRITICAL ERROR: Transaction ID is null at root level');
          return ApiResponse<Map<String, dynamic>>(
            success: false,
            message: 'Transaction ID not found in response',
          );
        }
      } else {
        print('❌ API CALL FAILED: ${response['message']}');
        return ApiResponse<Map<String, dynamic>>(
          success: false,
          message: response['message'] ?? 'Unknown API error',
        );
      }
    } catch (e) {
      return ApiResponse<Map<String, dynamic>>(
        success: false,
        message: e.toString(),
      );
    }
  }

  /// Verify ongoing session - Step 2 of charging flow (onGetCurrentTransaction)
  ///
  /// Parameters:
  /// - transactionId: The ID from the start transaction response
  Future<ApiResponse<Map<String, dynamic>>> verifyOngoingSession({
    required String transactionId,
  }) async {
    try {
      // Comprehensive debug logging for API request
      debugPrint('🔍 ===== VERIFY ONGOING SESSION API REQUEST =====');
      debugPrint('🔍 Endpoint: POST /api/v1/user/sessions/on-going');
      debugPrint('🔍 Transaction ID: "$transactionId"');
      debugPrint('🔍 Transaction ID Type: ${transactionId.runtimeType}');

      // Validate transaction ID before making API call
      if (transactionId.isEmpty) {
        debugPrint('❌ CRITICAL ERROR: Empty transaction ID provided');
        return ApiResponse<Map<String, dynamic>>(
          success: false,
          message: 'Invalid transaction ID',
        );
      }

      // STEP 2 PAYLOAD: Use exact transaction ID from Step 1 response
      // Required format: {"id": 19080} (integer value from Step 1)
      dynamic transactionIdValue;
      try {
        // Parse as integer to match Step 1 response format (19080)
        transactionIdValue = int.parse(transactionId);
        debugPrint('✅ Transaction ID parsed as integer: $transactionIdValue');
      } catch (e) {
        // Fallback to string if parsing fails
        transactionIdValue = transactionId;
        debugPrint('⚠️ Transaction ID kept as string: "$transactionIdValue"');
      }

      // Create Step 2 payload using transaction ID from Step 1
      final Map<String, dynamic> requestPayload = {'id': transactionIdValue};
      debugPrint('✅ Step 2 payload: $requestPayload');
      debugPrint('🔍 Payload format matches: {"id": $transactionIdValue}');
      debugPrint(
          '🔍 Transaction ID value type: ${transactionIdValue.runtimeType}');

      // CRITICAL FIX: Ensure authentication token is included
      debugPrint('🔍 Checking authentication token for step 2...');
      final token = await _apiService.getToken();
      if (token == null || token.isEmpty) {
        debugPrint(
            '❌ CRITICAL ERROR: No authentication token available for step 2');
        return ApiResponse<Map<String, dynamic>>(
          success: false,
          message: 'Authentication required for verify ongoing session',
        );
      }
      debugPrint('✅ Authentication token available for step 2');

      // Make the API call using the exact endpoint format from the documentation
      final rawResponse = await _apiService.post(
        '/user/sessions/on-going',
        data: requestPayload,
      );

      // Comprehensive debug logging for API response
      debugPrint('🔍 ===== VERIFY ONGOING SESSION API RESPONSE =====');
      debugPrint('🔍 Response Type: ${rawResponse.runtimeType}');
      debugPrint('🔍 Response Content: $rawResponse');

      // ULTIMATE MAP CONVERSION - PERMANENT FIX
      final Map<String, dynamic> response;
      try {
        response = _ultimateMapConverter(rawResponse, 'verifyOngoingSession');
      } catch (e) {
        return ApiResponse<Map<String, dynamic>>(
          success: false,
          message: 'Response conversion failed: $e',
        );
      }

      // Log all response fields for debugging
      debugPrint('🔍 Response Keys: ${response.keys.toList()}');
      debugPrint('🔍 Success Field: ${response['success']}');
      debugPrint('🔍 Message Field: ${response['message']}');
      debugPrint('🔍 Data Field: ${response['data']}');

      // Check if the API call was successful
      if (response['success'] == true) {
        // ULTIMATE DATA CONVERSION - PERMANENT FIX
        final Map<String, dynamic> responseData;
        try {
          responseData = _ultimateMapConverter(
              response['data'], 'verifyOngoingSession-data');
        } catch (e) {
          return ApiResponse<Map<String, dynamic>>(
            success: false,
            message: 'Data conversion failed: $e',
          );
        }

        // ENHANCED: BULLETPROOF AUTHORIZATION REFERENCE EXTRACTION
        debugPrint(
            '🔍 ===== ENHANCED AUTHORIZATION REFERENCE EXTRACTION =====');

        // Extract authorization reference using the centralized utility
        final String? extractedAuthRef = _extractAuthorizationReference(
            responseData, 'verifyOngoingSession-Step2');

        // Enhanced validation and logging
        if (extractedAuthRef != null) {
          debugPrint(
              '✅ Authorization reference successfully extracted: "$extractedAuthRef"');
          debugPrint('✅ Length: ${extractedAuthRef.length} characters');
          debugPrint('✅ Ready for Step 3 integration');

          // Add the extracted authorization reference to response data for consistency
          responseData['authorization_reference'] = extractedAuthRef;
        } else {
          debugPrint('❌ CRITICAL: Authorization reference extraction failed');
          debugPrint(
              '❌ Available response fields: ${responseData.keys.toList()}');
          debugPrint('❌ Full response data: $responseData');

          return ApiResponse<Map<String, dynamic>>(
            success: false,
            message:
                'Authorization reference not found in Step 2 response. Available fields: ${responseData.keys.join(', ')}',
          );
        }

        return ApiResponse<Map<String, dynamic>>(
          success: true,
          message: response['message'] ?? '',
          data: responseData,
        );
      } else {
        debugPrint('❌ API ERROR: ${response['message']}');
        return ApiResponse<Map<String, dynamic>>(
          success: false,
          message: response['message'] ?? 'Unknown API error',
        );
      }
    } catch (e) {
      debugPrint('❌ EXCEPTION in verifyOngoingSession: $e');
      debugPrint('❌ Exception Type: ${e.runtimeType}');
      return ApiResponse<Map<String, dynamic>>(
        success: false,
        message: e.toString(),
      );
    }
  }

  /// Get real-time charging data - Step 3 of charging flow (GetCharginDataViaApi)
  ///
  /// Parameters:
  /// - authorizationReference: The reference from the ongoing session response
  Future<ApiResponse<Map<String, dynamic>>> getChargingData({
    required String authorizationReference,
  }) async {
    try {
      // Comprehensive debug logging for API request
      debugPrint('🔍 ===== REAL-TIME DATA POLLING API REQUEST =====');
      debugPrint('🔍 Endpoint: GET /api/v1/user/sessions/on-going-data');
      debugPrint('🔍 Authorization Reference: "$authorizationReference"');
      debugPrint(
          '🔍 Query Parameters: authorization_reference=$authorizationReference');
      debugPrint(
          '🔍 Full URL: https://api2.eeil.online/api/v1/user/sessions/on-going-data?authorization_reference=$authorizationReference');

      // ENHANCED AUTHORIZATION REFERENCE VALIDATION
      if (authorizationReference.isEmpty) {
        debugPrint('❌ CRITICAL ERROR: Empty authorization reference provided');
        return ApiResponse<Map<String, dynamic>>(
          success: false,
          message: 'Invalid authorization reference: empty value',
        );
      }

      // Additional validation for authorization reference format
      if (authorizationReference.length < 3) {
        debugPrint(
            '❌ CRITICAL ERROR: Authorization reference too short: "${authorizationReference}" (${authorizationReference.length} chars)');
        return ApiResponse<Map<String, dynamic>>(
          success: false,
          message:
              'Invalid authorization reference: too short (${authorizationReference.length} characters)',
        );
      }

      // Check for common invalid values
      final invalidValues = ['null', 'undefined', 'none', 'error', 'invalid'];
      if (invalidValues.contains(authorizationReference.toLowerCase())) {
        debugPrint(
            '❌ CRITICAL ERROR: Authorization reference has invalid value: "$authorizationReference"');
        return ApiResponse<Map<String, dynamic>>(
          success: false,
          message: 'Invalid authorization reference: "$authorizationReference"',
        );
      }

      debugPrint(
          '✅ Authorization reference validation passed: "$authorizationReference"');

      // Make the API call using the exact endpoint format from the documentation
      final rawResponse = await _apiService.get(
        '/user/sessions/on-going-data',
        queryParams: {
          'authorization_reference': authorizationReference,
        },
      );

      // Comprehensive debug logging for API response
      debugPrint('🔍 ===== REAL-TIME DATA POLLING API RESPONSE =====');
      debugPrint('🔍 Response Type: ${rawResponse.runtimeType}');
      debugPrint('🔍 Response Content: $rawResponse');

      // ULTIMATE MAP CONVERSION - PERMANENT FIX
      final Map<String, dynamic> response;
      try {
        response = _ultimateMapConverter(rawResponse, 'getChargingData');
      } catch (e) {
        return ApiResponse<Map<String, dynamic>>(
          success: false,
          message: 'Response conversion failed: $e',
        );
      }

      // Log all response fields for debugging
      debugPrint('🔍 Response Keys: ${response.keys.toList()}');
      debugPrint('🔍 Success Field: ${response['success']}');
      debugPrint('🔍 Message Field: ${response['message']}');
      debugPrint('🔍 Data Field: ${response['data']}');

      // Check if the API call was successful
      if (response['success'] == true) {
        // ULTIMATE DATA CONVERSION - PERMANENT FIX
        final Map<String, dynamic> responseData;
        try {
          responseData =
              _ultimateMapConverter(response['data'], 'getChargingData-data');
        } catch (e) {
          return ApiResponse<Map<String, dynamic>>(
            success: false,
            message: 'Data conversion failed: $e',
          );
        }

        // Log all data fields for comprehensive debugging
        if (responseData.isNotEmpty) {
          debugPrint('🔍 ===== CHARGING SESSION DATA FIELDS =====');
          debugPrint('🔍 Status: ${responseData['status']}');
          debugPrint('🔍 SOC: ${responseData['soc']}');
          debugPrint('🔍 Energy: ${responseData['energy']}');
          debugPrint('🔍 Power: ${responseData['power']}');
          debugPrint('🔍 Amount: ${responseData['amount']}');
          debugPrint('🔍 CO2: ${responseData['co2']}');
          debugPrint('🔍 Unit: ${responseData['unit']}');
          debugPrint('🔍 Power Output: ${responseData['power_output']}');
          debugPrint('🔍 All Data Keys: ${responseData.keys.toList()}');
        } else {
          debugPrint('❌ WARNING: Response data is empty');
        }

        return ApiResponse<Map<String, dynamic>>(
          success: true,
          message: response['message'] ?? '',
          data: responseData,
        );
      } else {
        debugPrint('❌ API ERROR: ${response['message']}');
        return ApiResponse<Map<String, dynamic>>(
          success: false,
          message: response['message'] ?? 'Unknown API error',
        );
      }
    } catch (e) {
      debugPrint('❌ EXCEPTION in getChargingData: $e');
      debugPrint('❌ Exception Type: ${e.runtimeType}');
      return ApiResponse<Map<String, dynamic>>(
        success: false,
        message: e.toString(),
      );
    }
  }

  /// Stop a charging session - Step 4 of charging flow (onStopTransaction)
  ///
  /// Endpoint: POST https://api2.eeil.online/api/v1/user/sessions/stop/{transactionId}
  /// Parameters:
  /// - transactionId: The ID of the transaction to stop
  Future<ApiResponse<Map<String, dynamic>>> stopChargingSession({
    required String transactionId,
  }) async {
    try {
      debugPrint('🛑 ===== STOP CHARGING SESSION API CALL =====');
      debugPrint('🛑 Transaction ID: $transactionId');
      debugPrint(
          '🛑 Endpoint: ${ApiConfig.chargingSessionStop}/$transactionId');

      // Make the API call using the correct endpoint format with transaction ID in URL path
      final rawResponse = await _apiService.post(
        '${ApiConfig.chargingSessionStop}/$transactionId',
        data: {}, // Empty data since transaction ID is in URL path
      );

      // MAP CONVERSION
      final Map<String, dynamic> response;
      try {
        response = _ultimateMapConverter(rawResponse, 'stopChargingSession');
      } catch (e) {
        return ApiResponse<Map<String, dynamic>>(
          success: false,
          message: 'Response conversion failed: $e',
        );
      }

      // Check if the API call was successful
      if (response['success'] == true) {
        // DATA CONVERSION
        final Map<String, dynamic> convertedData;
        try {
          convertedData = _ultimateMapConverter(
              response['data'], 'stopChargingSession-data');
        } catch (e) {
          return ApiResponse<Map<String, dynamic>>(
            success: false,
            message: 'Data conversion failed: $e',
          );
        }

        return ApiResponse<Map<String, dynamic>>(
          success: true,
          message: response['message'] ?? '',
          data: convertedData,
        );
      } else {
        return ApiResponse<Map<String, dynamic>>(
          success: false,
          message: response['message'] ?? '',
        );
      }
    } catch (e) {
      return ApiResponse<Map<String, dynamic>>(
        success: false,
        message: e.toString(),
      );
    }
  }

  /// Get the status of a charging session
  ///
  /// Parameters:
  /// - sessionId: The ID of the session to check
  Future<ApiResponse<Map<String, dynamic>>> getChargingSessionStatus({
    required String sessionId,
  }) async {
    try {
      // Make the API call
      final rawResponse =
          await _apiService.get('/user/sessions/$sessionId/status');

      // ULTIMATE MAP CONVERSION - PERMANENT FIX
      final Map<String, dynamic> response;
      try {
        response =
            _ultimateMapConverter(rawResponse, 'getChargingSessionStatus');
      } catch (e) {
        return ApiResponse<Map<String, dynamic>>(
          success: false,
          message: 'Response conversion failed: $e',
        );
      }

      // Check if the API call was successful
      if (response['success'] == true) {
        // ULTIMATE DATA CONVERSION - PERMANENT FIX
        final Map<String, dynamic> convertedData;
        try {
          convertedData = _ultimateMapConverter(
              response['data'], 'getChargingSessionStatus-data');
        } catch (e) {
          return ApiResponse<Map<String, dynamic>>(
            success: false,
            message: 'Data conversion failed: $e',
          );
        }

        return ApiResponse<Map<String, dynamic>>(
          success: true,
          message: response['message'] ?? '',
          data: convertedData,
        );
      } else {
        return ApiResponse<Map<String, dynamic>>(
          success: false,
          message: response['message'] ?? '',
        );
      }
    } catch (e) {
      return ApiResponse<Map<String, dynamic>>(
        success: false,
        message: e.toString(),
      );
    }
  }
}

/// Comprehensive charging flow manager that handles the complete charging process
/// according to the backend logic provided - matches JavaScript implementation exactly
class ChargingFlowManager {
  final ChargingSessionService _chargingService;
  final PersistentChargingService _persistentService =
      PersistentChargingService();
  Timer? _dataPollingTimer;

  // Singleton instance
  static final ChargingFlowManager _instance = ChargingFlowManager._internal();
  factory ChargingFlowManager() => _instance;

  ChargingFlowManager._internal() : _chargingService = ChargingSessionService();

  /// 🚨 NEW: Session verification and polling only (skip Step 1 - startChargingSession)
  /// This method is used when the session was already started in ChargingOptionsPage
  /// It performs ONLY Step 2 (session verification) and Step 3 (real-time polling)
  Future<void> startSessionVerificationAndPolling({
    required String evseUid,
    required String connectorId,
    required double chargingValue,
    required bool instantCharging,
    required String chargeType, // 'units' or 'amount'
    required double walletBalance,
    required double pricePerUnit,
    String? promoId,
    String? promoCode,
    required Function(Map<String, dynamic>) onDataReceived,
    required Function(String) onError,
    required Function() onSessionComplete,
    required Function(Map<String, dynamic>) onSessionVerified,
  }) async {
    debugPrint('🔍 ===== STARTING SESSION VERIFICATION AND POLLING ONLY =====');
    debugPrint('🔍 Skipping Step 1 (startChargingSession) - already done');
    debugPrint('🔍 Starting Step 2: Session Verification');

    try {
      // STEP 2: Verify ongoing session (skip Step 1)
      debugPrint('🔍 ===== STEP 2: VERIFY ONGOING SESSION =====');
      debugPrint('🔍 Looking for existing active session...');

      // Get the most recent transaction ID from the charging parameters service
      final chargingParamsService = ChargingParametersService();
      final storedTransactionId = chargingParamsService.getTransactionId();

      if (storedTransactionId == null || storedTransactionId.isEmpty) {
        debugPrint('❌ No stored transaction ID found');
        onError('No active session found. Please start charging again.');
        return;
      }

      debugPrint('🔍 Using stored transaction ID: $storedTransactionId');

      final verifyResponse = await _chargingService.verifyOngoingSession(
        transactionId: storedTransactionId,
      );

      debugPrint('🔍 ===== VERIFY RESPONSE ANALYSIS =====');
      debugPrint('🔍 Verify Success: ${verifyResponse.success}');
      debugPrint('🔍 Verify Message: ${verifyResponse.message}');
      debugPrint('🔍 Verify Data: ${verifyResponse.data}');

      if (!verifyResponse.success || verifyResponse.data == null) {
        debugPrint('❌ ===== STEP 2 FAILED: SESSION VERIFICATION FAILED =====');
        debugPrint('❌ Error: ${verifyResponse.message}');
        onError(verifyResponse.message);
        return;
      }

      // Extract authorization reference for Step 3
      final String? extractedAuthRef =
          _chargingService._extractAuthorizationReference(
              verifyResponse.data!, 'startSessionVerificationAndPolling-Step2');

      if (extractedAuthRef == null || extractedAuthRef.isEmpty) {
        debugPrint(
            '❌ ===== STEP 2 FAILED: AUTHORIZATION REFERENCE NOT FOUND =====');
        debugPrint('❌ Available fields: ${verifyResponse.data!.keys.toList()}');
        onError(
            'Session verification failed - authorization reference not found');
        return;
      }

      debugPrint('✅ ===== STEP 2 COMPLETED: SESSION VERIFIED =====');
      debugPrint('✅ Authorization Reference: "$extractedAuthRef"');
      debugPrint('✅ Session Data: ${verifyResponse.data}');

      // Trigger Step 2 completion callback
      onSessionVerified(verifyResponse.data!);

      // STEP 3: Start real-time data polling
      debugPrint('🔍 ===== STEP 3: STARTING REAL-TIME DATA POLLING =====');
      await startChargingDataPolling(
        authorizationReference: extractedAuthRef,
        transactionId: storedTransactionId,
        onDataReceived: onDataReceived,
        onError: onError,
        onSessionComplete: onSessionComplete,
      );
    } catch (e, stackTrace) {
      debugPrint('❌ CRITICAL ERROR in startSessionVerificationAndPolling: $e');
      debugPrint('❌ Stack trace: $stackTrace');
      onError('Session verification failed: ${e.toString()}');
    }
  }

  /// Complete charging flow implementation with AUTHENTIC API DATA
  ///
  /// This method handles the entire charging process using authentic station detail API data:
  /// 1. Start charging session with authentic parameters (onStartTransaction)
  /// 2. Verify ongoing session (onGetCurrentTransaction)
  /// 3. Start polling for real-time data (GetCharginDataViaApi)
  /// 4. Handle session completion
  Future<void> startCompleteChargingFlow({
    required String evseUid,
    required String connectorId,
    required double chargingValue,
    required bool instantCharging,
    required String chargeType, // 'units' or 'amount'
    required double walletBalance,
    required double pricePerUnit,
    String? promoId,
    String? promoCode,
    required Function(Map<String, dynamic>) onDataReceived,
    required Function(String) onError,
    required Function() onSessionComplete,
    required Function(Map<String, dynamic>) onSessionVerified,
  }) async {
    try {
      // Calculate value using backend logic
      double calculatedValue = chargingValue;

      // Match backend logic exactly:
      if (instantCharging) {
        calculatedValue = walletBalance;
      }

      // Convert from monetary amount to units if needed (exactly as backend does)
      if (chargeType == 'amount' || instantCharging) {
        if (pricePerUnit > 0) {
          calculatedValue =
              double.parse((calculatedValue / pricePerUnit).toStringAsFixed(2));
        }
      }

      // Step 1: Start charging session
      final startResponse = await _chargingService.startChargingSession(
        evseUid: evseUid,
        connectorId: connectorId,
        chargingValue: calculatedValue,
        instantCharging: instantCharging,
        promoId: promoId,
        promoCode: promoCode,
      );

      // STEP 1 SUCCESS VALIDATION: Check if API response indicates success
      debugPrint('🔍 ===== STEP 1: SUCCESS VALIDATION =====');
      debugPrint('🔍 Start Response Success Field: ${startResponse.success}');
      debugPrint('🔍 Start Response Data: ${startResponse.data}');
      debugPrint('🔍 Start Response Message: "${startResponse.message}"');

      // Check if Step 1 failed at the API service level
      if (!startResponse.success) {
        debugPrint('❌ STEP 1 FAILED: API service indicates failure');
        debugPrint('❌ Server error message: "${startResponse.message}"');
        // Display exact server-side error message without interpretation
        onError(startResponse.message);
        return;
      }

      // Check if response data is missing
      if (startResponse.data == null) {
        debugPrint('❌ STEP 1 FAILED: Response data is null');
        debugPrint('❌ Server message: "${startResponse.message}"');
        // Display exact server-side error message without interpretation
        onError(startResponse.message);
        return;
      }

      // CRITICAL: Check if server response contains "success": true
      final serverSuccess = startResponse.data!['success'];
      debugPrint('🔍 Server Success Field: $serverSuccess');
      debugPrint('🔍 Server Success Type: ${serverSuccess.runtimeType}');

      if (serverSuccess != true) {
        debugPrint(
            '❌ STEP 1 FAILED: Server response does not contain "success": true');
        debugPrint('🔍 Server success field: $serverSuccess');
        debugPrint('🔍 Server message: "${startResponse.data!['message']}"');

        // Use server message from response data if available, otherwise use API service message
        final errorMessage =
            startResponse.data!['message']?.toString() ?? startResponse.message;
        debugPrint('❌ Displaying exact server error: "$errorMessage"');

        // Display exact server-side error message without interpretation
        onError(errorMessage);
        return;
      }

      debugPrint('✅ STEP 1 SUCCESS VALIDATION PASSED');
      debugPrint('✅ Server response contains "success": true');
      debugPrint('✅ Proceeding to Step 2 (verify ongoing session)');

      // CONDITIONAL EXECUTION: Only proceed to Step 2 if Step 1 has "success": true
      // Expected successful response: {"message": "Transaction start", "id": 19080, "success": true}

      // CRITICAL: Extract transaction ID from Step 1 response data
      // Expected Step 1 Response: {"message": "Transaction start", "id": 19080, "success": true}
      debugPrint('🔍 ===== STEP 1: TRANSACTION ID EXTRACTION =====');
      debugPrint('🔍 Complete Step 1 Response: ${startResponse.data}');
      debugPrint(
          '🔍 Expected format: {"message": "Transaction start", "id": 19080, "success": true}');
      debugPrint(
          '🔍 Start Response Data Type: ${startResponse.data.runtimeType}');
      debugPrint(
          '🔍 Start Response Data Keys: ${startResponse.data!.keys.toList()}');

      // Extract the "id" field (19080) from the Step 1 response
      final transactionId = startResponse.data!['id'];
      debugPrint('🔍 Raw Transaction ID from Step 1: $transactionId');
      debugPrint('🔍 Transaction ID Type: ${transactionId.runtimeType}');

      if (transactionId == null) {
        debugPrint('❌ STEP 1 FAILED: Transaction ID not found in response');
        debugPrint('❌ Available fields: ${startResponse.data!.keys.toList()}');
        debugPrint('❌ Full Step 1 response: ${startResponse.data}');
        onError('Transaction ID not found in response');
        return;
      }

      // SUCCESS: Transaction ID extracted from Step 1 response
      debugPrint('✅ Extracted transaction ID: $transactionId');
      debugPrint('🔍 Transaction ID type: ${transactionId.runtimeType}');
      debugPrint(
          '🔍 This ID will be used for Step 2 payload: {"id": $transactionId}');

      // STEP 2 - Use transaction ID from Step 1 as payload for verify ongoing session
      debugPrint('🔍 ===== STEP 2: VERIFY ONGOING SESSION =====');
      debugPrint('🔍 API Endpoint: POST /api/v1/user/sessions/on-going');
      debugPrint('🔍 Step 2 payload: {"id": $transactionId}');
      debugPrint('🔍 Transaction ID from Step 1: $transactionId');

      // Convert transaction ID to string for the API service call
      final String transactionIdForApi = transactionId.toString();

      final verifyResponse = await _chargingService.verifyOngoingSession(
        transactionId: transactionIdForApi,
      );

      debugPrint('🔍 ===== VERIFY RESPONSE ANALYSIS =====');
      debugPrint('🔍 Verify Success: ${verifyResponse.success}');
      debugPrint('🔍 Verify Message: "${verifyResponse.message}"');
      debugPrint('🔍 Verify Data: ${verifyResponse.data}');

      if (!verifyResponse.success) {
        debugPrint('❌ STEP 2 FAILED: Verify ongoing session failed');
        // Pass exact server-side error message without interpretation
        onError(verifyResponse.message);
        return;
      }

      if (verifyResponse.data == null || verifyResponse.data!.isEmpty) {
        debugPrint('❌ STEP 2 FAILED: Empty or null response data');
        // Pass exact server message without interpretation
        onError(verifyResponse.message);
        return;
      }

      // ENHANCED BULLETPROOF AUTHORIZATION REFERENCE EXTRACTION
      debugPrint(
          '🔍 ===== STEP 2: ENHANCED BULLETPROOF AUTHORIZATION REFERENCE EXTRACTION =====');

      // Use the centralized extraction utility for maximum reliability
      final String? extractedAuthRef =
          _chargingService._extractAuthorizationReference(
              verifyResponse.data!, 'startCompleteChargingFlow-Step2');

      // ENHANCED VALIDATION AND ERROR HANDLING
      if (extractedAuthRef == null || extractedAuthRef.isEmpty) {
        debugPrint(
            '❌ ===== STEP 2 FAILED: AUTHORIZATION REFERENCE NOT FOUND =====');
        debugPrint(
            '❌ Available response fields: ${verifyResponse.data!.keys.toList()}');
        debugPrint('❌ Full response data: ${verifyResponse.data}');
        debugPrint(
            '❌ The ongoing session API did not return any valid authorization reference');

        // Create comprehensive error message for debugging
        final errorDetails = [
          'Authorization reference not found in Step 2 response',
          'Available fields: ${verifyResponse.data!.keys.join(', ')}',
          'Response data: ${verifyResponse.data}',
          'This indicates the OCPP session verification failed or returned incomplete data',
        ].join(' | ');

        onError('Missing authorization reference: $errorDetails');
        return;
      }

      final String authRef = extractedAuthRef;
      debugPrint(
          '✅ ===== STEP 2 SUCCESS: AUTHORIZATION REFERENCE EXTRACTED =====');
      debugPrint('✅ Authorization Reference: "$authRef"');
      debugPrint('✅ Length: ${authRef.length} characters');
      debugPrint('✅ Ready for Step 3 (real-time data polling)');

      // Notify that session is verified (matching backend alert)
      // ENHANCED: Safe callback execution with null checks
      try {
        debugPrint(
            '🔍 Calling onSessionVerified callback with data: ${verifyResponse.data}');
        onSessionVerified(verifyResponse.data!);
        debugPrint('✅ onSessionVerified callback completed successfully');
      } catch (e) {
        debugPrint('❌ ERROR in onSessionVerified callback: $e');
        onError('Session verification callback failed: $e');
        return;
      }

      // Step 3: Start polling for real-time charging data (matching backend GetCharginDataViaApi)
      debugPrint('🔍 ===== STEP 3: STARTING REAL-TIME POLLING =====');

      // ENHANCED: Validate authorization reference before Step 3
      try {
        // Final validation of authorization reference format
        if (authRef.length < 3) {
          debugPrint(
              '⚠️ WARNING: Authorization reference seems unusually short: "$authRef"');
        }

        // Check for common invalid values
        final invalidValues = ['null', 'undefined', 'none', '', 'error'];
        if (invalidValues.contains(authRef.toLowerCase())) {
          throw Exception('Invalid authorization reference value: "$authRef"');
        }

        debugPrint(
            '✅ Authorization reference validated for Step 3: "$authRef"');

        // ENHANCED: Safe transaction ID conversion for polling
        final String transactionIdForPolling = transactionId.toString();
        debugPrint('🔍 Transaction ID for polling: "$transactionIdForPolling"');
        debugPrint(
            '🔍 Starting real-time data polling with validated parameters');

        await startChargingDataPolling(
          authorizationReference: authRef,
          transactionId: transactionIdForPolling,
          onDataReceived: onDataReceived,
          onError: onError,
          onSessionComplete: onSessionComplete,
        );

        debugPrint('✅ Step 3 (real-time polling) initiated successfully');
      } catch (e) {
        debugPrint('❌ CRITICAL ERROR in Step 3 initialization: $e');
        onError('Failed to start real-time polling: $e');
        return;
      }
    } catch (e) {
      onError(e.toString());
    }
  }

  /// Start polling for real-time charging data (matching backend GetCharginDataViaApi)
  Future<void> startChargingDataPolling({
    required String authorizationReference,
    required String transactionId,
    required Function(Map<String, dynamic>) onDataReceived,
    required Function(String) onError,
    required Function() onSessionComplete,
  }) async {
    debugPrint('🔍 ===== STARTING REAL-TIME DATA POLLING =====');
    debugPrint('🔍 Authorization Reference: "$authorizationReference"');
    debugPrint('🔍 Transaction ID: "$transactionId"');
    debugPrint('🔍 Polling Interval: 15 seconds');
    debugPrint('🔍 API Endpoint: GET /api/v1/user/sessions/on-going-data');

    // ENHANCED BULLETPROOF VALIDATION: Comprehensive input validation before starting polling
    try {
      // ENHANCED AUTHORIZATION REFERENCE VALIDATION
      if (authorizationReference.isEmpty) {
        throw Exception('Empty authorization reference - cannot start polling');
      }

      // Enhanced authorization reference format validation
      if (authorizationReference.length < 3) {
        throw Exception(
            'Authorization reference too short (${authorizationReference.length} chars): "$authorizationReference"');
      }

      // Enhanced check for invalid authorization reference values
      final invalidAuthValues = [
        'null',
        'undefined',
        'none',
        'error',
        'invalid',
        'false',
        'true',
        '0',
        '-1',
        'n/a',
        'na',
        'empty',
        'test',
        'demo'
      ];
      if (invalidAuthValues.contains(authorizationReference.toLowerCase())) {
        throw Exception(
            'Invalid authorization reference value: "$authorizationReference"');
      }

      // Check for suspicious patterns in authorization reference
      if (authorizationReference.toLowerCase().contains('password') ||
          authorizationReference.toLowerCase().contains('secret') ||
          authorizationReference.toLowerCase().contains('key')) {
        throw Exception(
            'Authorization reference contains suspicious content: "$authorizationReference"');
      }

      // ENHANCED TRANSACTION ID VALIDATION
      if (transactionId.isEmpty) {
        throw Exception('Empty transaction ID - cannot start polling');
      }

      // Enhanced check for invalid transaction ID values
      final invalidTxnValues = [
        'null',
        'undefined',
        'none',
        'error',
        'invalid',
        '0',
        '-1',
        'false',
        'true',
        'n/a',
        'na',
        'empty'
      ];
      if (invalidTxnValues.contains(transactionId.toLowerCase())) {
        throw Exception('Invalid transaction ID value: "$transactionId"');
      }

      // Additional transaction ID format validation (already checked for empty above)
      // Transaction ID should be meaningful after trimming whitespace
      if (transactionId.trim().isEmpty) {
        throw Exception(
            'Transaction ID is effectively empty after trimming: "$transactionId"');
      }

      debugPrint(
          '✅ Enhanced input validation passed - authorization reference and transaction ID are valid');
      debugPrint(
          '✅ Authorization Reference: "$authorizationReference" (${authorizationReference.length} chars)');
      debugPrint(
          '✅ Transaction ID: "$transactionId" (${transactionId.length} chars)');
      debugPrint(
          '✅ All validation checks passed - proceeding with polling setup');
    } catch (e) {
      debugPrint('❌ CRITICAL ERROR: Enhanced input validation failed: $e');
      onError('Polling validation failed: $e');
      return;
    }

    // Cancel any existing timer
    if (_dataPollingTimer?.isActive == true) {
      debugPrint('🔄 Cancelling existing polling timer');
      _dataPollingTimer?.cancel();
    }

    // 🚨 NEW: Start persistent charging service for background operation
    debugPrint('🔋 ===== STARTING PERSISTENT CHARGING SERVICE =====');
    try {
      await _persistentService.initialize();

      // Extract station UID and connector ID from the current session
      // These should be available from the charging parameters service
      final chargingParams = ChargingParametersService();
      final stationUid =
          chargingParams.getEvsesUid(); // Use EVSE UID as station UID
      final connectorId =
          chargingParams.getChargingParameters()?['connectorId']?.toString() ??
              'UNKNOWN';

      debugPrint('🔋 Station UID: $stationUid');
      debugPrint('🔋 Connector ID: $connectorId');
      debugPrint('🔋 Transaction ID: $transactionId');
      debugPrint('🔋 Auth Reference: $authorizationReference');

      await _persistentService.startPersistentChargingSession(
        stationUid: stationUid,
        connectorId: connectorId,
        transactionId: transactionId,
        authorizationReference: authorizationReference,
        onDataReceived: onDataReceived,
        onError: onError,
        onSessionComplete: onSessionComplete,
      );

      debugPrint('✅ Persistent charging service started successfully');
    } catch (e) {
      debugPrint('❌ Error starting persistent charging service: $e');
      // Continue with regular polling even if persistent service fails
    }

    debugPrint('✅ Starting immediate first polling call');

    // First immediate call (matching backend logic)
    await _fetchChargingData(
      authorizationReference: authorizationReference,
      transactionId: transactionId,
      onDataReceived: onDataReceived,
      onError: onError,
      onSessionComplete: onSessionComplete,
    );

    debugPrint('✅ Setting up periodic polling timer (15 seconds)');

    // Set up periodic polling every 15 seconds as per backend
    _dataPollingTimer = Timer.periodic(const Duration(seconds: 15), (_) async {
      debugPrint('⏰ Periodic polling timer triggered');
      await _fetchChargingData(
        authorizationReference: authorizationReference,
        transactionId: transactionId,
        onDataReceived: onDataReceived,
        onError: onError,
        onSessionComplete: onSessionComplete,
      );
    });

    debugPrint('✅ Real-time data polling started successfully');
  }

  /// Fetch charging data from the API (matching backend GetCharginDataViaApi logic)
  Future<void> _fetchChargingData({
    required String authorizationReference,
    required String transactionId,
    required Function(Map<String, dynamic>) onDataReceived,
    required Function(String) onError,
    required Function() onSessionComplete,
  }) async {
    try {
      debugPrint('🔍 ===== POLLING ATTEMPT =====');
      debugPrint('🔍 Authorization Reference: "$authorizationReference"');
      debugPrint('🔍 Transaction ID: "$transactionId"');
      debugPrint('🔍 Polling Timer Active: ${_dataPollingTimer?.isActive}');

      final response = await _chargingService.getChargingData(
        authorizationReference: authorizationReference,
      );

      debugPrint('🔍 ===== POLLING RESPONSE =====');
      debugPrint('🔍 Response Success: ${response.success}');
      debugPrint('🔍 Response Message: "${response.message}"');
      debugPrint('🔍 Response Data: ${response.data}');

      if (response.success && response.data != null) {
        final sessionData = response.data!;
        final sessionStatus = sessionData['status'];

        debugPrint('🔍 ===== SESSION STATUS CHECK =====');
        debugPrint('🔍 Session Status: "$sessionStatus"');
        debugPrint('🔍 Is Active: ${sessionStatus == 'ACTIVE'}');

        // Extract and log all important session data fields
        final soc = sessionData['soc'];
        final energy = sessionData['energy'];
        final power = sessionData['power'];
        final amount = sessionData['amount'];
        final co2 = sessionData['co2'];
        final unit = sessionData['unit'];
        final powerOutput = sessionData['power_output'];

        debugPrint('🔍 ===== SESSION DATA SUMMARY =====');
        debugPrint('🔍 SOC: $soc');
        debugPrint('🔍 Energy: $energy');
        debugPrint('🔍 Power: $power');
        debugPrint('🔍 Amount: $amount');
        debugPrint('🔍 CO2: $co2');
        debugPrint('🔍 Unit: $unit');
        debugPrint('🔍 Power Output: $powerOutput');

        // Check if charging is still active (exactly matching backend logic)
        if (sessionStatus == 'ACTIVE') {
          debugPrint('✅ Session is ACTIVE - continuing polling');
          // ENHANCED: Safe callback execution with null checks
          try {
            debugPrint(
                '🔍 Calling onDataReceived callback with data: $sessionData');
            onDataReceived(sessionData);
            debugPrint('✅ onDataReceived callback completed successfully');
          } catch (e) {
            debugPrint('❌ ERROR in onDataReceived callback: $e');
            onError('Data received callback failed: $e');
          }
        } else {
          // Charging is no longer active (matching backend logic)
          debugPrint('🔄 Session status changed to: $sessionStatus');
          debugPrint('🛑 Stopping polling and completing session');
          stopChargingDataPolling();
          // ENHANCED: Safe callback execution with null checks
          try {
            debugPrint('🔍 Calling onSessionComplete callback');
            onSessionComplete();
            debugPrint('✅ onSessionComplete callback completed successfully');
          } catch (e) {
            debugPrint('❌ ERROR in onSessionComplete callback: $e');
            onError('Session complete callback failed: $e');
          }
        }
      } else {
        debugPrint('❌ POLLING ERROR: ${response.message}');
        debugPrint('❌ Response Success: ${response.success}');
        debugPrint('❌ Response Data: ${response.data}');

        // Check if this is a network timeout or API error
        if (response.message.toLowerCase().contains('timeout') ||
            response.message.toLowerCase().contains('connection')) {
          debugPrint('🔄 Network error detected - continuing polling');
          // Don't stop polling for network errors, let it retry
        } else {
          debugPrint('🛑 API error detected - stopping polling');
          stopChargingDataPolling();
        }

        onError(response.message);
      }
    } catch (e) {
      debugPrint('❌ EXCEPTION in _fetchChargingData: $e');
      debugPrint('❌ Exception Type: ${e.runtimeType}');

      // Check if this is a network-related exception
      if (e.toString().toLowerCase().contains('timeout') ||
          e.toString().toLowerCase().contains('connection') ||
          e.toString().toLowerCase().contains('socket')) {
        debugPrint('🔄 Network exception detected - continuing polling');
        // Don't stop polling for network exceptions, let it retry
      } else {
        debugPrint('🛑 Critical exception detected - stopping polling');
        stopChargingDataPolling();
      }

      // Pass the raw error without interpretation
      onError(e.toString());
    }
  }

  /// Stop charging session remotely (matching backend onStopTransaction)
  Future<ApiResponse<Map<String, dynamic>>> stopChargingSession({
    required String transactionId,
    required Function(String) onError,
    required Function(Map<String, dynamic>) onSessionStopped,
  }) async {
    try {
      final response = await _chargingService.stopChargingSession(
        transactionId: transactionId,
      );

      if (response.success) {
        // Stop polling immediately
        stopChargingDataPolling();

        // Pass final billing data to callback
        if (response.data != null) {
          onSessionStopped(response.data!);
        }

        return response;
      } else {
        // Pass exact server error message
        onError(response.message);
        return response;
      }
    } catch (e) {
      // Pass raw error without interpretation
      onError(e.toString());
      return ApiResponse<Map<String, dynamic>>(
        success: false,
        message: e.toString(),
      );
    }
  }

  /// Stop the data polling timer
  void stopChargingDataPolling() {
    debugPrint('🔍 ===== STOPPING REAL-TIME DATA POLLING =====');
    debugPrint('🔍 Timer Active: ${_dataPollingTimer?.isActive}');

    if (_dataPollingTimer?.isActive == true) {
      debugPrint('🛑 Cancelling active polling timer');
      _dataPollingTimer?.cancel();
    } else {
      debugPrint('ℹ️ No active polling timer to cancel');
    }

    _dataPollingTimer = null;
    debugPrint('✅ Real-time data polling stopped successfully');
  }

  /// Clean up resources
  void dispose() {
    debugPrint('🔋 ===== DISPOSING CHARGING FLOW MANAGER =====');

    // Stop regular polling
    stopChargingDataPolling();

    // Stop persistent charging service
    try {
      _persistentService.stopPersistentChargingSession();
      debugPrint('✅ Persistent charging service stopped');
    } catch (e) {
      debugPrint('❌ Error stopping persistent charging service: $e');
    }

    debugPrint('✅ Charging flow manager disposed');
  }
}
