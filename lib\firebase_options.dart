// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyAdQ49tgN61V6taM4PJHYOD2pdmKkwMjW4',
    appId: '1:386706794878:web:WEB_APP_ID_PLACEHOLDER',
    messagingSenderId: '386706794878',
    projectId: 'ecoplug-9ab21',
    authDomain: 'ecoplug-9ab21.firebaseapp.com',
    storageBucket: 'ecoplug-9ab21.appspot.com',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyAdQ49tgN61V6taM4PJHYOD2pdmKkwMjW4',
    appId: '1:386706794878:android:7e19a2e8f607c0584eab2f',
    messagingSenderId: '386706794878',
    projectId: 'ecoplug-9ab21',
    storageBucket: 'ecoplug-9ab21.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'NEED_IOS_API_KEY',
    appId: '1:386706794878:ios:NEED_IOS_APP_ID',
    messagingSenderId: '386706794878',
    projectId: 'ecoplug-9ab21',
    storageBucket: 'ecoplug-9ab21.appspot.com',
    iosBundleId: 'com.eeil.ecoplug',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'YOUR_IOS_API_KEY',
    appId: '1:YOUR_PROJECT_NUMBER:ios:YOUR_MACOS_APP_ID',
    messagingSenderId: 'YOUR_PROJECT_NUMBER',
    projectId: 'your-ecoplug-project-id',
    storageBucket: 'your-ecoplug-project-id.appspot.com',
    iosBundleId: 'com.eeil.ecoplug',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'YOUR_WEB_API_KEY',
    appId: '1:YOUR_PROJECT_NUMBER:web:YOUR_WINDOWS_APP_ID',
    messagingSenderId: 'YOUR_PROJECT_NUMBER',
    projectId: 'your-ecoplug-project-id',
    authDomain: 'your-ecoplug-project-id.firebaseapp.com',
    storageBucket: 'your-ecoplug-project-id.appspot.com',
  );
}
