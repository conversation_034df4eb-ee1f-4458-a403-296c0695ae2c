import 'package:flutter/material.dart';
import '../widgets/charging_notification_pin_bar.dart';
import '../widgets/native_charging_notification_pin_bar.dart';
 
/// Example demonstrating how to replace ChargingNotificationPinBar with native notifications
/// This shows the migration path and usage patterns for the native notification integration
class PinBarNotificationIntegrationExample extends StatefulWidget {
  const PinBarNotificationIntegrationExample({super.key});

  @override
  State<PinBarNotificationIntegrationExample> createState() =>
      _PinBarNotificationIntegrationExampleState();
}

class _PinBarNotificationIntegrationExampleState extends State<PinBarNotificationIntegrationExample> {
  // Sample charging data
  bool _isCharging = true;
  double _chargePercentage = 0.68;
  String _currentPower = "7.2 kW";
  String _energyDelivered = "5.4 kWh";
  String _currentPrice = "₹1.51";
  double _carbonSavings = 8.2;
  double _energySourcePercentage = 95.0;
  String _chargingTimer = "00:45:30";
  bool _showNotification = true;
  bool _useNativeNotification = true;

  @override
  void initState() {
    super.initState();
    _initializeNativeNotifications();
  }

  Future<void> _initializeNativeNotifications() async {
    await PinBarNotificationMigrationHelper.initializeAndSetMode();
    setState(() {
      _useNativeNotification = PinBarNotificationMigrationHelper.useNativeNotifications;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Pin Bar Notification Integration'),
        backgroundColor: const Color(0xFF4CAF50),
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          // Control Panel
          _buildControlPanel(),
          
          // Notification Display Area
          Expanded(
            child: _buildNotificationArea(),
          ),
          
          // Status Information
          _buildStatusInfo(),
        ],
      ),
    );
  }

  Widget _buildControlPanel() {
    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Control Panel',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF333333),
            ),
          ),
          const SizedBox(height: 16),
          
          // Notification Type Toggle
          Row(
            children: [
              const Text('Notification Type: '),
              const SizedBox(width: 8),
              ChoiceChip(
                label: const Text('In-App Widget'),
                selected: !_useNativeNotification,
                onSelected: (selected) {
                  if (selected) {
                    setState(() {
                      _useNativeNotification = false;
                    });
                  }
                },
              ),
              const SizedBox(width: 8),
              ChoiceChip(
                label: const Text('Native Notification'),
                selected: _useNativeNotification,
                onSelected: (selected) {
                  if (selected) {
                    setState(() {
                      _useNativeNotification = true;
                    });
                  }
                },
              ),
            ],
          ),
          const SizedBox(height: 12),
          
          // Visibility Toggle
          SwitchListTile(
            title: const Text('Show Notification'),
            value: _showNotification,
            onChanged: (value) {
              setState(() {
                _showNotification = value;
              });
            },
            activeColor: const Color(0xFF4CAF50),
          ),
          
          // Charging Status Toggle
          SwitchListTile(
            title: const Text('Charging Active'),
            value: _isCharging,
            onChanged: (value) {
              setState(() {
                _isCharging = value;
              });
            },
            activeColor: const Color(0xFF4CAF50),
          ),
          
          // Charge Percentage Slider
          Row(
            children: [
              const Text('Charge: '),
              Expanded(
                child: Slider(
                  value: _chargePercentage,
                  min: 0.0,
                  max: 1.0,
                  divisions: 100,
                  label: '${(_chargePercentage * 100).toInt()}%',
                  onChanged: (value) {
                    setState(() {
                      _chargePercentage = value;
                    });
                  },
                  activeColor: const Color(0xFF4CAF50),
                ),
              ),
            ],
          ),
          
          // Update Buttons
          Row(
            children: [
              ElevatedButton(
                onPressed: _simulateChargingUpdate,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF4CAF50),
                  foregroundColor: Colors.white,
                ),
                child: const Text('Simulate Update'),
              ),
              const SizedBox(width: 8),
              ElevatedButton(
                onPressed: _resetToDefaults,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.grey[600],
                  foregroundColor: Colors.white,
                ),
                child: const Text('Reset'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationArea() {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            _useNativeNotification 
                ? 'Native Android Notification (Check notification bar)' 
                : 'In-App Widget Display',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Color(0xFF333333),
            ),
          ),
          const SizedBox(height: 16),
          
          // Show the appropriate notification type
          if (_showNotification) ...[
            if (_useNativeNotification)
              NativeChargingNotificationPinBar(
                isCharging: _isCharging,
                chargePercentage: _chargePercentage,
                currentPower: _currentPower,
                energyDelivered: _energyDelivered,
                currentPrice: _currentPrice,
                carbonSavings: _carbonSavings,
                energySourcePercentage: _energySourcePercentage,
                chargingTimer: _chargingTimer,
                isVisible: _showNotification,
                onClose: () {
                  setState(() {
                    _showNotification = false;
                  });
                },
                onTap: () {
                  debugPrint('Native notification tapped');
                },
              )
            else
              ChargingNotificationPinBar(
                isCharging: _isCharging,
                chargePercentage: _chargePercentage,
                currentPower: _currentPower,
                energyDelivered: _energyDelivered,
                currentPrice: _currentPrice,
                carbonSavings: _carbonSavings,
                energySourcePercentage: _energySourcePercentage,
                chargingTimer: _chargingTimer,
                isVisible: _showNotification,
                onClose: () {
                  setState(() {
                    _showNotification = false;
                  });
                },
                onTap: () {
                  debugPrint('In-app widget tapped');
                },
              ),
          ] else ...[
            const Center(
              child: Text(
                'Notification Hidden',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStatusInfo() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF4CAF50).withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF4CAF50).withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Current Status',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Color(0xFF4CAF50),
            ),
          ),
          const SizedBox(height: 8),
          Text('Mode: ${_useNativeNotification ? "Native Notification" : "In-App Widget"}'),
          Text('Visible: ${_showNotification ? "Yes" : "No"}'),
          Text('Charging: ${_isCharging ? "Active" : "Complete"}'),
          Text('Battery: ${(_chargePercentage * 100).toInt()}%'),
          Text('Power: $_currentPower'),
          Text('Energy: $_energyDelivered'),
          Text('Cost: $_currentPrice'),
          Text('Timer: $_chargingTimer'),
        ],
      ),
    );
  }

  void _simulateChargingUpdate() {
    setState(() {
      // Simulate charging progress
      if (_isCharging && _chargePercentage < 1.0) {
        _chargePercentage = (_chargePercentage + 0.05).clamp(0.0, 1.0);
      }
      
      // Update other values
      final powerValue = (7.2 + (0.5 - 0.25)).toStringAsFixed(1);
      _currentPower = "$powerValue kW";
      
      final energyValue = (_chargePercentage * 10).toStringAsFixed(1);
      _energyDelivered = "$energyValue kWh";
      
      final costValue = (_chargePercentage * 2.5).toStringAsFixed(2);
      _currentPrice = "₹$costValue";
      
      // Update timer (simulate time passing)
      final parts = _chargingTimer.split(':');
      int minutes = int.parse(parts[1]);
      int seconds = int.parse(parts[2]);
      
      seconds += 15;
      if (seconds >= 60) {
        seconds = 0;
        minutes++;
      }
      
      _chargingTimer = "00:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}";
    });
  }

  void _resetToDefaults() {
    setState(() {
      _isCharging = true;
      _chargePercentage = 0.68;
      _currentPower = "7.2 kW";
      _energyDelivered = "5.4 kWh";
      _currentPrice = "₹1.51";
      _carbonSavings = 8.2;
      _energySourcePercentage = 95.0;
      _chargingTimer = "00:45:30";
      _showNotification = true;
    });
  }
}
