import 'package:flutter/foundation.dart';
import '../../../core/api/api_exception.dart';

/// Comprehensive error handling system for PayU payment integration
/// Provides centralized error handling, logging, and user-friendly error messages
class PayUErrorHandler {
  static const String _logPrefix = '❌ PAYU ERROR:';

  /// Handle PayU SDK initialization errors
  static PayUError handleInitializationError(dynamic error) {
    debugPrint('$_logPrefix SDK Initialization failed: $error');
    
    if (error.toString().contains('merchant key')) {
      return PayUError(
        type: PayUErrorType.configuration,
        code: 'INVALID_MERCHANT_KEY',
        message: 'Payment service configuration error. Please contact support.',
        userMessage: 'Payment service is temporarily unavailable. Please try again later.',
        technicalDetails: error.toString(),
        isRetryable: false,
      );
    }
    
    if (error.toString().contains('network') || error.toString().contains('connection')) {
      return PayUError(
        type: PayUErrorType.network,
        code: 'NETWORK_ERROR',
        message: 'Network error during PayU initialization',
        userMessage: 'Please check your internet connection and try again.',
        technicalDetails: error.toString(),
        isRetryable: true,
      );
    }
    
    return PayUError(
      type: PayUErrorType.sdk,
      code: 'SDK_INIT_FAILED',
      message: 'PayU SDK initialization failed',
      userMessage: 'Payment service initialization failed. Please restart the app.',
      technicalDetails: error.toString(),
      isRetryable: true,
    );
  }

  /// Handle PayU payment transaction errors
  static PayUError handleTransactionError(dynamic error, String? transactionId) {
    debugPrint('$_logPrefix Transaction failed: $error (TxnID: $transactionId)');
    
    if (error is ApiException) {
      return _handleApiException(error, transactionId);
    }
    
    final errorString = error.toString().toLowerCase();
    
    // Network-related errors
    if (errorString.contains('timeout') || errorString.contains('connection')) {
      return PayUError(
        type: PayUErrorType.network,
        code: 'TRANSACTION_TIMEOUT',
        message: 'Transaction timed out',
        userMessage: 'Transaction is taking longer than expected. Please check your transaction history.',
        technicalDetails: error.toString(),
        transactionId: transactionId,
        isRetryable: true,
      );
    }
    
    // Payment gateway errors
    if (errorString.contains('payment') && errorString.contains('failed')) {
      return PayUError(
        type: PayUErrorType.payment,
        code: 'PAYMENT_FAILED',
        message: 'Payment processing failed',
        userMessage: 'Payment could not be processed. Please try again or use a different payment method.',
        technicalDetails: error.toString(),
        transactionId: transactionId,
        isRetryable: true,
      );
    }
    
    // User cancellation
    if (errorString.contains('cancel') || errorString.contains('abort')) {
      return PayUError(
        type: PayUErrorType.userCancelled,
        code: 'USER_CANCELLED',
        message: 'Payment cancelled by user',
        userMessage: 'Payment was cancelled.',
        technicalDetails: error.toString(),
        transactionId: transactionId,
        isRetryable: false,
      );
    }
    
    // Generic transaction error
    return PayUError(
      type: PayUErrorType.transaction,
      code: 'TRANSACTION_ERROR',
      message: 'Transaction processing error',
      userMessage: 'An error occurred while processing your payment. Please try again.',
      technicalDetails: error.toString(),
      transactionId: transactionId,
      isRetryable: true,
    );
  }

  /// Handle API communication errors
  static PayUError handleApiError(dynamic error, String? transactionId) {
    debugPrint('$_logPrefix API Error: $error (TxnID: $transactionId)');
    
    if (error is ApiException) {
      return _handleApiException(error, transactionId);
    }
    
    return PayUError(
      type: PayUErrorType.api,
      code: 'API_ERROR',
      message: 'API communication error',
      userMessage: 'Unable to communicate with payment service. Please try again.',
      technicalDetails: error.toString(),
      transactionId: transactionId,
      isRetryable: true,
    );
  }

  /// Handle backend response errors
  static PayUError handleBackendError(Map<String, dynamic>? response, String? transactionId) {
    debugPrint('$_logPrefix Backend Error: $response (TxnID: $transactionId)');
    
    if (response == null) {
      return PayUError(
        type: PayUErrorType.backend,
        code: 'NULL_RESPONSE',
        message: 'No response from backend',
        userMessage: 'Payment service is not responding. Please try again later.',
        transactionId: transactionId,
        isRetryable: true,
      );
    }
    
    final errorCode = response['error'] ?? 'UNKNOWN_ERROR';
    final errorMessage = response['message'] ?? 'Unknown backend error';
    
    return PayUError(
      type: PayUErrorType.backend,
      code: errorCode,
      message: errorMessage,
      userMessage: _getUserFriendlyMessage(errorCode, errorMessage),
      technicalDetails: response.toString(),
      transactionId: transactionId,
      isRetryable: _isRetryableError(errorCode),
    );
  }

  /// Handle database-related errors
  static PayUError handleDatabaseError(dynamic error, String? transactionId) {
    debugPrint('$_logPrefix Database Error: $error (TxnID: $transactionId)');
    
    return PayUError(
      type: PayUErrorType.database,
      code: 'DATABASE_ERROR',
      message: 'Database operation failed',
      userMessage: 'Unable to save transaction details. Please contact support if payment was deducted.',
      technicalDetails: error.toString(),
      transactionId: transactionId,
      isRetryable: false,
    );
  }

  /// Handle validation errors
  static PayUError handleValidationError(String field, String? value, String? transactionId) {
    debugPrint('$_logPrefix Validation Error: $field = $value (TxnID: $transactionId)');
    
    return PayUError(
      type: PayUErrorType.validation,
      code: 'VALIDATION_ERROR',
      message: 'Invalid $field: $value',
      userMessage: 'Please check your payment details and try again.',
      technicalDetails: 'Field: $field, Value: $value',
      transactionId: transactionId,
      isRetryable: false,
    );
  }

  /// Handle unknown/unexpected errors
  static PayUError handleUnknownError(dynamic error, String? transactionId) {
    debugPrint('$_logPrefix Unknown Error: $error (TxnID: $transactionId)');
    
    return PayUError(
      type: PayUErrorType.unknown,
      code: 'UNKNOWN_ERROR',
      message: 'Unexpected error occurred',
      userMessage: 'An unexpected error occurred. Please try again or contact support.',
      technicalDetails: error.toString(),
      transactionId: transactionId,
      isRetryable: true,
    );
  }

  /// Handle ApiException specifically
  static PayUError _handleApiException(ApiException apiError, String? transactionId) {
    switch (apiError.code) {
      case 'CONNECTION_TIMEOUT':
        return PayUError(
          type: PayUErrorType.network,
          code: apiError.code ?? 'CONNECTION_TIMEOUT',
          message: apiError.message,
          userMessage: 'Connection timed out. Please check your internet connection.',
          transactionId: transactionId,
          isRetryable: true,
        );
        
      case 'UNAUTHORIZED':
        return PayUError(
          type: PayUErrorType.authentication,
          code: apiError.code ?? 'UNAUTHORIZED',
          message: apiError.message,
          userMessage: 'Authentication failed. Please log in again.',
          transactionId: transactionId,
          isRetryable: false,
        );

      case 'INVALID_PAYMENT_DATA':
        return PayUError(
          type: PayUErrorType.validation,
          code: apiError.code ?? 'INVALID_PAYMENT_DATA',
          message: apiError.message,
          userMessage: 'Invalid payment information. Please check your details.',
          transactionId: transactionId,
          isRetryable: false,
        );

      default:
        return PayUError(
          type: PayUErrorType.api,
          code: apiError.code ?? 'API_ERROR',
          message: apiError.message,
          userMessage: 'Payment service error. Please try again.',
          transactionId: transactionId,
          isRetryable: true,
        );
    }
  }

  /// Get user-friendly message for error codes
  static String _getUserFriendlyMessage(String errorCode, String originalMessage) {
    switch (errorCode.toUpperCase()) {
      case 'TRANSACTION_NOT_FOUND':
        return 'Transaction not found. Please check your transaction history.';
      case 'HASH_VERIFICATION_FAILED':
        return 'Payment verification failed. Please contact support.';
      case 'DATABASE_UPDATE_FAILED':
        return 'Unable to update transaction status. Please contact support.';
      case 'INSUFFICIENT_BALANCE':
        return 'Insufficient balance in your account.';
      case 'INVALID_AMOUNT':
        return 'Invalid payment amount. Please check and try again.';
      case 'PAYMENT_DECLINED':
        return 'Payment was declined by your bank. Please try a different payment method.';
      case 'CARD_EXPIRED':
        return 'Your card has expired. Please use a different card.';
      case 'INVALID_CARD':
        return 'Invalid card details. Please check your card information.';
      default:
        return originalMessage.isNotEmpty ? originalMessage : 'An error occurred. Please try again.';
    }
  }

  /// Check if error is retryable
  static bool _isRetryableError(String errorCode) {
    const nonRetryableErrors = [
      'HASH_VERIFICATION_FAILED',
      'INVALID_PAYMENT_DATA',
      'UNAUTHORIZED',
      'CARD_EXPIRED',
      'INVALID_CARD',
      'INSUFFICIENT_BALANCE',
    ];
    
    return !nonRetryableErrors.contains(errorCode.toUpperCase());
  }
}

/// PayU error types for categorization
enum PayUErrorType {
  configuration,
  network,
  sdk,
  payment,
  transaction,
  api,
  backend,
  database,
  validation,
  authentication,
  userCancelled,
  unknown,
}

/// Comprehensive PayU error class with detailed information
class PayUError {
  final PayUErrorType type;
  final String code;
  final String message;
  final String userMessage;
  final String? technicalDetails;
  final String? transactionId;
  final bool isRetryable;
  final DateTime timestamp;
  final Map<String, dynamic>? additionalData;

  PayUError({
    required this.type,
    required this.code,
    required this.message,
    required this.userMessage,
    this.technicalDetails,
    this.transactionId,
    this.isRetryable = false,
    this.additionalData,
  }) : timestamp = DateTime.now();

  /// Convert error to JSON for logging
  Map<String, dynamic> toJson() {
    return {
      'type': type.toString(),
      'code': code,
      'message': message,
      'user_message': userMessage,
      'technical_details': technicalDetails,
      'transaction_id': transactionId,
      'is_retryable': isRetryable,
      'timestamp': timestamp.toIso8601String(),
      'additional_data': additionalData,
    };
  }

  /// Create error from JSON (for persistence/logging)
  factory PayUError.fromJson(Map<String, dynamic> json) {
    return PayUError(
      type: PayUErrorType.values.firstWhere(
        (e) => e.toString() == json['type'],
        orElse: () => PayUErrorType.unknown,
      ),
      code: json['code'] ?? 'UNKNOWN',
      message: json['message'] ?? 'Unknown error',
      userMessage: json['user_message'] ?? 'An error occurred',
      technicalDetails: json['technical_details'],
      transactionId: json['transaction_id'],
      isRetryable: json['is_retryable'] ?? false,
      additionalData: json['additional_data'],
    );
  }

  /// Get severity level for logging
  String get severity {
    switch (type) {
      case PayUErrorType.configuration:
      case PayUErrorType.database:
        return 'CRITICAL';
      case PayUErrorType.payment:
      case PayUErrorType.transaction:
      case PayUErrorType.backend:
        return 'HIGH';
      case PayUErrorType.network:
      case PayUErrorType.api:
        return 'MEDIUM';
      case PayUErrorType.validation:
      case PayUErrorType.userCancelled:
        return 'LOW';
      default:
        return 'MEDIUM';
    }
  }

  /// Get color for UI display
  String get displayColor {
    switch (type) {
      case PayUErrorType.userCancelled:
        return 'orange';
      case PayUErrorType.validation:
        return 'yellow';
      case PayUErrorType.network:
        return 'blue';
      default:
        return 'red';
    }
  }

  @override
  String toString() {
    return 'PayUError(type: $type, code: $code, message: $message, txnId: $transactionId)';
  }
}

/// PayU error recovery suggestions
class PayUErrorRecovery {
  static List<String> getRecoverySteps(PayUError error) {
    switch (error.type) {
      case PayUErrorType.network:
        return [
          'Check your internet connection',
          'Try switching between WiFi and mobile data',
          'Wait a moment and try again',
          'Restart the app if problem persists',
        ];

      case PayUErrorType.payment:
        return [
          'Check your card details',
          'Ensure sufficient balance',
          'Try a different payment method',
          'Contact your bank if card is blocked',
        ];

      case PayUErrorType.validation:
        return [
          'Check all required fields are filled',
          'Verify payment amount is correct',
          'Ensure card details are valid',
          'Try again with correct information',
        ];

      case PayUErrorType.authentication:
        return [
          'Log out and log in again',
          'Clear app cache',
          'Update the app to latest version',
          'Contact support if problem persists',
        ];

      case PayUErrorType.userCancelled:
        return [
          'Try the payment again',
          'Complete the payment process',
          'Don\'t close the payment screen',
        ];

      default:
        return [
          'Try again after a few minutes',
          'Restart the app',
          'Check your internet connection',
          'Contact support if problem continues',
        ];
    }
  }
}
