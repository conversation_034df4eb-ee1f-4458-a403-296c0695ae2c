import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:flutter/foundation.dart';

class TokenService {
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true, // Recommended for Android
    ),
  );
  static const String _tokenKey = 'auth_token';
  static const String _userIdKey = 'user_id'; // If you also store user ID

  Future<void> saveToken(String token) async {
    try {
      await _secureStorage.write(key: _tokenKey, value: token);
      if (kDebugMode) {
        debugPrint('Token saved securely.');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error saving token securely: $e');
      }
      // Optionally rethrow or handle as a critical error
    }
  }

  Future<String?> getToken() async {
    try {
      final token = await _secureStorage.read(key: _tokenKey);
      if (kDebugMode && token != null) {
        // Avoid printing the token itself, even in debug mode for best practice
        debugPrint('Token retrieved securely.');
      }
      return token;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error retrieving token securely: $e');
      }
      return null;
    }
  }

  Future<void> clearToken() async {
    try {
      await _secureStorage.delete(key: _tokenKey);
      await _secureStorage.delete(key: _userIdKey); // Also clear user ID if stored
      if (kDebugMode) {
        debugPrint('Token cleared securely.');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error clearing token securely: $e');
      }
    }
  }

  Future<bool> isLoggedIn() async {
    final token = await getToken();
    return token != null && token.isNotEmpty;
  }

  // You can add methods for saving/retrieving user ID or other sensitive info here
  Future<void> saveUserId(String userId) async {
    await _secureStorage.write(key: _userIdKey, value: userId);
  }

  Future<String?> getUserId() async {
    return await _secureStorage.read(key: _userIdKey);
  }
}