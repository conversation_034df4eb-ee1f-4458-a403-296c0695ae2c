import 'dart:async';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Service for preloading screen images to ensure smooth transitions
/// Handles asset image preloading and caching for optimal performance
class ScreenImagePreloader {
  // Cache for preloaded images
  final Map<String, ImageProvider> _imageProviderCache = {};
  final Map<String, ui.Image> _imageCache = {};
  final Map<String, bool> _preloadStatus = {};

  bool _isInitialized = false;

  /// Initialize the image preloader service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint('🖼️ Initializing ScreenImagePreloader service');
      _isInitialized = true;
      debugPrint('✅ ScreenImagePreloader service initialized successfully');
    } catch (e) {
      debugPrint('❌ Error initializing ScreenImagePreloader: $e');
      rethrow;
    }
  }

  /// Preload charging session screen assets
  /// Returns true if all assets were preloaded successfully
  Future<bool> preloadChargingSessionAssets(BuildContext context) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      debugPrint('🔄 Preloading charging session assets');

      // List of assets to preload for charging session screen
      final assetsToPreload = [
        'assets/images/charging_session _screen_background.png',
      ];

      bool allSuccessful = true;

      for (final assetPath in assetsToPreload) {
        try {
          debugPrint('📥 Preloading asset: $assetPath');

          // Preload the asset image
          final success = await _preloadAssetImage(context, assetPath);

          if (success) {
            debugPrint('✅ Successfully preloaded: $assetPath');
            _preloadStatus[assetPath] = true;
          } else {
            debugPrint('⚠️ Failed to preload: $assetPath');
            _preloadStatus[assetPath] = false;
            allSuccessful = false;
          }
        } catch (e) {
          debugPrint('❌ Error preloading $assetPath: $e');
          _preloadStatus[assetPath] = false;
          allSuccessful = false;
        }
      }

      if (allSuccessful) {
        debugPrint('✅ All charging session assets preloaded successfully');
      } else {
        debugPrint('⚠️ Some charging session assets failed to preload');
      }

      return allSuccessful;
    } catch (e) {
      debugPrint('❌ Error in preloadChargingSessionAssets: $e');
      return false;
    }
  }

  /// Preload a single asset image
  Future<bool> _preloadAssetImage(BuildContext context, String assetPath) async {
    try {
      // Create AssetImage provider
      final imageProvider = AssetImage(assetPath);

      // Cache the image provider
      _imageProviderCache[assetPath] = imageProvider;

      // Use precacheImage to load the image into Flutter's image cache
      await precacheImage(imageProvider, context);

      // Also load the raw image data for additional caching
      final imageStream = imageProvider.resolve(ImageConfiguration.empty);
      final completer = Completer<bool>();

      late ImageStreamListener listener;
      listener = ImageStreamListener(
        (ImageInfo info, bool synchronousCall) {
          // Cache the ui.Image
          _imageCache[assetPath] = info.image;
          imageStream.removeListener(listener);
          completer.complete(true);
        },
        onError: (dynamic exception, StackTrace? stackTrace) {
          imageStream.removeListener(listener);
          debugPrint('❌ Error loading image $assetPath: $exception');
          completer.complete(false);
        },
      );

      imageStream.addListener(listener);

      // Wait for the image to load with timeout
      return await completer.future.timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          imageStream.removeListener(listener);
          debugPrint('⏰ Timeout loading image: $assetPath');
          return false;
        },
      );
    } catch (e) {
      debugPrint('❌ Error preloading asset image $assetPath: $e');
      return false;
    }
  }

  /// Get cached image provider for an asset
  /// Returns the cached provider if available, null otherwise
  ImageProvider? getImageProvider(String assetPath) {
    if (_imageProviderCache.containsKey(assetPath)) {
      debugPrint('📋 Using cached image provider for: $assetPath');
      return _imageProviderCache[assetPath];
    }

    debugPrint('⚠️ No cached image provider found for: $assetPath');
    return null;
  }

  /// Get cached ui.Image for an asset
  /// Returns the cached image if available, null otherwise
  ui.Image? getCachedImage(String assetPath) {
    if (_imageCache.containsKey(assetPath)) {
      debugPrint('📋 Using cached ui.Image for: $assetPath');
      return _imageCache[assetPath];
    }

    debugPrint('⚠️ No cached ui.Image found for: $assetPath');
    return null;
  }

  /// Check if an asset has been preloaded successfully
  bool isAssetPreloaded(String assetPath) {
    return _preloadStatus[assetPath] == true;
  }

  /// Get preload status for all assets
  Map<String, bool> getPreloadStatus() {
    return Map.from(_preloadStatus);
  }

  /// Preload a custom list of assets
  Future<bool> preloadCustomAssets(BuildContext context, List<String> assetPaths) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      debugPrint('🔄 Preloading ${assetPaths.length} custom assets');

      bool allSuccessful = true;

      for (final assetPath in assetPaths) {
        try {
          final success = await _preloadAssetImage(context, assetPath);

          if (success) {
            _preloadStatus[assetPath] = true;
          } else {
            _preloadStatus[assetPath] = false;
            allSuccessful = false;
          }
        } catch (e) {
          debugPrint('❌ Error preloading custom asset $assetPath: $e');
          _preloadStatus[assetPath] = false;
          allSuccessful = false;
        }
      }

      return allSuccessful;
    } catch (e) {
      debugPrint('❌ Error in preloadCustomAssets: $e');
      return false;
    }
  }

  /// Clear all cached images and reset preload status
  void clearCache() {
    debugPrint('🧹 Clearing ScreenImagePreloader cache');
    _imageProviderCache.clear();
    _imageCache.clear();
    _preloadStatus.clear();
    debugPrint('✅ ScreenImagePreloader cache cleared');
  }

  /// Get cache statistics
  Map<String, dynamic> getCacheStats() {
    return {
      'imageProviders': _imageProviderCache.length,
      'images': _imageCache.length,
      'preloadStatus': _preloadStatus.length,
      'successfulPreloads': _preloadStatus.values.where((status) => status).length,
      'failedPreloads': _preloadStatus.values.where((status) => !status).length,
    };
  }

  /// Dispose of the service and clear all caches
  void dispose() {
    debugPrint('🗑️ Disposing ScreenImagePreloader service');
    clearCache();
    _isInitialized = false;
    debugPrint('✅ ScreenImagePreloader service disposed');
  }
}
