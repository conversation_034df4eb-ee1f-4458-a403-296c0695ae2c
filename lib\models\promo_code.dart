import 'package:flutter/foundation.dart';

/// Enhanced data model for promo code with validation and helper methods
@immutable
class PromoCode {
  final int id;
  final String promoType;
  final int credits;
  final String code;
  final String description;
  final String startDate;
  final String endDate;
  final String frequency;
  final int maxLimit;
  final int userId;
  final int status;
  final String createdAt;
  final String updatedAt;
  final int codeUsed;
  final int redeemed;
  final int minimumAmountApplicable;
  final String companyUid;

  const PromoCode({
    required this.id,
    required this.promoType,
    required this.credits,
    required this.code,
    required this.description,
    required this.startDate,
    required this.endDate,
    required this.frequency,
    required this.maxLimit,
    required this.userId,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
    required this.codeUsed,
    required this.redeemed,
    required this.minimumAmountApplicable,
    required this.companyUid,
  });

  /// Create PromoCode from JSON with enhanced error handling
  factory PromoCode.fromJson(Map<String, dynamic> json) {
    try {
      return PromoCode(
        id: _parseIntSafely(json['id']),
        promoType: _parseStringSafely(json['promo_type']),
        credits: _parseIntSafely(json['credits']),
        code: _parseStringSafely(json['code']),
        description: _parseStringSafely(json['description']),
        startDate: _parseStringSafely(json['start_date']),
        endDate: _parseStringSafely(json['end_date']),
        frequency: _parseStringSafely(json['frequency']),
        maxLimit: _parseIntSafely(json['max_limit']),
        userId: _parseIntSafely(json['user_id']),
        status: _parseIntSafely(json['status']),
        createdAt: _parseStringSafely(json['created_at']),
        updatedAt: _parseStringSafely(json['updated_at']),
        codeUsed: _parseIntSafely(json['code_used']),
        redeemed: _parseIntSafely(json['redeemed']),
        minimumAmountApplicable: _parseIntSafely(json['minimum_amount_applicable']),
        companyUid: _parseStringSafely(json['company_uid']),
      );
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error parsing PromoCode from JSON: $e');
        debugPrint('JSON data: $json');
      }
      rethrow;
    }
  }

  /// Convert PromoCode to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'promo_type': promoType,
      'credits': credits,
      'code': code,
      'description': description,
      'start_date': startDate,
      'end_date': endDate,
      'frequency': frequency,
      'max_limit': maxLimit,
      'user_id': userId,
      'status': status,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'code_used': codeUsed,
      'redeemed': redeemed,
      'minimum_amount_applicable': minimumAmountApplicable,
      'company_uid': companyUid,
    };
  }

  /// Check if promo code is currently active
  /// As per user preference, show ALL promo codes regardless of expiry/status
  bool get isActive => true;

  /// Check if promo code is valid for the given amount
  bool isValidForAmount(double amount) {
    return amount >= minimumAmountApplicable;
  }

  /// Get formatted expiry date
  String get formattedExpiryDate {
    if (endDate.isEmpty) return '';
    try {
      final date = DateTime.parse(endDate);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return endDate;
    }
  }

  /// Check if promo code is expired (for display purposes only)
  bool get isExpired {
    if (endDate.isEmpty) return false;
    try {
      final expiryDate = DateTime.parse(endDate);
      return DateTime.now().isAfter(expiryDate);
    } catch (e) {
      return false;
    }
  }

  /// Get savings amount as formatted string
  String get savingsText => '₹$credits';

  /// Get minimum amount text
  String get minimumAmountText {
    if (minimumAmountApplicable <= 0) return '';
    return 'Min. ₹$minimumAmountApplicable';
  }

  /// Check if promo code matches search query
  bool matchesSearch(String query) {
    if (query.isEmpty) return true;
    final lowerQuery = query.toLowerCase();
    return code.toLowerCase().contains(lowerQuery) ||
           description.toLowerCase().contains(lowerQuery);
  }

  /// Create a copy with updated fields
  PromoCode copyWith({
    int? id,
    String? promoType,
    int? credits,
    String? code,
    String? description,
    String? startDate,
    String? endDate,
    String? frequency,
    int? maxLimit,
    int? userId,
    int? status,
    String? createdAt,
    String? updatedAt,
    int? codeUsed,
    int? redeemed,
    int? minimumAmountApplicable,
    String? companyUid,
  }) {
    return PromoCode(
      id: id ?? this.id,
      promoType: promoType ?? this.promoType,
      credits: credits ?? this.credits,
      code: code ?? this.code,
      description: description ?? this.description,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      frequency: frequency ?? this.frequency,
      maxLimit: maxLimit ?? this.maxLimit,
      userId: userId ?? this.userId,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      codeUsed: codeUsed ?? this.codeUsed,
      redeemed: redeemed ?? this.redeemed,
      minimumAmountApplicable: minimumAmountApplicable ?? this.minimumAmountApplicable,
      companyUid: companyUid ?? this.companyUid,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PromoCode && other.id == id && other.code == code;
  }

  @override
  int get hashCode => Object.hash(id, code);

  @override
  String toString() {
    return 'PromoCode(id: $id, code: $code, credits: $credits, description: $description)';
  }

  /// Helper method to safely parse integers from JSON
  static int _parseIntSafely(dynamic value) {
    if (value == null) return 0;
    if (value is int) return value;
    if (value is String) {
      return int.tryParse(value) ?? 0;
    }
    if (value is double) return value.toInt();
    return 0;
  }

  /// Helper method to safely parse strings from JSON
  static String _parseStringSafely(dynamic value) {
    if (value == null) return '';
    return value.toString();
  }
}

/// Model for promo code verification response
@immutable
class PromoCodeVerificationResponse {
  final bool success;
  final String message;
  final PromoCodeVerificationData? data;

  const PromoCodeVerificationResponse({
    required this.success,
    required this.message,
    this.data,
  });

  factory PromoCodeVerificationResponse.fromJson(Map<String, dynamic> json) {
    return PromoCodeVerificationResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: json['data'] != null 
          ? PromoCodeVerificationData.fromJson(json['data'])
          : null,
    );
  }
}

/// Model for verified promo code data
@immutable
class PromoCodeVerificationData {
  final String code;
  final int credits;
  final int minimumAmountApplicable;
  final String description;

  const PromoCodeVerificationData({
    required this.code,
    required this.credits,
    required this.minimumAmountApplicable,
    required this.description,
  });

  factory PromoCodeVerificationData.fromJson(Map<String, dynamic> json) {
    return PromoCodeVerificationData(
      code: json['code'] ?? '',
      credits: PromoCode._parseIntSafely(json['credits']),
      minimumAmountApplicable: PromoCode._parseIntSafely(json['minimum_amount_applicable']),
      description: json['description'] ?? '',
    );
  }
}

/// Model for promo code API response
@immutable
class PromoCodeResponse {
  final bool success;
  final String message;
  final List<PromoCode> data;

  const PromoCodeResponse({
    required this.success,
    required this.message,
    required this.data,
  });

  factory PromoCodeResponse.fromJson(Map<String, dynamic> json) {
    final List<dynamic> dataList = json['data'] ?? [];
    final promoCodes = dataList
        .map((item) => PromoCode.fromJson(item as Map<String, dynamic>))
        .toList();

    return PromoCodeResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: promoCodes,
    );
  }
}
