import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart'; // Import Riverpod
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:ecoplug/firebase_options.dart';
import 'package:ecoplug/services/fcm_service.dart';
import 'package:ecoplug/services/fcm_subscription_service.dart';
import 'package:ecoplug/services/messaging_service.dart';
import 'package:ecoplug/services/unified_notification_service.dart';
import 'package:ecoplug/services/auth_notification_service.dart';
import 'package:ecoplug/services/charging_session_notification_manager.dart';
import 'package:ecoplug/services/local_notification_manager.dart';
import 'package:ecoplug/services/persistent_charging_service.dart';
import 'package:ecoplug/services/deep_link_service.dart';
import 'package:ecoplug/screens/auth/auth_screen.dart';
import 'package:ecoplug/screens/splash/splash_screen.dart';
import 'package:ecoplug/widgets/navigation_bar.dart';
import 'package:ecoplug/screens/Trip/trip_page.dart';
import 'package:ecoplug/screens/Profile/Profilescreen/profile_screen_riverpod.dart';
import 'screens/station/station_list_page.dart';
import 'screens/station/station_details_page_fixed.dart';
import 'screens/wallet/wallet_screen.dart';
import 'screens/charging/charging_history_page.dart';
import 'screens/charging/active_sessions_screen.dart';
import 'screens/charging_session_screen.dart';
import 'screens/error/connectivity_error_page.dart';
import 'services/notification_navigation_service.dart';
import 'services/global_connectivity_monitor.dart';
import 'utils/app_themes.dart';
import 'utils/notification_test_utility.dart'; // Import notification test utility
import 'providers/provider_observer.dart'; // Import the provider observer
import 'providers/providers.dart'; // Import all providers
import 'utils/route_observer.dart'; // Import route observer
import 'debug/fcm_debug_widget.dart'; // Import FCM debug widget

// Background message handler for FCM following tutorial pattern
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  // Initialize Firebase for background handler
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  debugPrint('🔔 ===== FCM BACKGROUND MESSAGE RECEIVED =====');
  debugPrint('🔔 Message ID: ${message.messageId}');
  debugPrint('🔔 From: ${message.from}');
  debugPrint('🔔 Data: ${message.data}');

  if (message.notification != null) {
    debugPrint('🔔 Title: ${message.notification!.title}');
    debugPrint('🔔 Body: ${message.notification!.body}');
  }

  // Handle background message based on type
  if (message.data.containsKey('type')) {
    switch (message.data['type']) {
      case 'charging':
        debugPrint('🔔 Background charging notification received');
        // Handle charging notification in background
        break;
      case 'station':
        debugPrint('🔔 Background station notification received');
        // Handle station notification in background
        break;
      default:
        debugPrint(
            '🔔 Unknown background notification type: ${message.data['type']}');
    }
  }
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase first (required for other services)
  try {
    final stopwatch = Stopwatch()..start();
    debugPrint('🚀 ===== STARTING PARALLEL SERVICE INITIALIZATION =====');

    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    debugPrint('✅ Firebase initialized successfully');

    // Set up background message handler
    FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);

    // PERFORMANCE OPTIMIZATION: Initialize services in parallel groups
    // Group 1: Core notification services (can run in parallel)
    final coreNotificationFutures = [
      () async {
        final localNotificationManager = LocalNotificationManager();
        await localNotificationManager.initialize();
        debugPrint('✅ Local notification manager initialized successfully');
      }(),
      () async {
        final notificationService = UnifiedNotificationService();
        await notificationService.initialize();
        debugPrint('✅ Notification services initialized successfully');
      }(),
      () async {
        final authNotificationService = AuthNotificationService();
        await authNotificationService.initialize();
        debugPrint('✅ Auth notification service initialized successfully');
      }(),
      () async {
        final chargingNotificationManager =
            ChargingSessionNotificationManager();
        await chargingNotificationManager.initialize();
        debugPrint(
            '✅ Charging session notification manager initialized successfully');
      }(),
    ];

    // Group 2: FCM and messaging services (can run in parallel)
    final fcmServicesFutures = [
      () async {
        final fcmService = FCMService();
        await fcmService.initialize();
        debugPrint('✅ FCM service initialized successfully');
      }(),
      () async {
        final fcmSubscriptionService = FCMSubscriptionService();
        await fcmSubscriptionService.initialize();
        debugPrint('✅ FCM subscription service initialized successfully');
      }(),
      () async {
        final messagingService = MessagingService();
        await messagingService.init();
        debugPrint('✅ Messaging service initialized successfully');
      }(),
    ];

    // Group 3: Background and utility services (can run in parallel)
    final backgroundServicesFutures = [
      () async {
        final persistentChargingService = PersistentChargingService();
        await persistentChargingService.initialize();
        debugPrint('✅ Persistent charging service initialized successfully');
      }(),
      () async {
        final deepLinkService = DeepLinkService();
        await deepLinkService.initialize(
          onOpenChargingSession: () {
            debugPrint(
                '🔗 Deep link triggered: navigating to charging session');
            // Navigation will be handled by the app's routing system
          },
          onDeepLinkReceived: (data) {
            debugPrint('🔗 Deep link received: $data');
            // Handle deep link data as needed
          },
        );
        debugPrint('✅ Deep link service initialized successfully');
      }(),
      () async {
        debugPrint('🔔 Initializing notification permissions...');
        await NotificationTestUtility.initializePermissionsOnly();
        debugPrint('✅ Notification permissions initialized successfully');
      }(),
    ];

    // Execute all service groups in parallel for maximum performance
    await Future.wait([
      Future.wait(coreNotificationFutures),
      Future.wait(fcmServicesFutures),
      Future.wait(backgroundServicesFutures),
    ]);

    stopwatch.stop();
    debugPrint(
        '🚀 ===== ALL SERVICES INITIALIZED IN ${stopwatch.elapsedMilliseconds}ms =====');
  } catch (e) {
    debugPrint('❌ Error initializing Firebase/Notifications: $e');
  }

  // Fix green line issues by ensuring proper rendering
  // Disable layout bounds visual debug rendering
  // Remove when issue is resolved
  debugDisableShadows = false;

  // Google Maps API key is configured in AndroidManifest.xml and AppDelegate.swift

  runApp(
    ProviderScope(
      // Wrap the app with ProviderScope
      observers: [LoggingProviderObserver()], // Add provider observer
      child: MyApp(),
    ),
  );
} // <-- Ensure main() properly closes here

class MyApp extends ConsumerWidget {
  // Change to ConsumerWidget
  const MyApp({super.key}); // Remove apiBridge parameter

  // Global navigator key for notification navigation
  static final GlobalKey<NavigatorState> navigatorKey =
      GlobalKey<NavigatorState>();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Add WidgetRef ref
    // Access themeMode using Riverpod
    final themeMode = ref.watch(themeNotifierProvider);

    // Initialize notification navigation service
    final navigationService = NotificationNavigationService();
    navigationService.initialize(navigatorKey);

    // Initialize global connectivity monitor
    GlobalConnectivityMonitor.initialize(navigatorKey);

    return MaterialApp(
      title: 'Ecoplug',
      debugShowCheckedModeBanner: false,
      theme: AppThemes.lightTheme,
      darkTheme: AppThemes.darkTheme,
      themeMode: themeMode, // Use Riverpod themeMode here
      navigatorKey:
          navigatorKey, // Add navigator key for notification navigation
      navigatorObservers: [routeObserver], // Add route observer
      initialRoute: '/',
      routes: {
        '/': (context) => const SplashScreen(),
        '/auth': (context) => const AuthScreen(),
        '/dashboard': (context) => const MainNavigation(),
        '/stationList': (context) =>
            const StationListPage(), // This will be replaced by direct navigation
        '/wallet': (context) => const WalletPage(),
        '/trip': (context) => const TripPage(),
        '/profile': (context) => const ProfileScreenRiverpod(),
        '/charging-history': (context) => const ChargingHistoryPage(),
        '/active-sessions': (context) => const ActiveSessionsScreen(),
        '/fcm-debug': (context) => const FCMDebugWidget(), // FCM debug route
        '/connectivity-error': (context) {
          // Handle connectivity error route with optional arguments
          final args = ModalRoute.of(context)?.settings.arguments
              as Map<String, dynamic>?;
          return ConnectivityErrorPage(
            errorMessage: args?['error_message'] as String?,
          );
        },
        '/charging_session': (context) {
          // Handle charging session route with arguments
          final args = ModalRoute.of(context)?.settings.arguments
              as Map<String, dynamic>?;
          return ChargingSessionScreen(
            stationUid: args?['station_uid'] as String?,
            connectorId: args?['connector_id'] as String?,
            initialCharge: args?['charge_percentage'] as double? ?? 0.0,
            verifiedSessionData:
                args?['verified_session_data'] as Map<String, dynamic>?,
          );
        },
      },
      // Use onGenerateRoute for routes that need parameters
      onGenerateRoute: (settings) {
        if (settings.name == '/stationDetails') {
          // Extract the arguments
          final args = settings.arguments;

          if (args is Map<String, dynamic>) {
            // Check if we have a UID directly
            if (args.containsKey('uid') && args['uid'] is String) {
              final String uid = args['uid'] as String;
              if (uid.isNotEmpty) {
                return MaterialPageRoute(
                  builder: (context) {
                    if (uid.isEmpty) {
                      throw ArgumentError(
                          'UID cannot be empty when launching StationDetailsPage');
                    }
                    return StationDetailsPage(uid: uid);
                  },
                );
              }
            }

            // Extract UID from arguments directly
            // Extract UID from arguments
            final String uid = args['uid'] as String? ?? '';
            if (uid.isNotEmpty) {
              return MaterialPageRoute(
                builder: (context) {
                  return StationDetailsPage(uid: uid);
                },
              );
            } else {
              // Handle missing UID
              return MaterialPageRoute(
                builder: (context) => const Scaffold(
                  body: Center(
                    child: Text('Invalid station information'),
                  ),
                ),
              );
            }
          }
        }
        return null;
      },
    );
  }
}
