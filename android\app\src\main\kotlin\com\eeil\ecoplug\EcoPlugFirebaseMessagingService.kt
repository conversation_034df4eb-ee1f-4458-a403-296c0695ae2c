package com.eeil.ecoplug

import android.app.NotificationManager
import android.util.Log
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import java.util.concurrent.atomic.AtomicInteger

/**
 * EcoPlug Firebase Cloud Messaging Service
 *
 * Handles incoming FCM notifications and displays them using the same
 * notification builder logic as the charging notifications to maintain
 * consistent branding and user experience.
 *
 * Features:
 * - Uses official EcoPlug app icons for proper branding
 * - Respects charging_session_custom channel when available
 * - Falls back to ecoplug_fcm channel for general notifications
 * - Maintains consistent notification styling across the app
 */
class EcoPlugFirebaseMessagingService : FirebaseMessagingService() {

    companion object {
        private const val TAG = "EcoPlugFCMService"
        private val notificationIdCounter = AtomicInteger(2000) // Start from 2000 to avoid conflicts
    }

    private lateinit var notificationHelper: NotificationHelper

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "🔔 EcoPlug FCM Service created")

        // Initialize notification helper for consistent notification building
        notificationHelper = NotificationHelper(this)
    }

    /**
     * Called when a new FCM token is generated
     * This happens when the app is first installed, restored, or when the token is refreshed
     */
    override fun onNewToken(token: String) {
        super.onNewToken(token)
        Log.d(TAG, "🔔 New FCM token generated: ${token.take(20)}...")

        // TODO: Send the token to your server to enable targeted notifications
        // This would typically involve calling your backend API to register the token
        sendTokenToServer(token)
    }

    /**
     * Called when a message is received from FCM
     * Builds and displays a notification using the same styling as charging notifications
     */
    override fun onMessageReceived(remoteMessage: RemoteMessage) {
        super.onMessageReceived(remoteMessage)

        Log.d(TAG, "🔔 ===== FCM MESSAGE RECEIVED =====")
        Log.d(TAG, "🔔 Message ID: ${remoteMessage.messageId}")
        Log.d(TAG, "🔔 From: ${remoteMessage.from}")
        Log.d(TAG, "🔔 Notification Title: ${remoteMessage.notification?.title}")
        Log.d(TAG, "🔔 Notification Body: ${remoteMessage.notification?.body}")
        Log.d(TAG, "🔔 Data Payload: ${remoteMessage.data}")
        Log.d(TAG, "🔔 Timestamp: ${System.currentTimeMillis()}")

        // Validate charging data
        validateChargingData(remoteMessage.data)

        // Extract notification data
        val title = remoteMessage.notification?.title
            ?: remoteMessage.data["title"]
            ?: "EcoPlug"

        val message = remoteMessage.notification?.body
            ?: remoteMessage.data["message"]
            ?: remoteMessage.data["body"]
            ?: "You have a new notification"

        // Check if this is a charging-related notification
        val isChargingNotification = remoteMessage.data["type"] == "charging"
            || remoteMessage.data["category"] == "charging"
            || remoteMessage.data["action"] == "charging_update"
            || title.contains("charging", ignoreCase = true)
            || message.contains("charging", ignoreCase = true)
            || remoteMessage.data.containsKey("soc")
            || remoteMessage.data.containsKey("charge_percentage")

        Log.d(TAG, "🔔 Building notification: title='$title', message='$message', isCharging=$isChargingNotification")

        // For charging notifications, use fixed ID to replace existing notifications
        val notificationId = if (isChargingNotification) {
            1001 // Fixed ID for charging notifications to prevent spam
        } else {
            generateNotificationId(remoteMessage)
        }

        // Extract charging data for enhanced notification content
        val enhancedMessage = if (isChargingNotification) {
            buildChargingNotificationMessage(remoteMessage.data, message)
        } else {
            message
        }

        // Build notification using helper to maintain consistency
        val notification = notificationHelper.buildNotification(
            channelId = remoteMessage.data["channel_id"], // Allow server to specify channel
            title = title,
            message = enhancedMessage,
            useChargingChannel = isChargingNotification
        )

        // Show the notification
        notificationHelper.showNotification(notificationId, notification)

        Log.d(TAG, "✅ FCM notification displayed successfully with ID: $notificationId")

        // Handle any additional data payload actions
        handleDataPayload(remoteMessage.data)
    }

    /**
     * Generate a unique notification ID based on the message content
     * This allows for replacing notifications with the same ID if needed
     */
    private fun generateNotificationId(remoteMessage: RemoteMessage): Int {
        // Use message ID if available, otherwise generate a unique ID
        return remoteMessage.messageId?.hashCode()
            ?: remoteMessage.data["notification_id"]?.toIntOrNull()
            ?: notificationIdCounter.incrementAndGet()
    }

    /**
     * Handle additional data payload from FCM message
     * This can be used for deep linking, updating app state, etc.
     */
    private fun handleDataPayload(data: Map<String, String>) {
        if (data.isEmpty()) return

        Log.d(TAG, "🔔 Processing FCM data payload: $data")

        try {
            // Handle different types of actions based on data payload
            when (data["action"]) {
                "open_charging_session" -> {
                    Log.d(TAG, "🔔 FCM action: Open charging session")
                    // Could broadcast an intent to open charging session
                }
                "refresh_data" -> {
                    Log.d(TAG, "🔔 FCM action: Refresh app data")
                    // Could trigger data refresh
                }
                "update_settings" -> {
                    Log.d(TAG, "🔔 FCM action: Update app settings")
                    // Could update app configuration
                }
                else -> {
                    Log.d(TAG, "🔔 FCM action: No specific action required")
                }
            }

            // Log any custom data fields for debugging
            data.forEach { (key, value) ->
                if (!key.startsWith("google.") && key !in listOf("title", "message", "body", "action", "type", "category", "channel_id", "notification_id")) {
                    Log.d(TAG, "🔔 Custom FCM data: $key = $value")
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "❌ Error handling FCM data payload: ${e.message}", e)
        }
    }

    /**
     * Validate charging data in FCM message for debugging
     */
    private fun validateChargingData(data: Map<String, String>) {
        Log.d(TAG, "🔍 ===== FCM CHARGING DATA VALIDATION =====")

        val chargingFields = mapOf(
            "soc" to data["soc"],
            "charge_percentage" to data["charge_percentage"],
            "power" to data["power"],
            "current_power" to data["current_power"],
            "energy" to data["energy"],
            "energy_delivered" to data["energy_delivered"],
            "cost" to data["cost"],
            "current_price" to data["current_price"],
            "timer" to data["timer"],
            "charging_timer" to data["charging_timer"],
            "transaction_id" to data["transaction_id"],
            "session_id" to data["session_id"]
        )

        Log.d(TAG, "🔍 Charging Data Fields:")
        chargingFields.forEach { (key, value) ->
            if (!value.isNullOrEmpty()) {
                Log.d(TAG, "🔍   ✅ $key: $value")
            } else {
                Log.d(TAG, "🔍   ❌ $key: NOT_FOUND")
            }
        }

        // Check for real vs default values
        val soc = data["soc"] ?: data["charge_percentage"]
        if (soc != null) {
            if (soc == "0" || soc == "30" || soc.isEmpty()) {
                Log.w(TAG, "⚠️ WARNING: SOC appears to be default/fallback value: $soc")
            } else {
                Log.d(TAG, "✅ Real SOC data detected: $soc")
            }
        } else {
            Log.e(TAG, "❌ CRITICAL: No SOC data found in FCM message")
        }

        Log.d(TAG, "🔍 ===== END VALIDATION =====")
    }

    /**
     * Build enhanced charging notification message with all available data
     */
    private fun buildChargingNotificationMessage(data: Map<String, String>, fallbackMessage: String): String {
        try {
            val soc = data["soc"] ?: data["charge_percentage"] ?: ""
            val power = data["power"] ?: data["current_power"] ?: ""
            val energy = data["energy"] ?: data["energy_delivered"] ?: ""
            val cost = data["cost"] ?: data["current_price"] ?: ""
            val timer = data["timer"] ?: data["charging_timer"] ?: ""

            // Build comprehensive message if we have charging data
            if (soc.isNotEmpty()) {
                val socValue = soc.replace("%", "")
                val parts = mutableListOf<String>()

                parts.add("Battery: $socValue%")
                if (power.isNotEmpty()) parts.add("Power: $power")
                if (energy.isNotEmpty()) parts.add("Energy: $energy")
                if (cost.isNotEmpty()) parts.add("Cost: $cost")
                if (timer.isNotEmpty()) parts.add("Time: $timer")

                return parts.joinToString(" • ")
            }

            return fallbackMessage
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error building charging message: ${e.message}")
            return fallbackMessage
        }
    }

    /**
     * Send the FCM token to your server for targeted notifications
     * This should be implemented based on your backend API
     */
    private fun sendTokenToServer(token: String) {
        try {
            Log.d(TAG, "🔔 Sending FCM token to server...")

            // TODO: Implement actual server communication
            // Example implementation:
            /*
            val apiService = RetrofitClient.getApiService()
            apiService.updateFcmToken(
                UpdateTokenRequest(
                    token = token,
                    deviceId = getDeviceId(),
                    platform = "android"
                )
            ).enqueue(object : Callback<ApiResponse> {
                override fun onResponse(call: Call<ApiResponse>, response: Response<ApiResponse>) {
                    if (response.isSuccessful) {
                        Log.d(TAG, "✅ FCM token sent to server successfully")
                    } else {
                        Log.e(TAG, "❌ Failed to send FCM token: ${response.errorBody()}")
                    }
                }
                override fun onFailure(call: Call<ApiResponse>, t: Throwable) {
                    Log.e(TAG, "❌ Network error sending FCM token: ${t.message}")
                }
            })
            */

            Log.d(TAG, "✅ FCM token ready for server integration")

        } catch (e: Exception) {
            Log.e(TAG, "❌ Error sending FCM token to server: ${e.message}", e)
        }
    }

    /**
     * Called when the FCM service is destroyed
     */
    override fun onDestroy() {
        Log.d(TAG, "🔔 EcoPlug FCM Service destroyed")
        super.onDestroy()
    }
}
