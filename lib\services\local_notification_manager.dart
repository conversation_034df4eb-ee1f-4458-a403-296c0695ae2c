import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:ecoplug/config/notification_config.dart';
import 'package:ecoplug/services/notification_navigation_service.dart';
import 'package:ecoplug/services/notification_history_service.dart';
import 'package:ecoplug/models/notification_history_item.dart';

import '../utils/notification_utils.dart';

/// Local Notification Manager
/// Centralized manager for all local notifications in the EcoPlug app
/// Handles initialization, permissions, channels, and notification display
/// Prevents conflicts between multiple notification services
class LocalNotificationManager {
  static final LocalNotificationManager _instance =
      LocalNotificationManager._internal();
  factory LocalNotificationManager() => _instance;
  LocalNotificationManager._internal();

  final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();
  final NotificationNavigationService _navigationService =
      NotificationNavigationService();
  final NotificationHistoryService _historyService =
      NotificationHistoryService();

  bool _isInitialized = false;
  bool _permissionsGranted = false;
  final Map<String, bool> _channelsCreated = {};

  /// Initialize the local notification manager
  Future<void> initialize() async {
    if (_isInitialized) {
      debugPrint('📱 Local Notification Manager already initialized');
      return;
    }

    try {
      debugPrint('📱 ===== INITIALIZING LOCAL NOTIFICATION MANAGER =====');

      // Step 1: Request notification permissions
      await _requestNotificationPermissions();

      // Step 2: Initialize local notifications plugin
      await _initializeLocalNotifications();

      // Step 3: Create all required notification channels
      await _createAllNotificationChannels();

      // Step 4: Initialize notification history service
      await _historyService.initialize();

      _isInitialized = true;
      debugPrint('✅ Local Notification Manager initialized successfully');
      debugPrint('📱 Permissions granted: $_permissionsGranted');
      debugPrint('📱 Channels created: ${_channelsCreated.keys.toList()}');
    } catch (e) {
      debugPrint('❌ Error initializing Local Notification Manager: $e');
      rethrow;
    }
  }

  /// Request notification permissions for Android using native dialog
  Future<void> _requestNotificationPermissions() async {
    debugPrint('📱 ===== REQUESTING ANDROID NOTIFICATION PERMISSIONS =====');

    try {
      if (Platform.isAndroid) {
        final androidPlugin =
            _localNotifications.resolvePlatformSpecificImplementation<
                AndroidFlutterLocalNotificationsPlugin>();

        if (androidPlugin != null) {
          // First check if notifications are already enabled
          final areNotificationsEnabled =
              await androidPlugin.areNotificationsEnabled() ?? false;
          debugPrint(
              '📱 Current Android notifications status: $areNotificationsEnabled');

          if (areNotificationsEnabled) {
            _permissionsGranted = true;
            debugPrint('✅ Android notification permissions already granted');
            return;
          }

          // Request notification permissions using native Android dialog
          debugPrint('📱 🔔 Requesting Android notification permissions...');
          debugPrint(
              '📱 📲 Native Android permission dialog should appear now');

          final permissionGranted =
              await androidPlugin.requestNotificationsPermission();
          debugPrint('📱 Native permission request result: $permissionGranted');

          _permissionsGranted = permissionGranted ?? false;

          if (_permissionsGranted) {
            debugPrint('✅ ✅ Android notification permissions GRANTED by user');
            debugPrint(
                '📱 🔔 Notifications will now appear in Android notification tray');
          } else {
            debugPrint('❌ ❌ Android notification permissions DENIED by user');
            debugPrint(
                '📱 ⚠️ Notifications will NOT appear in Android notification tray');
            debugPrint(
                '📱 💡 User can enable notifications manually in Android Settings > Apps > EcoPlug > Notifications');
          }

          // Double-check the final status
          final finalStatus =
              await androidPlugin.areNotificationsEnabled() ?? false;
          debugPrint('📱 Final Android notifications status: $finalStatus');

          if (finalStatus != _permissionsGranted) {
            debugPrint(
                '⚠️ Permission status mismatch detected, using actual status: $finalStatus');
            _permissionsGranted = finalStatus;
          }
        } else {
          debugPrint('❌ Android plugin not available');
          _permissionsGranted = false;
        }
      } else {
        // For iOS, use the standard permission request
        debugPrint('📱 Requesting iOS notification permissions...');
        final iosPlugin =
            _localNotifications.resolvePlatformSpecificImplementation<
                IOSFlutterLocalNotificationsPlugin>();

        if (iosPlugin != null) {
          final result = await iosPlugin.requestPermissions(
            alert: true,
            badge: true,
            sound: true,
          );
          _permissionsGranted = result ?? false;
          debugPrint('📱 iOS permission result: $_permissionsGranted');
        }
      }
    } catch (e) {
      debugPrint('❌ Error requesting notification permissions: $e');
      debugPrint('❌ Stack trace: ${StackTrace.current}');
      _permissionsGranted = false;
    }
  }

  /// Initialize local notifications plugin
  Future<void> _initializeLocalNotifications() async {
    debugPrint('📱 Initializing local notifications plugin...');

    // BRANDING: Use EcoPlug launcher icon for consistent branding
    const androidSettings =
        AndroidInitializationSettings('@drawable/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    final initialized = await _localNotifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    if (initialized == true) {
      debugPrint('✅ Local notifications plugin initialized successfully');
    } else {
      debugPrint('❌ Failed to initialize local notifications plugin');
      throw Exception('Local notifications initialization failed');
    }
  }

  /// Create all required notification channels using shared utility
  Future<void> _createAllNotificationChannels() async {
    if (!Platform.isAndroid) {
      debugPrint('📱 Skipping channel creation - not Android platform');
      return;
    }

    debugPrint('📱 Creating notification channels using shared utility...');

    try {
      // Create standard charging and FCM channels using shared utility
      final chargingSuccess =
          await NotificationUtils.createChargingNotificationChannel(
              _localNotifications);

      final fcmSuccess = await NotificationUtils.createFCMNotificationChannel(
          _localNotifications);

      // Track the main channels
      _channelsCreated[NotificationUtils.chargingChannelId] = chargingSuccess;
      _channelsCreated[NotificationUtils.fcmChannelId] = fcmSuccess;

      debugPrint(
          '✅ Standard channels - Charging: ${chargingSuccess ? 'Success' : 'Failed'}, FCM: ${fcmSuccess ? 'Success' : 'Failed'}');

      // Create additional channels from config for backwards compatibility
      final androidPlugin =
          _localNotifications.resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>();

      if (androidPlugin != null) {
        final channelConfigs = NotificationConfig.channels;

        for (final entry in channelConfigs.entries) {
          final channelId = entry.key;
          final config = entry.value;

          // Skip if already created by shared utility
          if (channelId == NotificationUtils.chargingChannelId ||
              channelId == NotificationUtils.fcmChannelId) {
            continue;
          }

          try {
            final channel = config.toAndroidChannel();
            await androidPlugin.createNotificationChannel(channel);

            _channelsCreated[channelId] = true;
            debugPrint('✅ Created additional notification channel: $channelId');
          } catch (e) {
            debugPrint('❌ Error creating channel $channelId: $e');
            _channelsCreated[channelId] = false;
          }
        }

        // Create additional channels for active charging notifications
        await _createActiveChargingChannel(androidPlugin);
      }
    } catch (e) {
      debugPrint('❌ Error in notification channel creation: $e');
    }
  }

  /// Check if a specific channel is created
  bool isChannelCreated(String channelId) {
    return _channelsCreated.containsKey(channelId) &&
        _channelsCreated[channelId] == true;
  }

  /// Ensure a specific channel exists (public method for guarantee)
  Future<void> ensureChannelExists(String channelId) async {
    if (channelId == 'active_charging_session') {
      await _ensureActiveChargingChannelExists();
    } else {
      debugPrint('⚠️ Channel creation not supported for: $channelId');
    }
  }

  /// Test notification capability for a specific channel
  Future<bool> testNotificationCapability(String channelId) async {
    try {
      debugPrint('🧪 Testing notification capability for channel: $channelId');

      // Try to show a silent test notification
      final testId = 99999; // High ID to avoid conflicts
      final success = await showNotification(
        id: testId,
        title: 'Test',
        body: 'Silent test notification',
        channelId: channelId,
        payload: 'test_notification',
      );

      // Immediately cancel the test notification
      if (success) {
        await cancelNotification(testId);
        debugPrint('✅ Notification test successful for channel: $channelId');
      } else {
        debugPrint('❌ Notification test failed for channel: $channelId');
      }

      return success;
    } catch (e) {
      debugPrint('❌ Error testing notification capability: $e');
      return false;
    }
  }

  /// Ensure active charging channel exists (fallback method)
  Future<void> _ensureActiveChargingChannelExists() async {
    if (!Platform.isAndroid) return;

    final androidPlugin =
        _localNotifications.resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>();

    if (androidPlugin != null) {
      await _createActiveChargingChannel(androidPlugin);
    }
  }

  /// Create active charging notification channel
  Future<void> _createActiveChargingChannel(
      AndroidFlutterLocalNotificationsPlugin androidPlugin) async {
    try {
      debugPrint('📱 Creating active_charging_session channel...');

      const channel = AndroidNotificationChannel(
        'active_charging_session',
        'Active Charging Session',
        description: 'Persistent notifications for ongoing charging sessions',
        importance: Importance.high,
        showBadge: true,
        enableVibration: true,
        enableLights: true,
        ledColor: Color(0xFF00FF00),
        playSound: false,
      );

      await androidPlugin.createNotificationChannel(channel);
      _channelsCreated['active_charging_session'] = true;
      debugPrint('✅ Created active charging notification channel successfully');
      debugPrint('📱 Channel ID: active_charging_session');
      debugPrint('📱 Channel Name: Active Charging Session');
    } catch (e) {
      debugPrint('❌ Error creating active charging channel: $e');
      debugPrint('❌ Error type: ${e.runtimeType}');
      debugPrint('❌ Error details: ${e.toString()}');
      _channelsCreated['active_charging_session'] = false;
    }
  }

  /// Handle notification tap
  Future<void> _onNotificationTapped(NotificationResponse response) async {
    debugPrint('📱 Notification tapped: ${response.payload}');

    try {
      if (response.payload != null && response.payload!.isNotEmpty) {
        // Parse payload to determine navigation
        final payload = response.payload!;

        if (payload.startsWith('welcome|')) {
          // Welcome notification - navigate to dashboard
          await _navigationService.storeNavigationIntent('dashboard', payload);
        } else if (payload.startsWith('charging_session|')) {
          // Charging session notification - navigate to charging session
          await _navigationService.storeNavigationIntent(
              'charging_session', payload);
        } else {
          // Default navigation
          await _navigationService.storeNavigationIntent('dashboard', payload);
        }

        await _navigationService.processPendingNavigationIntents();
      }
    } catch (e) {
      debugPrint('❌ Error handling notification tap: $e');
    }
  }

  /// Show a local notification
  Future<bool> showNotification({
    required int id,
    required String title,
    required String body,
    required String channelId,
    String? payload,
    bool ongoing = false,
    bool autoCancel = true,
    int? progress,
    int? maxProgress,
    List<AndroidNotificationAction>? actions,
    AndroidNotificationDetails? customAndroidDetails,
  }) async {
    if (!_isInitialized) {
      debugPrint('❌ Cannot show notification - manager not initialized');
      return false;
    }

    if (!_permissionsGranted) {
      debugPrint('❌ Cannot show notification - permissions not granted');
      return false;
    }

    if (!_channelsCreated.containsKey(channelId) ||
        !_channelsCreated[channelId]!) {
      debugPrint('❌ Cannot show notification - channel $channelId not created');
      debugPrint('📱 Available channels: ${_channelsCreated.keys.toList()}');
      debugPrint('📱 Channel states: $_channelsCreated');

      // Try to create the channel if it's the active charging session channel
      if (channelId == 'active_charging_session') {
        debugPrint(
            '🔧 Attempting to create missing active_charging_session channel...');
        await _ensureActiveChargingChannelExists();

        // Check again after creation attempt
        if (!_channelsCreated.containsKey(channelId) ||
            !_channelsCreated[channelId]!) {
          debugPrint('❌ Failed to create active_charging_session channel');
          return false;
        }
        debugPrint(
            '✅ Successfully created missing active_charging_session channel');
      } else {
        return false;
      }
    }

    try {
      debugPrint('📱 Showing notification: $title');

      // Get channel configuration
      final channelConfig = NotificationConfig.getChannelConfig(channelId);
      if (channelConfig == null) {
        debugPrint('❌ Channel configuration not found for: $channelId');
        return false;
      }

      // Create Android notification details
      final androidDetails = customAndroidDetails ??
          AndroidNotificationDetails(
            channelId,
            channelConfig.name,
            channelDescription: channelConfig.description,
            importance: channelConfig.importance,
            ongoing: ongoing,
            autoCancel: autoCancel,
            showWhen: true,
            when: DateTime.now().millisecondsSinceEpoch,
            showProgress: progress != null && maxProgress != null,
            maxProgress: maxProgress ?? 100,
            progress: progress ?? 0,
            indeterminate: false,
            color: const Color(0xFF4CAF50),
            colorized: true,
            largeIcon:
                const DrawableResourceAndroidBitmap('@mipmap/launcher_icon'),
            icon: '@mipmap/launcher_icon', // BRANDING: Actual EcoPlug app logo
            actions: actions,
            styleInformation: BigTextStyleInformation(
              body,
              htmlFormatBigText: false,
              contentTitle: title,
              htmlFormatContentTitle: false,
            ),
          );

      // Create iOS notification details
      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      final notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      // Show the notification
      await _localNotifications.show(
        id,
        title,
        body,
        notificationDetails,
        payload: payload,
      );

      // HISTORY: Store notification in history
      await _historyService.addLocalNotification(
        notificationId: id,
        title: title,
        body: body,
        type: _getNotificationTypeFromChannel(channelId),
        channelId: channelId,
        data: {'payload': payload},
        priority: 0,
      );

      debugPrint('✅ Notification shown successfully: $title');
      return true;
    } catch (e) {
      debugPrint('❌ Error showing notification: $e');
      return false;
    }
  }

  /// Cancel a notification
  Future<void> cancelNotification(int id) async {
    try {
      await _localNotifications.cancel(id);
      debugPrint('✅ Notification cancelled: $id');
    } catch (e) {
      debugPrint('❌ Error cancelling notification: $e');
    }
  }

  /// Cancel all notifications
  Future<void> cancelAllNotifications() async {
    try {
      await _localNotifications.cancelAll();
      debugPrint('✅ All notifications cancelled');
    } catch (e) {
      debugPrint('❌ Error cancelling all notifications: $e');
    }
  }

  /// Get notification manager status
  Map<String, dynamic> getStatus() {
    return {
      'initialized': _isInitialized,
      'permissions_granted': _permissionsGranted,
      'channels_created': _channelsCreated,
      'platform': Platform.operatingSystem,
    };
  }

  /// Test notification functionality
  Future<bool> testNotification() async {
    debugPrint('📱 Testing notification functionality...');

    final success = await showNotification(
      id: 99999,
      title: 'EcoPlug Test Notification',
      body:
          'This is a test notification to verify the system is working correctly.',
      channelId: 'user_welcome',
      payload: 'test|notification',
    );

    if (success) {
      debugPrint('✅ Test notification sent successfully');
    } else {
      debugPrint('❌ Test notification failed');
    }

    return success;
  }

  /// Check if notifications are enabled
  bool get isReady => _isInitialized && _permissionsGranted;

  /// Get the local notifications plugin instance
  FlutterLocalNotificationsPlugin get plugin => _localNotifications;

  /// Helper method to determine notification type from channel ID
  NotificationType _getNotificationTypeFromChannel(String channelId) {
    switch (channelId) {
      case 'charging_session':
      case 'charging_updates':
        return NotificationType.charging;
      case 'wallet_updates':
      case 'payment_alerts':
        return NotificationType.payment;
      case 'station_alerts':
      case 'station_updates':
        return NotificationType.station;
      case 'user_welcome':
      case 'welcome_messages':
        return NotificationType.welcome;
      case 'promotions':
      case 'offers':
        return NotificationType.offer;
      case 'system_updates':
      case 'maintenance_alerts':
        return NotificationType.maintenance;
      default:
        return NotificationType.general;
    }
  }
}
