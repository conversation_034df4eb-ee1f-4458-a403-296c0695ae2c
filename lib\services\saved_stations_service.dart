import 'package:flutter/foundation.dart';
import '../core/api/api_service.dart';
import '../core/api/api_config.dart';
import '../models/saved_station_model.dart';
import '../models/station.dart';

/// Service for managing saved/bookmarked stations
class SavedStationsService {
  final ApiService _apiService = ApiService();

  /// Fetch all saved/bookmarked stations (API returns full station details directly)
  Future<List<Station>> getSavedStations() async {
    try {
      debugPrint('🔖 Fetching saved stations from API...');

      final response = await _apiService.get(ApiConfig.getBookmarks);

      debugPrint('🔖 Saved stations API response: $response');
      debugPrint('🔖 Response type: ${response.runtimeType}');

      if (response is! Map<String, dynamic>) {
        throw Exception('Invalid response format from saved stations API');
      }

      final savedStationsResponse = SavedStationsResponse.fromJson(response);
      debugPrint('🔖 Successfully parsed ${savedStationsResponse.data.length} saved stations');

      if (!savedStationsResponse.success) {
        throw Exception(savedStationsResponse.message);
      }

      // Convert SavedStation models to Station models for UI
      final List<Station> stations = savedStationsResponse.data
          .map((savedStation) => _convertSavedStationToStation(savedStation))
          .toList();

      debugPrint('🔖 Successfully converted ${stations.length} saved stations to Station models');
      return stations;

    } catch (e) {
      debugPrint('❌ Error fetching saved stations: $e');
      throw Exception('Failed to fetch saved stations: $e');
    }
  }

  /// Convert SavedStation to Station model for UI consistency
  Station _convertSavedStationToStation(SavedStation savedStation) {
    // Normalize status for consistent UI display
    final normalizedStatus = _normalizeStationStatus(savedStation.status);

    // Create connector list from types
    final connectors = savedStation.types.map((type) {
      return Connector(
        id: type.name,
        name: type.name,
        type: type.name,
        price: 0.0,
        power: 'Unknown', // No power info in saved stations API
        totalGuns: 1,
        availableGuns: normalizedStatus.toLowerCase() == 'available' ? 1 : 0,
        icon: type.icon,
        status: normalizedStatus,
        maxElectricPower: 0,
        standard: type.name,
        priceLabel: 'Contact Station',
        pricePerUnit: 0.0,
        soc: 0,
        powerOutput: 'Unknown',
        maxPower: 0,
        evsesUid: '',
      );
    }).toList();

    return Station(
      id: savedStation.stationId.toString(),
      name: savedStation.name,
      address: savedStation.address,
      city: savedStation.city,
      state: null,
      images: [], // No images in saved stations API
      evses: [], // No EVSE data in saved stations API
      latitude: savedStation.latitude,
      longitude: savedStation.longitude,
      distance: 0.0, // Distance will be calculated if needed
      status: normalizedStatus,
      rating: 0.0, // No rating in saved stations API
      reviews: 0, // No reviews in saved stations API
      connectors: connectors,
      mapPinUrl: null,
      focusedMapPinUrl: null,
      types: savedStation.types.map((type) => {
        'name': type.name,
        'icon': type.icon,
      }).toList(),
      uid: savedStation.uid,
      openingTimes: null,
      openStatus: null,
    );
  }

  /// Normalize station status for consistent UI display
  String _normalizeStationStatus(String status) {
    final statusLower = status.toLowerCase().trim();

    // Map various status values to standardized ones
    switch (statusLower) {
      case 'available':
      case 'open':
      case 'online':
      case 'operational':
        return 'Available';

      case 'closed':
      case 'offline':
      case 'unavailable':
      case 'out of service':
      case 'maintenance':
        return 'Closed';

      case 'in use':
      case 'charging':
      case 'occupied':
        return 'In Use';

      case 'unknown':
      case '':
      default:
        return 'Closed'; // Default unknown statuses to "Closed" (conservative approach)
    }
  }

  /// Remove a station from bookmarks
  Future<bool> removeBookmark(String stationUid) async {
    try {
      debugPrint('🔖 Removing bookmark for station: $stationUid');

      final response = await _apiService.post(
        ApiConfig.saveBookmark,
        data: {
          'location_uid': stationUid,
          'status': 0, // 0 = remove bookmark
        },
      );

      debugPrint('🔖 Remove bookmark API response: $response');

      if (response['success'] == true) {
        debugPrint('✅ Bookmark removed successfully');
        return true;
      } else {
        debugPrint('❌ Failed to remove bookmark: ${response['message']}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ Error removing bookmark: $e');
      return false;
    }
  }
}
