import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'dart:async';
import 'dart:collection';

import '../../core/api/api_service.dart';
import '../../models/wallet_transaction_model.dart';
import 'payu_error_handler.dart';

/// Comprehensive transaction verification system for PayU payments
/// Handles transaction status verification, duplicate prevention, and recovery
class PayUTransactionVerifier {
  static const String _logPrefix = '🔍 PAYU VERIFIER:';
  static const String _pendingTransactionsKey = 'payu_pending_transactions';
  static const String _verificationHistoryKey = 'payu_verification_history';
  
  final ApiService _apiService;
  
  PayUTransactionVerifier(this._apiService);

  /// Verify transaction status with comprehensive checks
  Future<TransactionVerificationResult> verifyTransaction({
    required String transactionId,
    required String expectedStatus,
    int maxRetries = 3,
    Duration retryDelay = const Duration(seconds: 5),
  }) async {
    debugPrint('$_logPrefix Starting verification for transaction: $transactionId');
    debugPrint('$_logPrefix Expected status: $expectedStatus');
    
    final startTime = DateTime.now();
    
    try {
      // 1. Check if transaction is already being verified
      if (await _isTransactionBeingVerified(transactionId)) {
        debugPrint('$_logPrefix Transaction already being verified, waiting...');
        return await _waitForExistingVerification(transactionId);
      }
      
      // 2. Mark transaction as being verified
      await _markTransactionAsVerifying(transactionId);
      
      // 3. Perform verification with retries
      TransactionVerificationResult? result;
      
      for (int attempt = 1; attempt <= maxRetries; attempt++) {
        debugPrint('$_logPrefix Verification attempt $attempt/$maxRetries');
        
        try {
          result = await _performSingleVerification(transactionId, expectedStatus);
          
          if (result.isVerified || !result.shouldRetry) {
            break;
          }
          
          if (attempt < maxRetries) {
            debugPrint('$_logPrefix Retrying in ${retryDelay.inSeconds} seconds...');
            await Future.delayed(retryDelay);
          }
          
        } catch (e) {
          debugPrint('$_logPrefix Verification attempt $attempt failed: $e');
          
          if (attempt == maxRetries) {
            result = TransactionVerificationResult(
              transactionId: transactionId,
              isVerified: false,
              actualStatus: 'UNKNOWN',
              expectedStatus: expectedStatus,
              error: PayUErrorHandler.handleApiError(e, transactionId),
              verificationTime: DateTime.now().difference(startTime),
              shouldRetry: false,
            );
          }
        }
      }
      
      // 4. Record verification result
      await _recordVerificationResult(result!);
      
      // 5. Clean up verification state
      await _clearTransactionVerification(transactionId);
      
      final totalTime = DateTime.now().difference(startTime);
      debugPrint('$_logPrefix Verification completed in ${totalTime.inMilliseconds}ms');
      debugPrint('$_logPrefix Result: ${result.isVerified ? 'VERIFIED' : 'FAILED'}');
      
      return result;
      
    } catch (e) {
      await _clearTransactionVerification(transactionId);
      
      return TransactionVerificationResult(
        transactionId: transactionId,
        isVerified: false,
        actualStatus: 'ERROR',
        expectedStatus: expectedStatus,
        error: PayUErrorHandler.handleUnknownError(e, transactionId),
        verificationTime: DateTime.now().difference(startTime),
        shouldRetry: false,
      );
    }
  }

  /// Verify multiple transactions in batch
  Future<List<TransactionVerificationResult>> verifyMultipleTransactions(
    List<String> transactionIds, {
    String expectedStatus = 'COMPLETED',
    int maxConcurrent = 3,
  }) async {
    debugPrint('$_logPrefix Batch verification for ${transactionIds.length} transactions');
    
    final results = <TransactionVerificationResult>[];
    final semaphore = Semaphore(maxConcurrent);
    
    final futures = transactionIds.map((txnId) async {
      await semaphore.acquire();
      try {
        return await verifyTransaction(
          transactionId: txnId,
          expectedStatus: expectedStatus,
        );
      } finally {
        semaphore.release();
      }
    });
    
    results.addAll(await Future.wait(futures));
    
    debugPrint('$_logPrefix Batch verification completed');
    debugPrint('$_logPrefix Verified: ${results.where((r) => r.isVerified).length}/${results.length}');
    
    return results;
  }

  /// Check for pending transactions that need verification
  Future<List<String>> getPendingTransactions() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final pendingJson = prefs.getString(_pendingTransactionsKey);
      
      if (pendingJson == null) return [];
      
      final pendingData = jsonDecode(pendingJson) as Map<String, dynamic>;
      final now = DateTime.now();
      
      // Filter out expired pending transactions (older than 1 hour)
      final validTransactions = <String>[];
      
      for (final entry in pendingData.entries) {
        final timestamp = DateTime.parse(entry.value['timestamp']);
        if (now.difference(timestamp).inHours < 1) {
          validTransactions.add(entry.key);
        }
      }
      
      // Update storage with valid transactions only
      if (validTransactions.length != pendingData.length) {
        await _updatePendingTransactions(validTransactions);
      }
      
      return validTransactions;
      
    } catch (e) {
      debugPrint('$_logPrefix Error getting pending transactions: $e');
      return [];
    }
  }

  /// Add transaction to pending verification list
  Future<void> addPendingTransaction(String transactionId) async {
    try {
      final pending = await getPendingTransactions();
      if (!pending.contains(transactionId)) {
        pending.add(transactionId);
        await _updatePendingTransactions(pending);
        debugPrint('$_logPrefix Added transaction to pending list: $transactionId');
      }
    } catch (e) {
      debugPrint('$_logPrefix Error adding pending transaction: $e');
    }
  }

  /// Remove transaction from pending verification list
  Future<void> removePendingTransaction(String transactionId) async {
    try {
      final pending = await getPendingTransactions();
      if (pending.remove(transactionId)) {
        await _updatePendingTransactions(pending);
        debugPrint('$_logPrefix Removed transaction from pending list: $transactionId');
      }
    } catch (e) {
      debugPrint('$_logPrefix Error removing pending transaction: $e');
    }
  }

  /// Get verification history for debugging
  Future<List<TransactionVerificationResult>> getVerificationHistory({
    int limit = 50,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final historyJson = prefs.getString(_verificationHistoryKey);
      
      if (historyJson == null) return [];
      
      final historyList = jsonDecode(historyJson) as List;
      final results = historyList
          .map((json) => TransactionVerificationResult.fromJson(json))
          .toList();
      
      // Sort by timestamp (newest first) and limit
      results.sort((a, b) => b.timestamp.compareTo(a.timestamp));
      
      return results.take(limit).toList();
      
    } catch (e) {
      debugPrint('$_logPrefix Error getting verification history: $e');
      return [];
    }
  }

  /// Clear old verification history
  Future<void> clearOldVerificationHistory({
    Duration maxAge = const Duration(days: 7),
  }) async {
    try {
      final history = await getVerificationHistory(limit: 1000);
      final cutoffTime = DateTime.now().subtract(maxAge);
      
      final recentHistory = history
          .where((result) => result.timestamp.isAfter(cutoffTime))
          .toList();
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        _verificationHistoryKey,
        jsonEncode(recentHistory.map((r) => r.toJson()).toList()),
      );
      
      debugPrint('$_logPrefix Cleared old verification history');
      debugPrint('$_logPrefix Kept ${recentHistory.length}/${history.length} recent records');
      
    } catch (e) {
      debugPrint('$_logPrefix Error clearing verification history: $e');
    }
  }

  /// Perform single verification attempt
  Future<TransactionVerificationResult> _performSingleVerification(
    String transactionId,
    String expectedStatus,
  ) async {
    debugPrint('$_logPrefix Performing verification for: $transactionId');
    
    try {
      // Fetch current wallet data to check transaction status
      final walletResponse = await _apiService.get('/user/wallet/info');
      
      if (walletResponse['success'] != true) {
        throw Exception('Failed to fetch wallet data: ${walletResponse['message']}');
      }
      
      final walletData = walletResponse['data'];
      final transactions = (walletData['payment_history'] as List?)
          ?.map((json) => TransactionModel.fromJson(json))
          .toList() ?? [];
      
      // Find the transaction
      final transaction = transactions.firstWhere(
        (tx) => tx.id.toString() == transactionId ||
                tx.remark.contains(transactionId) ||
                tx.source.contains(transactionId),
        orElse: () => throw Exception('Transaction not found'),
      );
      
      final actualStatus = transaction.status?.toUpperCase() ?? 'UNKNOWN';
      final isVerified = _isStatusMatch(actualStatus, expectedStatus);
      
      debugPrint('$_logPrefix Transaction found - Status: $actualStatus');
      debugPrint('$_logPrefix Expected: $expectedStatus, Verified: $isVerified');
      
      return TransactionVerificationResult(
        transactionId: transactionId,
        isVerified: isVerified,
        actualStatus: actualStatus,
        expectedStatus: expectedStatus,
        transaction: transaction,
        verificationTime: Duration.zero, // Will be set by caller
        shouldRetry: !isVerified && _shouldRetryForStatus(actualStatus),
      );
      
    } catch (e) {
      debugPrint('$_logPrefix Verification failed: $e');
      
      return TransactionVerificationResult(
        transactionId: transactionId,
        isVerified: false,
        actualStatus: 'ERROR',
        expectedStatus: expectedStatus,
        error: PayUErrorHandler.handleApiError(e, transactionId),
        verificationTime: Duration.zero,
        shouldRetry: true,
      );
    }
  }

  /// Check if status matches expected (with fuzzy matching)
  bool _isStatusMatch(String actualStatus, String expectedStatus) {
    final actual = actualStatus.toUpperCase();
    final expected = expectedStatus.toUpperCase();

    if (actual == expected) return true;

    // Fuzzy matching for common status variations
    const statusGroups = {
      'SUCCESS': ['COMPLETED', 'SUCCESSFUL', 'SUCCESS', 'COMPLETE'],
      'FAILED': ['REJECTED', 'FAILED', 'FAILURE', 'ERROR', 'DECLINED'],
      'PENDING': ['PENDING', 'PROCESSING', 'INITIATED', 'IN_PROGRESS'],
      'CANCELLED': ['CANCELLED', 'CANCELED', 'ABORTED', 'TIMEOUT'],
    };

    for (final group in statusGroups.values) {
      if (group.contains(actual) && group.contains(expected)) {
        return true;
      }
    }

    return false;
  }

  /// Check if status should trigger retry
  bool _shouldRetryForStatus(String status) {
    const retryableStatuses = ['PENDING', 'PROCESSING', 'INITIATED', 'IN_PROGRESS'];
    return retryableStatuses.contains(status.toUpperCase());
  }

  /// Update pending transactions list
  Future<void> _updatePendingTransactions(List<String> transactions) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final pendingData = <String, dynamic>{};

      for (final txnId in transactions) {
        pendingData[txnId] = {
          'timestamp': DateTime.now().toIso8601String(),
        };
      }

      await prefs.setString(_pendingTransactionsKey, jsonEncode(pendingData));
    } catch (e) {
      debugPrint('$_logPrefix Error updating pending transactions: $e');
    }
  }

  /// Check if transaction is currently being verified
  Future<bool> _isTransactionBeingVerified(String transactionId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final verifyingKey = 'verifying_$transactionId';
      return prefs.containsKey(verifyingKey);
    } catch (e) {
      return false;
    }
  }

  /// Mark transaction as being verified
  Future<void> _markTransactionAsVerifying(String transactionId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final verifyingKey = 'verifying_$transactionId';
      await prefs.setString(verifyingKey, DateTime.now().toIso8601String());
    } catch (e) {
      debugPrint('$_logPrefix Error marking transaction as verifying: $e');
    }
  }

  /// Clear transaction verification state
  Future<void> _clearTransactionVerification(String transactionId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final verifyingKey = 'verifying_$transactionId';
      await prefs.remove(verifyingKey);
    } catch (e) {
      debugPrint('$_logPrefix Error clearing verification state: $e');
    }
  }

  /// Wait for existing verification to complete
  Future<TransactionVerificationResult> _waitForExistingVerification(
    String transactionId,
  ) async {
    const maxWaitTime = Duration(minutes: 2);
    const checkInterval = Duration(seconds: 1);

    final startTime = DateTime.now();

    while (DateTime.now().difference(startTime) < maxWaitTime) {
      if (!await _isTransactionBeingVerified(transactionId)) {
        // Verification completed, try to get result from history
        final history = await getVerificationHistory(limit: 10);
        final recentResult = history.firstWhere(
          (r) => r.transactionId == transactionId,
          orElse: () => TransactionVerificationResult(
            transactionId: transactionId,
            isVerified: false,
            actualStatus: 'UNKNOWN',
            expectedStatus: 'UNKNOWN',
            verificationTime: Duration.zero,
            shouldRetry: true,
          ),
        );

        return recentResult;
      }

      await Future.delayed(checkInterval);
    }

    // Timeout waiting for existing verification
    return TransactionVerificationResult(
      transactionId: transactionId,
      isVerified: false,
      actualStatus: 'TIMEOUT',
      expectedStatus: 'UNKNOWN',
      error: PayUErrorHandler.handleUnknownError(
        'Timeout waiting for existing verification',
        transactionId,
      ),
      verificationTime: maxWaitTime,
      shouldRetry: true,
    );
  }

  /// Record verification result in history
  Future<void> _recordVerificationResult(TransactionVerificationResult result) async {
    try {
      final history = await getVerificationHistory(limit: 100);
      history.insert(0, result);

      // Keep only recent 100 results
      final recentHistory = history.take(100).toList();

      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        _verificationHistoryKey,
        jsonEncode(recentHistory.map((r) => r.toJson()).toList()),
      );

    } catch (e) {
      debugPrint('$_logPrefix Error recording verification result: $e');
    }
  }
}

/// Result of transaction verification
class TransactionVerificationResult {
  final String transactionId;
  final bool isVerified;
  final String actualStatus;
  final String expectedStatus;
  final TransactionModel? transaction;
  final PayUError? error;
  final Duration verificationTime;
  final bool shouldRetry;
  final DateTime timestamp;

  TransactionVerificationResult({
    required this.transactionId,
    required this.isVerified,
    required this.actualStatus,
    required this.expectedStatus,
    this.transaction,
    this.error,
    required this.verificationTime,
    this.shouldRetry = false,
  }) : timestamp = DateTime.now();

  /// Convert to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'transaction_id': transactionId,
      'is_verified': isVerified,
      'actual_status': actualStatus,
      'expected_status': expectedStatus,
      'transaction': transaction != null ? {
        'id': transaction!.id,
        'amount': transaction!.amount,
        'status': transaction!.status,
        'type': transaction!.type,
        'remark': transaction!.remark,
        'created_at': transaction!.createdAt.toIso8601String(),
        'source': transaction!.source,
      } : null,
      'error': error?.toJson(),
      'verification_time_ms': verificationTime.inMilliseconds,
      'should_retry': shouldRetry,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  /// Create from JSON
  factory TransactionVerificationResult.fromJson(Map<String, dynamic> json) {
    return TransactionVerificationResult(
      transactionId: json['transaction_id'] ?? '',
      isVerified: json['is_verified'] ?? false,
      actualStatus: json['actual_status'] ?? 'UNKNOWN',
      expectedStatus: json['expected_status'] ?? 'UNKNOWN',
      transaction: json['transaction'] != null
          ? TransactionModel.fromJson(json['transaction'])
          : null,
      error: json['error'] != null
          ? PayUError.fromJson(json['error'])
          : null,
      verificationTime: Duration(milliseconds: json['verification_time_ms'] ?? 0),
      shouldRetry: json['should_retry'] ?? false,
    );
  }

  @override
  String toString() {
    return 'TransactionVerificationResult(id: $transactionId, verified: $isVerified, status: $actualStatus)';
  }
}

/// Simple semaphore implementation for controlling concurrency
class Semaphore {
  final int maxCount;
  int _currentCount;
  final Queue<Completer<void>> _waitQueue = Queue<Completer<void>>();

  Semaphore(this.maxCount) : _currentCount = maxCount;

  Future<void> acquire() async {
    if (_currentCount > 0) {
      _currentCount--;
      return;
    }

    final completer = Completer<void>();
    _waitQueue.add(completer);
    return completer.future;
  }

  void release() {
    if (_waitQueue.isNotEmpty) {
      final completer = _waitQueue.removeFirst();
      completer.complete();
    } else {
      _currentCount++;
    }
  }
}
