/// Transaction model
class TransactionModel {
  final String id;
  final String title;
  final String description;
  final double amount;
  final DateTime timestamp;
  final String type; // 'credit', 'debit'
  final String status; // 'completed', 'pending', 'failed'
  final String? transactionReference;
  
  TransactionModel({
    required this.id,
    required this.title,
    required this.description,
    required this.amount,
    required this.timestamp,
    required this.type,
    required this.status,
    this.transactionReference,
  });
  
  factory TransactionModel.fromJson(Map<String, dynamic> json) {
    return TransactionModel(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      amount: (json['amount'] as num?)?.toDouble() ?? 0.0,
      timestamp: json['timestamp'] != null 
          ? DateTime.parse(json['timestamp']) 
          : DateTime.now(),
      type: json['type'] ?? 'debit',
      status: json['status'] ?? 'completed',
      transactionReference: json['transactionReference'],
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'amount': amount,
      'timestamp': timestamp.toIso8601String(),
      'type': type,
      'status': status,
      'transactionReference': transactionReference,
    };
  }
}
