 import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'package:ecoplug/models/user_model.dart';
import 'package:ecoplug/services/service_locator.dart';
import 'package:ecoplug/services/sync_service.dart';
import 'package:ecoplug/services/auth_manager.dart';
import 'package:ecoplug/widgets/gst_input_widget.dart';
import 'package:ecoplug/utils/gst_formatter.dart';
import 'package:ecoplug/models/api_profile_model.dart';
import 'package:ecoplug/services/api_service.dart';
import 'package:ecoplug/core/api/api_service.dart' as CoreApi;
import 'package:ecoplug/shared/utils/api_response_handler.dart';

class EditProfilePage extends StatefulWidget {
  final UserProfile userProfile;

  const EditProfilePage({
    super.key,
    required this.userProfile,
  });

  @override
  State<EditProfilePage> createState() => _EditProfilePageState();
}

class _EditProfilePageState extends State<EditProfilePage>
    with TickerProviderStateMixin {
  late TextEditingController _nameController;
  late TextEditingController _emailController;
  bool _isLoading = false;
  bool _hasChanges = false;
  bool _gstExpanded = false;
  String? _gstNo;
  String? _businessName;
  ProfileData? _profileData;

  // API service for GST updates
  final CoreApi.ApiService _coreApiService = CoreApi.ApiService();

  // Enhanced animation controllers
  late AnimationController _fadeController;
  late AnimationController _slideController;

  // Animation objects
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.userProfile.name);
    _emailController = TextEditingController(text: widget.userProfile.email);

    // Initialize GST values from user profile, cleaning GST number
    _gstNo = widget.userProfile.gstNo != null
        ? GSTFormatter.cleanGSTForAPI(widget.userProfile.gstNo!)
        : null;
    _businessName = widget.userProfile.businessName;

    _initializeAnimations();
    _startAnimations();

    // Listen for changes to detect if user has modified anything
    _nameController.addListener(_checkForChanges);
    _emailController.addListener(_checkForChanges);

    // Load profile data from API
    _loadProfileData();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1200),
    );

    _slideController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeOutCubic),
    );
  }

  void _startAnimations() {
    _fadeController.forward();
    _slideController.forward();
  }

  Future<void> _loadProfileData() async {
    try {
      // First try to get fresh data from the profile API
      debugPrint('🔄 Loading profile data from API...');

      final apiService = ApiService();
      final profileResponse = await apiService.getUserProfile();

      if (profileResponse.success) {
        // Extract GST data directly from the API response data
        final profileData = profileResponse.data;

        if (mounted) {
          setState(() {
            // Clean GST number by removing spaces for consistent storage
            _gstNo = profileData.gstNo != null && profileData.gstNo!.isNotEmpty
                ? GSTFormatter.cleanGSTForAPI(profileData.gstNo!)
                : null;
            _businessName = profileData.businessName;
          });
        }

        // Debug logging for GST data loading
        debugPrint('🔍 GST DATA LOADED FROM API:');
        debugPrint('  Original GST: ${profileData.gstNo}');
        debugPrint('  Cleaned GST: $_gstNo');
        debugPrint('  Business Name: $_businessName');
        debugPrint('  Has GST Data: ${_hasGSTData()}');

        // Update the ProfileData for other uses
        _profileData = profileData;

      } else {
        // Fallback to AuthManager cached data if API fails
        debugPrint('⚠️ API failed, falling back to cached data...');

        final authManager = AuthManager();
        final userData = await authManager.getUserData();

        if (userData != null) {
          // Try to extract GST data from the raw userData map
          final gstNo = userData['gst_no'] as String?;
          final businessName = userData['business_name'] as String?;

          if (mounted) {
            setState(() {
              // Clean GST number by removing spaces for consistent storage
              _gstNo = gstNo != null && gstNo.isNotEmpty
                  ? GSTFormatter.cleanGSTForAPI(gstNo)
                  : null;
              _businessName = businessName;
            });
          }

          // Debug logging for cached GST data loading
          debugPrint('🔍 GST DATA LOADED FROM CACHE:');
          debugPrint('  Original GST: $gstNo');
          debugPrint('  Cleaned GST: $_gstNo');
          debugPrint('  Business Name: $_businessName');
          debugPrint('  Has GST Data: ${_hasGSTData()}');

          // Create ProfileData from user data for other uses
          _profileData = ProfileData.fromJson(userData);
        }
      }
    } catch (e) {
      debugPrint('❌ Error loading profile data: $e');

      // Final fallback to AuthManager cached data
      try {
        final authManager = AuthManager();
        final userData = await authManager.getUserData();

        if (userData != null) {
          // Try to extract GST data from the raw userData map
          final gstNo = userData['gst_no'] as String?;
          final businessName = userData['business_name'] as String?;

          if (mounted) {
            setState(() {
              // Clean GST number by removing spaces for consistent storage
              _gstNo = gstNo != null && gstNo.isNotEmpty
                  ? GSTFormatter.cleanGSTForAPI(gstNo)
                  : null;
              _businessName = businessName;
            });
          }

          debugPrint('🔍 GST DATA LOADED FROM FALLBACK CACHE:');
          debugPrint('  Original GST: $gstNo');
          debugPrint('  Cleaned GST: $_gstNo');
          debugPrint('  Business Name: $_businessName');
          debugPrint('  Has GST Data: ${_hasGSTData()}');
        }
      } catch (fallbackError) {
        debugPrint('❌ Fallback also failed: $fallbackError');
      }
    }
  }

  void _checkForChanges() {
    final hasChanges = _nameController.text != widget.userProfile.name ||
        _emailController.text != widget.userProfile.email ||
        _gstNo != widget.userProfile.gstNo ||
        _businessName != widget.userProfile.businessName;

    if (hasChanges != _hasChanges) {
      setState(() {
        _hasChanges = hasChanges;
      });
    }
  }

  void _onGSTChanged(String? gstNo, String? businessName) {
    setState(() {
      // Clean GST number to ensure consistent format (remove spaces)
      _gstNo = gstNo != null && gstNo.isNotEmpty
          ? GSTFormatter.cleanGSTForAPI(gstNo)
          : null;
      _businessName = businessName;
    });
    _checkForChanges();
  }

  void _toggleGSTExpansion() {
    setState(() {
      _gstExpanded = !_gstExpanded;
    });
  }

  /// Check if user has existing GST data
  bool _hasGSTData() {
    final hasGstNo = _gstNo != null && _gstNo!.isNotEmpty;
    final hasBusinessName = _businessName != null && _businessName!.isNotEmpty;
    return hasGstNo || hasBusinessName;
  }

  void _resetChanges() {
    setState(() {
      // Reset text controllers to original values
      _nameController.text = widget.userProfile.name;
      _emailController.text = widget.userProfile.email;

      // Reset GST values to original values, cleaning GST number
      _gstNo = widget.userProfile.gstNo != null
          ? GSTFormatter.cleanGSTForAPI(widget.userProfile.gstNo!)
          : null;
      _businessName = widget.userProfile.businessName;

      // Collapse GST section
      _gstExpanded = false;

      // Reset changes flag
      _hasChanges = false;
    });

    // Show feedback to user
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Changes have been reset'),
        backgroundColor: Colors.orange,
        behavior: SnackBarBehavior.floating,
        duration: Duration(seconds: 2),
      ),
    );
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  // This method is now handled by the image picker button directly

  Future<void> _saveProfile() async {
    if (!_hasChanges) return;

    setState(() {
      _isLoading = true;
    });

    // Show a loading indicator
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Updating profile...'),
        duration: Duration(seconds: 1),
      ),
    );

    try {
      // Get user ID from SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      final userId = prefs.getString('user_id');

      if (userId == null) {
        throw Exception('User ID not found. Please log in again.');
      }

      // Log the update attempt
      debugPrint('\n=== UPDATING PROFILE FROM EDIT SCREEN ===');
      debugPrint('User ID: $userId');
      debugPrint('Name: ${_nameController.text}');
      debugPrint('Email: ${_emailController.text}');
      debugPrint('GST No: $_gstNo');
      debugPrint('Business Name: $_businessName');

      // Get the auth manager
      final authManager = AuthManager();

      // Get current user data
      final userData = await authManager.getUserData() ?? {};

      // Update user data with new values, ensuring GST number is cleaned
      userData['name'] = _nameController.text;
      userData['email'] = _emailController.text;
      userData['gst_no'] = _gstNo != null ? GSTFormatter.cleanGSTForAPI(_gstNo!) : null;
      userData['business_name'] = _businessName;

      // Update user data in AuthManager
      await authManager.updateUserData(userData);

      // Queue the profile update for syncing
      final syncService = SyncService();
      await syncService.queueProfileUpdate(
        userId: userId,
        name: _nameController.text,
        email: _emailController.text,
        phone: widget.userProfile.phone,
      );

      // Get the user repository
      final userRepository = ServiceLocator().userRepository;

      // Make the API call to update the profile
      final response = await userRepository.updateProfile(
        userId,
        _nameController.text,
        _emailController.text,
      );

      // Check if the profile update was successful
      if (response.success) {
        // If GST information is provided, update it separately
        if (_gstNo != null && _gstNo!.isNotEmpty &&
            _businessName != null && _businessName!.isNotEmpty) {

          debugPrint('\n=== UPDATING GST INFORMATION ===');

          // Use ApiResponseHandler for GST update
          await _updateGSTInformation();
        }

        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Profile updated successfully'),
              backgroundColor: Color(0xFF67C44C),
              behavior: SnackBarBehavior.floating,
            ),
          );
          Navigator.pop(
            context,
            UserProfile(
              name: _nameController.text,
              email: _emailController.text,
              phone: widget.userProfile.phone, // Keep the original phone number
              gstNo: _gstNo,
              businessName: _businessName,
            ),
          );
        }
      } else {
        // Show error message
        throw Exception(response.message);
      }
    } catch (e) {
      if (mounted) {
        // Check if it's a network error
        final errorMessage = e.toString().toLowerCase();
        if (errorMessage.contains('connection') ||
            errorMessage.contains('network') ||
            errorMessage.contains('socket') ||
            errorMessage.contains('timeout')) {
          // Show a more user-friendly message for network errors
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                  'Network error. Your profile has been saved locally and will be updated when you reconnect.'),
              backgroundColor: Colors.orange,
              duration: Duration(seconds: 4),
            ),
          );

          // Return success anyway since we've saved the data locally
          Navigator.pop(
            context,
            UserProfile(
              name: _nameController.text,
              email: _emailController.text,
              phone: widget.userProfile.phone,
              gstNo: _gstNo,
              businessName: _businessName,
            ),
          );
        } else {
          // Show a generic error message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  'Error updating profile: ${e.toString().split(':').last}'),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 3),
            ),
          );
        }
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Update GST information using the dedicated API endpoint
  Future<void> _updateGSTInformation() async {
    if (_gstNo == null || _gstNo!.isEmpty ||
        _businessName == null || _businessName!.isEmpty) {
      return;
    }

    return ApiResponseHandler.handle<Map<String, dynamic>>(
      apiCall: () => _coreApiService.updateGstInfo(
        gstin: GSTFormatter.cleanGSTForAPI(_gstNo!),
        businessName: _businessName!,
      ),
      onSuccess: (data) async {
        debugPrint('GST information updated successfully');

        // Refresh profile data after successful GST update
        await _refreshProfileData();
      },
      onError: (message, errorCode) {
        debugPrint('Error updating GST information: $message');

        // Show error message for GST update failure
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('GST update failed: $message'),
              backgroundColor: Colors.orange,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      },
      onLoading: () {
        debugPrint('Updating GST information...');
      },
      showLoadingIndicator: false, // Don't show additional loading since we're already in loading state
    );
  }

  /// Refresh profile data after successful GST update
  Future<void> _refreshProfileData() async {
    try {
      // Reload profile data from API to verify GST update
      await _loadProfileData();
      debugPrint('Profile data refreshed after GST update');
    } catch (e) {
      debugPrint('Error refreshing profile data: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final backgroundColor =
        isDarkMode ? const Color(0xFF121212) : const Color(0xFFF8F9FA);
    final cardColor = isDarkMode ? const Color(0xFF1E1E1E) : Colors.white;
    final textColor = isDarkMode ? Colors.white : Colors.black;
    final secondaryTextColor = isDarkMode ? Colors.white70 : Colors.black87;
    final accentColor = const Color(0xFF67C44C);

    return Scaffold(
      backgroundColor: backgroundColor,
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(70),
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                cardColor,
                cardColor.withOpacity(0.95),
                cardColor.withOpacity(0.9),
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: ClipRRect(
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
              child: AppBar(
                backgroundColor: Colors.transparent,
                elevation: 0,
                scrolledUnderElevation: 0,
                leading: IconButton(
                  icon: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: cardColor.withOpacity(0.8),
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: isDarkMode
                            ? Colors.grey.shade700
                            : Colors.grey.shade300,
                        width: 1,
                      ),
                    ),
                    child: Icon(
                      Icons.arrow_back,
                      color: textColor,
                      size: 20,
                    ),
                  ),
                  onPressed: () => Navigator.pop(context),
                ),
                title: Text(
                  'Edit Profile',
                  style: TextStyle(
                    color: textColor,
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    letterSpacing: 0.5,
                  ),
                ),
                actions: [
                  if (_hasChanges)
                    Container(
                      margin: const EdgeInsets.only(right: 16),
                      child: TextButton(
                        onPressed: _isLoading ? null : _saveProfile,
                        style: TextButton.styleFrom(
                          backgroundColor: accentColor.withOpacity(0.1),
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 8),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(20),
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.save_outlined,
                              size: 16,
                              color: _isLoading ? Colors.grey : accentColor,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'Save',
                              style: TextStyle(
                                color: _isLoading ? Colors.grey : accentColor,
                                fontWeight: FontWeight.bold,
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
                flexibleSpace: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        cardColor.withOpacity(0.1),
                        cardColor.withOpacity(0.3),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SafeArea(
          child: SingleChildScrollView(
            physics: const BouncingScrollPhysics(),
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [


                // Section Title with Enhanced Styling
                SlideTransition(
                  position: Tween<Offset>(
                    begin: const Offset(-0.3, 0),
                    end: Offset.zero,
                  ).animate(CurvedAnimation(
                    parent: _slideController,
                    curve: Curves.easeOutCubic,
                  )),
                  child: Container(
                    margin: const EdgeInsets.fromLTRB(4, 8, 4, 16),
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          accentColor.withOpacity(0.1),
                          accentColor.withOpacity(0.05),
                        ],
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight,
                      ),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: accentColor.withOpacity(0.2),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: accentColor.withOpacity(0.15),
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            Icons.person_outline,
                            size: 20,
                            color: accentColor,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'Personal Information',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: textColor,
                            letterSpacing: 0.5,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                // Form Fields in a Card
                _buildGlassmorphicCard(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children: [
                        _buildTextField(
                          controller: _nameController,
                          label: 'Full Name',
                          icon: Icons.person_outline,
                          hintText: 'Enter your full name',
                        ),
                        const SizedBox(height: 20),
                        _buildTextField(
                          controller: _emailController,
                          label: 'Email Address',
                          icon: Icons.email_outlined,
                          keyboardType: TextInputType.emailAddress,
                          hintText: 'Enter your email address',
                        ),
                        // Phone number is displayed as read-only
                        const SizedBox(height: 20),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              vertical: 16, horizontal: 12),
                          decoration: BoxDecoration(
                            color: isDarkMode
                                ? Colors.grey.shade800.withAlpha(128)
                                : Colors.grey.shade100,
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: isDarkMode
                                  ? Colors.grey.shade700
                                  : Colors.grey.shade300,
                              width: 1,
                            ),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.phone_outlined,
                                color: isDarkMode
                                    ? Colors.grey.shade400
                                    : Colors.grey.shade600,
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Phone Number',
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: isDarkMode
                                            ? Colors.grey.shade400
                                            : Colors.grey.shade600,
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      widget.userProfile.phone,
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w500,
                                        color: secondaryTextColor,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Icon(
                                Icons.lock_outline,
                                size: 16,
                                color: isDarkMode
                                    ? Colors.grey.shade500
                                    : Colors.grey.shade400,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                // Show GST section only if user has existing GST data
                if (_hasGSTData()) ...[
                  const SizedBox(height: 24),

                  // Business Information Section Title with Enhanced Styling
                  SlideTransition(
                    position: Tween<Offset>(
                      begin: const Offset(-0.3, 0),
                      end: Offset.zero,
                    ).animate(CurvedAnimation(
                      parent: _slideController,
                      curve: Curves.easeOutCubic,
                    )),
                    child: Container(
                      margin: const EdgeInsets.only(left: 4, bottom: 20),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 12),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Colors.blue.withOpacity(0.1),
                            Colors.blue.withOpacity(0.05),
                          ],
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                        ),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Colors.blue.withOpacity(0.2),
                          width: 1,
                        ),
                      ),
                      child: Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: Colors.blue.withOpacity(0.15),
                              shape: BoxShape.circle,
                            ),
                            child: Icon(
                              Icons.business_outlined,
                              size: 20,
                              color: Colors.blue,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Text(
                            'Business Information',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: textColor,
                              letterSpacing: 0.5,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  // GST Details Section in a Card
                  _buildGlassmorphicCard(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: GSTInputWidget(
                        initialGstNo: _gstNo,
                        initialBusinessName: _businessName,
                        isExpanded: _gstExpanded,
                        onTap: _toggleGSTExpansion,
                        onChanged: _onGSTChanged,
                      ),
                    ),
                  ),
                ] else ...[
                  // Optional "Add GST Details" button when no GST data exists
                  const SizedBox(height: 24),
                  _buildGlassmorphicCard(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: GestureDetector(
                        onTap: _toggleGSTExpansion,
                        child: Container(
                          decoration: BoxDecoration(
                            color: isDarkMode ? Colors.grey.shade800 : Colors.grey[100],
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: isDarkMode ? Colors.grey.shade700 : Colors.grey.shade300,
                              width: 1,
                            ),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
                            child: Row(
                              children: [
                                Icon(
                                  Icons.receipt_long_outlined,
                                  color: isDarkMode ? Colors.grey.shade400 : Colors.grey[600],
                                  size: 22,
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: Text(
                                    'Add GST Details (Optional)',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500,
                                      color: isDarkMode ? Colors.grey.shade300 : Colors.grey[700],
                                    ),
                                  ),
                                ),
                                Icon(
                                  Icons.add,
                                  color: isDarkMode ? Colors.grey.shade500 : Colors.grey[400],
                                  size: 20,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),

                  // Show GSTInputWidget when user wants to add GST details
                  if (_gstExpanded)
                    _buildGlassmorphicCard(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: GSTInputWidget(
                          initialGstNo: _gstNo,
                          initialBusinessName: _businessName,
                          isExpanded: _gstExpanded,
                          onTap: _toggleGSTExpansion,
                          onChanged: _onGSTChanged,
                        ),
                      ),
                    ),
                ],



                const SizedBox(height: 32),

                // Save Button with Enhanced Animation
                AnimatedSwitcher(
                  duration: const Duration(milliseconds: 400),
                  transitionBuilder:
                      (Widget child, Animation<double> animation) {
                    return SlideTransition(
                      position: Tween<Offset>(
                        begin: const Offset(0, 0.3),
                        end: Offset.zero,
                      ).animate(CurvedAnimation(
                        parent: animation,
                        curve: Curves.elasticOut,
                      )),
                      child: ScaleTransition(
                        scale: Tween<double>(begin: 0.8, end: 1.0).animate(
                          CurvedAnimation(
                            parent: animation,
                            curve: Curves.easeOutBack,
                          ),
                        ),
                        child: FadeTransition(
                          opacity: animation,
                          child: child,
                        ),
                      ),
                    );
                  },
                  child: _hasChanges
                      ? Column(
                          key: const ValueKey('save_buttons'),
                          children: [
                            // Modern Save Button
                            Container(
                              width: double.infinity,
                              height: 60,
                              margin: const EdgeInsets.symmetric(
                                  horizontal: 0, vertical: 8),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(20),
                                gradient: LinearGradient(
                                  colors: _isLoading
                                      ? [
                                          Colors.grey.shade400,
                                          Colors.grey.shade500
                                        ]
                                      : [
                                          const Color(0xFF67C44C),
                                          const Color(0xFF5EB546),
                                        ],
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: (_isLoading
                                            ? Colors.grey
                                            : const Color(0xFF67C44C))
                                        .withOpacity(0.3),
                                    blurRadius: 12,
                                    offset: const Offset(0, 6),
                                  ),
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.1),
                                    blurRadius: 8,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: ElevatedButton(
                                onPressed: _isLoading ? null : _saveProfile,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.transparent,
                                  foregroundColor: Colors.white,
                                  elevation: 0,
                                  shadowColor: Colors.transparent,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                ),
                                child: _isLoading
                                    ? Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          SizedBox(
                                            width: 24,
                                            height: 24,
                                            child: CircularProgressIndicator(
                                              strokeWidth: 2.5,
                                              valueColor:
                                                  const AlwaysStoppedAnimation<
                                                      Color>(Colors.white),
                                            ),
                                          ),
                                          const SizedBox(width: 16),
                                          const Text(
                                            'Saving Changes...',
                                            style: TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.w600,
                                              color: Colors.white,
                                            ),
                                          ),
                                        ],
                                      )
                                    : Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Container(
                                            padding: const EdgeInsets.all(4),
                                            decoration: BoxDecoration(
                                              color:
                                                  Colors.white.withOpacity(0.2),
                                              shape: BoxShape.circle,
                                            ),
                                            child: const Icon(
                                              Icons.save_outlined,
                                              size: 20,
                                              color: Colors.white,
                                            ),
                                          ),
                                          const SizedBox(width: 12),
                                          const Text(
                                            'Save Changes',
                                            style: TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.w600,
                                              color: Colors.white,
                                              letterSpacing: 0.5,
                                            ),
                                          ),
                                        ],
                                      ),
                              ),
                            ),

                            const SizedBox(height: 16),

                            // Enhanced Reset Button
                            Container(
                              width: double.infinity,
                              height: 52,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(16),
                                border: Border.all(
                                  color: Colors.grey.shade400,
                                  width: 1.5,
                                ),
                                gradient: LinearGradient(
                                  colors: [
                                    cardColor,
                                    cardColor.withOpacity(0.8),
                                  ],
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                ),
                              ),
                              child: OutlinedButton(
                                onPressed: _isLoading ? null : _resetChanges,
                                style: OutlinedButton.styleFrom(
                                  backgroundColor: Colors.transparent,
                                  foregroundColor: Colors.grey[700],
                                  side: BorderSide.none,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(16),
                                  ),
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Container(
                                      padding: const EdgeInsets.all(4),
                                      decoration: BoxDecoration(
                                        color: Colors.grey.shade200
                                            .withOpacity(0.8),
                                        shape: BoxShape.circle,
                                      ),
                                      child: Icon(
                                        Icons.refresh_outlined,
                                        size: 18,
                                        color: Colors.grey[700],
                                      ),
                                    ),
                                    const SizedBox(width: 12),
                                    Text(
                                      'Reset Changes',
                                      style: TextStyle(
                                        fontSize: 15,
                                        fontWeight: FontWeight.w600,
                                        color: Colors.grey[700],
                                        letterSpacing: 0.3,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        )
                      : const SizedBox.shrink(),
                ),

                // Loading indicator when no changes but still loading
                if (_isLoading && !_hasChanges)
                  const Center(
                    child: CircularProgressIndicator(
                      valueColor:
                          AlwaysStoppedAnimation<Color>(Color(0xFF67C44C)),
                    ),
                  ),

                const SizedBox(height: 32),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    TextInputType keyboardType = TextInputType.text,
    bool isError = false,
    String? errorText,
    String? hintText,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final cardColor = isDarkMode ? Colors.grey.shade900 : Colors.white;
    final borderColor =
        isDarkMode ? Colors.grey.shade800 : Colors.grey.shade200;
    final textColor = isDarkMode ? Colors.white70 : Colors.black87;

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        color: cardColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isError ? Colors.red : borderColor,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 12, 16, 4),
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                color: isDarkMode ? Colors.grey.shade400 : Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          TextField(
            controller: controller,
            keyboardType: keyboardType,
            style: TextStyle(
              color: textColor,
              fontSize: 16,
            ),
            decoration: InputDecoration(
              hintText: hintText,
              hintStyle: TextStyle(
                color: isDarkMode ? Colors.grey.shade600 : Colors.grey.shade400,
                fontSize: 16,
              ),
              prefixIcon: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 12),
                child: Icon(
                  icon,
                  color:
                      isDarkMode ? Colors.grey.shade400 : Colors.grey.shade600,
                  size: 20,
                ),
              ),
              prefixIconConstraints: const BoxConstraints(minWidth: 44),
              contentPadding: const EdgeInsets.fromLTRB(16, 12, 16, 12),
              border: InputBorder.none,
              errorText: isError ? errorText : null,
              errorStyle: const TextStyle(
                color: Colors.red,
                fontSize: 12,
              ),
            ),
            onChanged: (_) => _checkForChanges(),
          ),
        ],
      ),
    );
  }



  Widget _buildGlassmorphicCard({required Widget child}) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final cardColor = isDarkMode ? Colors.grey.shade900 : Colors.white;

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        color: cardColor.withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: isDarkMode ? Colors.grey.shade800 : Colors.grey.shade200,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
          child: child,
        ),
      ),
    );
  }
}