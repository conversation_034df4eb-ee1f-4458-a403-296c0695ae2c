import 'package:flutter/material.dart';
import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../widgets/navigation_bar.dart';
import '../auth/auth_screen.dart';
import '../../services/marker_image_provider.dart';
import '../../features/auth/application/auth_notifier.dart';

class SplashScreen extends ConsumerStatefulWidget {
  const SplashScreen({super.key});

  @override
  ConsumerState<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends ConsumerState<SplashScreen>
    with SingleTickerProviderStateMixin {
  // Animation controller
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  // Marker image provider for essential preloading only
  final MarkerImageProvider _markerImageProvider = MarkerImageProvider();

  @override
  void initState() {
    super.initState();

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    // Create animations
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.5, curve: Curves.easeIn),
      ),
    );

    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.5, curve: Curves.easeOutQuart),
      ),
    );

    // Start the animation
    _animationController.forward();

    // Preload data and check authentication status
    _preloadDataAndCheckAuth();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// Preload data and check authentication status
  Future<void> _preloadDataAndCheckAuth() async {
    // Start preloading in background (don't wait for it)
    unawaited(_preloadMarkerImages());

    // Check authentication status immediately - no artificial delays
    await _checkAuthStatus();
  }




  /// Preload only essential resources (fast, non-blocking)
  Future<void> _preloadMarkerImages() async {
    try {
      // Only preload essential marker images - skip heavy API calls
      await _markerImageProvider.preloadCommonMarkers();
    } catch (e) {
      // Continue even if preloading fails - don't block app startup
    }
  }

  /// Check authentication status with proper waiting for initialization
  Future<void> _checkAuthStatus() async {
    try {
      debugPrint('🚀 Splash: Starting auth status check');

      // Get the current auth state
      final authState = ref.read(authProvider);

      // If still loading, wait a bit for initialization to complete
      if (authState.isLoading) {
        debugPrint('🚀 Splash: Auth state is loading, waiting for initialization...');

        // Wait up to 3 seconds for auth initialization
        int attempts = 0;
        const maxAttempts = 30; // 3 seconds with 100ms intervals

        while (attempts < maxAttempts) {
          await Future.delayed(const Duration(milliseconds: 100));
          final currentState = ref.read(authProvider);

          if (!currentState.isLoading) {
            debugPrint('🚀 Splash: Auth initialization completed after ${attempts * 100}ms');
            break;
          }
          attempts++;
        }

        if (attempts >= maxAttempts) {
          debugPrint('🚀 Splash: Auth initialization timeout, proceeding to auth screen');
        }
      }

      // Get the final auth state
      final finalAuthState = ref.read(authProvider);
      debugPrint('🚀 Splash: Final auth state - isLoading: ${finalAuthState.isLoading}');

      // Handle navigation based on final auth state
      finalAuthState.when(
        data: (state) {
          debugPrint('🚀 Splash: Auth state data - isAuthenticated: ${state.isAuthenticated}');
          // Schedule navigation for next frame to avoid navigation errors
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (!mounted) return;

            if (state.isAuthenticated && state.user != null) {
              debugPrint('🚀 Splash: User is authenticated, navigating to dashboard');
              Navigator.of(context).pushReplacement(
                MaterialPageRoute(builder: (context) => const MainNavigation()),
              );
            } else {
              debugPrint('🚀 Splash: User is not authenticated, navigating to auth screen');
              Navigator.of(context).pushReplacement(
                MaterialPageRoute(builder: (context) => const AuthScreen()),
              );
            }
          });
        },
        loading: () {
          debugPrint('🚀 Splash: Auth state still loading, navigating to auth screen');
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (!mounted) return;
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(builder: (context) => const AuthScreen()),
            );
          });
        },
        error: (error, stackTrace) {
          debugPrint('🚀 Splash: Auth state error: $error');
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (!mounted) return;
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(builder: (context) => const AuthScreen()),
            );
          });
        },
      );
    } catch (e) {
      debugPrint('🚀 Splash: Error checking auth status: $e');
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (!mounted) return;
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const AuthScreen()),
        );
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // Always use dark theme for splash screen

    return Scaffold(
      backgroundColor: const Color(0xFF121212), // Always use dark theme
      body: Center(
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: ScaleTransition(
            scale: _scaleAnimation,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Pure logo image without any background or container
                Image.asset(
                  'assets/images/ecoplug_logo_dark_fixed.png', // Light logo for dark background
                  width: 150,
                  height: 150,
                  fit: BoxFit.scaleDown, // Preserves transparency and prevents background
                  filterQuality: FilterQuality.high, // High quality rendering
                  isAntiAlias: true, // Smooth edges
                ),

                const SizedBox(height: 24),

                // App name
                const Text(
                  'Ecoplug',
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF34C759), // Green color
                  ),
                ),

                const SizedBox(height: 8),

                // Tagline
                const Text(
                  'Charging the future, sustainably',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey,
                  ),
                ),

                const SizedBox(height: 48),

                // Loading indicator
                const SizedBox(
                  width: 40,
                  height: 40,
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(
                        Color(0xFF34C759)), // Green color
                    strokeWidth: 3,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
