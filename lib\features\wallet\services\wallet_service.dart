import 'dart:async';
import 'package:flutter/foundation.dart';

import '../../../repositories/wallet_repository.dart';
import '../../../models/wallet/wallet_models.dart';
import '../../../models/api_response.dart';
import '../../../core/services/connectivity_service.dart';

/// Service for handling wallet-related operations
class WalletService {
  final WalletRepository _walletRepository;
  final ConnectivityService _connectivityService;

  // Cache for wallet data
  WalletInfo? _cachedWalletInfo;
  DateTime? _walletInfoLastFetched;

  // Stream controller for wallet updates
  final _walletUpdateController = StreamController<WalletInfo>.broadcast();

  // Stream for wallet updates
  Stream<WalletInfo> get walletUpdates => _walletUpdateController.stream;

  WalletService({
    required WalletRepository walletRepository,
    required ConnectivityService connectivityService,
  })  : _walletRepository = walletRepository,
        _connectivityService = connectivityService;

  /// Get wallet information
  /// Uses caching to reduce API calls
  Future<ApiResponse<WalletInfo>> getWalletInfo(
      {bool forceRefresh = false}) async {
    // Check if we have cached data and it's not too old (less than 5 minutes)
    final now = DateTime.now();
    final cacheValid = _cachedWalletInfo != null &&
        _walletInfoLastFetched != null &&
        now.difference(_walletInfoLastFetched!).inMinutes < 5;

    // Return cached data if valid and not forcing refresh
    if (cacheValid && !forceRefresh) {
      return ApiResponse<WalletInfo>(
        success: true,
        message: 'Wallet info retrieved from cache',
        data: _cachedWalletInfo!,
      );
    }

    // Check connectivity before making API call
    final isConnected = await _connectivityService.checkConnectionManually();
    if (!isConnected) {
      // Return cached data if available, even if old
      if (_cachedWalletInfo != null) {
        return ApiResponse<WalletInfo>(
          success: true,
          message: 'Using cached wallet info (offline)',
          data: _cachedWalletInfo!,
        );
      } else {
        return ApiResponse<WalletInfo>(
          success: false,
          message: 'No internet connection and no cached data available',
        );
      }
    }

    // Make API call
    final response = await _walletRepository.getWalletInfo();

    // Update cache if successful
    if (response.success && response.data != null) {
      _cachedWalletInfo = response.data;
      _walletInfoLastFetched = now;

      // Notify listeners
      _walletUpdateController.add(response.data!);
    }

    return response;
  }

  /// Add money to wallet
  /// Note: Payment gateway integration has been removed as per user requirements
  Future<ApiResponse<bool>> addMoney(double amount) async {
    try {
      // Validate amount - REMOVED MINIMUM RESTRICTION
      if (amount < 1) {
        return ApiResponse<bool>(
          success: false,
          message: 'Amount must be at least ₹1',
          data: false,
        );
      }

      if (amount > 50000) {
        return ApiResponse<bool>(
          success: false,
          message: 'Amount cannot exceed ₹50,000',
          data: false,
        );
      }

      debugPrint('Attempting to add money: ₹$amount');

      // Check connectivity before making API call
      final isConnected = await _connectivityService.checkConnectionManually();
      if (!isConnected) {
        return ApiResponse<bool>(
          success: false,
          message: 'No internet connection',
          data: false,
        );
      }

      // Add money through repository
      final response = await _walletRepository.addMoney(amount);

      if (response.success) {
        debugPrint('✅ Money added successfully: ₹$amount');
        // Force refresh wallet info after successful addition
        await getWalletInfo(forceRefresh: true);
      } else {
        debugPrint('❌ Failed to add money: ${response.message}');
      }

      return response;
    } catch (e) {
      debugPrint('❌ Error in WalletService.addMoney: $e');
      return ApiResponse<bool>(
        success: false,
        message: 'Failed to add money: $e',
        data: false,
      );
    }
  }

  /// Get charging sessions history
  Future<ApiResponse<List<ChargingSession>>> getChargingSessionHistory() async {
    // Check connectivity before making API call
    final isConnected = await _connectivityService.checkConnectionManually();
    if (!isConnected) {
      return ApiResponse<List<ChargingSession>>(
        success: false,
        message: 'No internet connection',
      );
    }

    return await _walletRepository.getChargingSessionHistory();
  }

  /// Get ongoing charging sessions
  Future<ApiResponse<List<ChargingSession>>> getOngoingSessions() async {
    // Check connectivity before making API call
    final isConnected = await _connectivityService.checkConnectionManually();
    if (!isConnected) {
      return ApiResponse<List<ChargingSession>>(
        success: false,
        message: 'No internet connection',
      );
    }

    return await _walletRepository.getOngoingSessions();
  }

  /// Get billing details for a transaction
  Future<ApiResponse<BillingDetails>> getBillingDetails(
      String transactionId) async {
    // Check connectivity before making API call
    final isConnected = await _connectivityService.checkConnectionManually();
    if (!isConnected) {
      return ApiResponse<BillingDetails>(
        success: false,
        message: 'No internet connection',
      );
    }

    return await _walletRepository.getBillingDetails(transactionId);
  }

  /// Start a charging transaction
  Future<ApiResponse<String>> startTransaction(
    String stationId,
    String connectorId, {
    String? vehicleId,
    String? promocodeId,
  }) async {
    // Check connectivity before making API call
    final isConnected = await _connectivityService.checkConnectionManually();
    if (!isConnected) {
      return ApiResponse<String>(
        success: false,
        message: 'No internet connection',
      );
    }

    return await _walletRepository.startTransaction(
      stationId,
      connectorId,
      vehicleId,
      promocodeId,
    );
  }

  /// Stop a charging transaction
  Future<ApiResponse<BillingDetails>> stopTransaction(
      String transactionId) async {
    // Check connectivity before making API call
    final isConnected = await _connectivityService.checkConnectionManually();
    if (!isConnected) {
      return ApiResponse<BillingDetails>(
        success: false,
        message: 'No internet connection',
      );
    }

    return await _walletRepository.stopTransaction(transactionId);
  }

  /// Clear wallet cache
  void clearCache() {
    _cachedWalletInfo = null;
    _walletInfoLastFetched = null;
    debugPrint('Wallet cache cleared');
  }

  /// Dispose resources
  void dispose() {
    _walletUpdateController.close();
  }
}
