import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'auth_manager.dart';
import 'token_service.dart';
import '../features/profile/application/profile_notifier_riverpod.dart'
    as riverpod_notifier;
import '../features/profile/application/profile_notifier.dart'
    as state_notifier;

/// Comprehensive logout service that ensures ALL user data is cleared
/// This service coordinates logout across all data persistence mechanisms
class LogoutService {
  // Singleton pattern
  static final LogoutService _instance = LogoutService._internal();
  factory LogoutService() => _instance;
  LogoutService._internal();

  // Services
  final AuthManager _authManager = AuthManager();
  final TokenService _tokenService = TokenService();
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();

  /// CRITICAL: Comprehensive logout that clears ALL user data
  Future<bool> performCompleteLogout() async {
    try {
      debugPrint('🚪 STARTING COMPREHENSIVE LOGOUT PROCESS');

      // Step 1: Clear all authentication tokens
      await _clearAllTokens();

      // Step 2: Clear all SharedPreferences data
      await _clearAllSharedPreferences();

      // Step 3: Clear all secure storage
      await _clearAllSecureStorage();

      // Step 4: Clear all in-memory caches
      await _clearAllInMemoryCaches();

      // Step 5: Clear all service-specific data
      await _clearAllServiceData();

      debugPrint('✅ COMPREHENSIVE LOGOUT COMPLETED SUCCESSFULLY');
      return true;
    } catch (e) {
      debugPrint('❌ ERROR DURING COMPREHENSIVE LOGOUT: $e');
      return false;
    }
  }

  /// Step 1: Clear all authentication tokens
  Future<void> _clearAllTokens() async {
    debugPrint('🔑 CLEARING ALL AUTHENTICATION TOKENS');

    try {
      // Clear token from TokenService
      await _tokenService.clearToken();

      // Clear token from AuthManager
      await _authManager.logout();

      debugPrint('✅ ALL AUTHENTICATION TOKENS CLEARED');
    } catch (e) {
      debugPrint('⚠️ Error clearing tokens: $e');
    }
  }

  /// Step 2: Clear all SharedPreferences data
  Future<void> _clearAllSharedPreferences() async {
    debugPrint('🗂️ CLEARING ALL SHARED PREFERENCES DATA');

    try {
      final prefs = await SharedPreferences.getInstance();

      // Get all keys and clear user-related data
      final keys = prefs.getKeys();
      final userRelatedKeys = keys
          .where((key) =>
              key.startsWith('user_') ||
              key.startsWith('auth_') ||
              key.startsWith('profile_') ||
              key.startsWith('wallet_') ||
              key.startsWith('charging_') ||
              key.startsWith('last_') ||
              key.startsWith('pending_') ||
              key.contains('token') ||
              key.contains('login') ||
              key.contains('otp'))
          .toList();

      // Clear all user-related keys
      for (final key in userRelatedKeys) {
        await prefs.remove(key);
        debugPrint('🗑️ Removed SharedPreferences key: $key');
      }

      // Also clear specific known keys
      final specificKeys = [
        'is_logged_in',
        'user_data',
        'auth_token',
        'last_login_time',
        'user_id',
        'user_name',
        'user_email',
        'user_phone',
        'user_token',
        'last_phone_number',
        'last_otp_value',
        'last_otp_time',
        'profile_data',
        'pending_profile_update',
      ];

      for (final key in specificKeys) {
        await prefs.remove(key);
      }

      debugPrint('✅ ALL SHARED PREFERENCES DATA CLEARED');
    } catch (e) {
      debugPrint('⚠️ Error clearing SharedPreferences: $e');
    }
  }

  /// Step 3: Clear all secure storage
  Future<void> _clearAllSecureStorage() async {
    debugPrint('🔒 CLEARING ALL SECURE STORAGE DATA');

    try {
      // Clear all secure storage data
      await _secureStorage.deleteAll();
      debugPrint('✅ ALL SECURE STORAGE DATA CLEARED');
    } catch (e) {
      debugPrint('⚠️ Error clearing secure storage: $e');
    }
  }

  /// Step 4: Clear all in-memory caches
  Future<void> _clearAllInMemoryCaches() async {
    debugPrint('🧠 CLEARING ALL IN-MEMORY CACHES');

    try {
      // Clear profile notifier caches from both notifier types
      riverpod_notifier.ProfileNotifier.clearAllCaches();
      state_notifier.ProfileNotifier.clearAllCaches();

      debugPrint('✅ ALL IN-MEMORY CACHES CLEARED');
    } catch (e) {
      debugPrint('⚠️ Error clearing in-memory caches: $e');
    }
  }

  /// Step 5: Clear all service-specific data
  Future<void> _clearAllServiceData() async {
    debugPrint('🔧 CLEARING ALL SERVICE-SPECIFIC DATA');

    try {
      // Clear sync service data
      // Note: SyncService might have its own cleanup methods

      debugPrint('✅ ALL SERVICE-SPECIFIC DATA CLEARED');
    } catch (e) {
      debugPrint('⚠️ Error clearing service data: $e');
    }
  }

  /// Force refresh all providers after login
  Future<void> forceRefreshAllProviders() async {
    debugPrint('🔄 FORCING REFRESH OF ALL PROVIDERS AFTER LOGIN');

    try {
      // This method will be called after successful login
      // to ensure fresh data is loaded for the new user

      debugPrint('✅ ALL PROVIDERS REFRESHED');
    } catch (e) {
      debugPrint('⚠️ Error refreshing providers: $e');
    }
  }

  /// Validate that logout was successful
  Future<bool> validateLogoutSuccess() async {
    debugPrint('✅ VALIDATING LOGOUT SUCCESS');

    try {
      // Check if any tokens remain
      final token = await _tokenService.getToken();
      if (token != null && token.isNotEmpty) {
        debugPrint('❌ VALIDATION FAILED: Token still exists');
        return false;
      }

      // Check if user is still logged in
      final isLoggedIn = await _authManager.isLoggedIn();
      if (isLoggedIn) {
        debugPrint('❌ VALIDATION FAILED: User still appears logged in');
        return false;
      }

      // Check if user data still exists in SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      final userData = prefs.getString('user_data');
      if (userData != null && userData.isNotEmpty) {
        debugPrint(
            '❌ VALIDATION FAILED: User data still exists in SharedPreferences');
        return false;
      }

      debugPrint('✅ LOGOUT VALIDATION SUCCESSFUL - NO STALE DATA FOUND');
      return true;
    } catch (e) {
      debugPrint('⚠️ Error during logout validation: $e');
      return false;
    }
  }
}
