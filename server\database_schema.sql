-- Enhanced Database Schema for PayU Payment Integration
-- This file contains the required database structure for proper PayU payment handling

-- =====================================================
-- PAYMENT HISTORY TABLE
-- =====================================================
-- Ensure payment_history table has all required fields for PayU integration

-- Check if payment_history table exists and create if not
CREATE TABLE IF NOT EXISTS `payment_history` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `status` enum('PENDING','COMPLETED','REJECTED','CANCELLED','TIMEOUT') NOT NULL DEFAULT 'PENDING',
  `payment_method` varchar(50) NOT NULL DEFAULT 'payu',
  `txnid` varchar(100) DEFAULT NULL,
  `transaction_id` varchar(100) DEFAULT NULL,
  `reference_number` varchar(100) DEFAULT NULL,
  `remark` text,
  
  -- PayU specific fields
  `payu_response` longtext,
  `payu_hash` varchar(255) DEFAULT NULL,
  `payu_status` varchar(50) DEFAULT NULL,
  `payu_mihpayid` varchar(100) DEFAULT NULL,
  `payu_mode` varchar(50) DEFAULT NULL,
  `payu_bankcode` varchar(50) DEFAULT NULL,
  `payu_cardnum` varchar(50) DEFAULT NULL,
  `payu_bank_ref_num` varchar(100) DEFAULT NULL,
  `payu_error` varchar(255) DEFAULT NULL,
  `payu_error_message` text,
  
  -- Timestamps
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `processed_at` timestamp NULL DEFAULT NULL,
  
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_txnid` (`txnid`),
  KEY `idx_transaction_id` (`transaction_id`),
  KEY `idx_reference_number` (`reference_number`),
  KEY `idx_status` (`status`),
  KEY `idx_payment_method` (`payment_method`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_payu_mihpayid` (`payu_mihpayid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add missing columns to existing payment_history table
ALTER TABLE `payment_history` 
ADD COLUMN IF NOT EXISTS `payu_response` longtext AFTER `remark`,
ADD COLUMN IF NOT EXISTS `payu_hash` varchar(255) DEFAULT NULL AFTER `payu_response`,
ADD COLUMN IF NOT EXISTS `payu_status` varchar(50) DEFAULT NULL AFTER `payu_hash`,
ADD COLUMN IF NOT EXISTS `payu_mihpayid` varchar(100) DEFAULT NULL AFTER `payu_status`,
ADD COLUMN IF NOT EXISTS `payu_mode` varchar(50) DEFAULT NULL AFTER `payu_mihpayid`,
ADD COLUMN IF NOT EXISTS `payu_bankcode` varchar(50) DEFAULT NULL AFTER `payu_mode`,
ADD COLUMN IF NOT EXISTS `payu_cardnum` varchar(50) DEFAULT NULL AFTER `payu_bankcode`,
ADD COLUMN IF NOT EXISTS `payu_bank_ref_num` varchar(100) DEFAULT NULL AFTER `payu_cardnum`,
ADD COLUMN IF NOT EXISTS `payu_error` varchar(255) DEFAULT NULL AFTER `payu_bank_ref_num`,
ADD COLUMN IF NOT EXISTS `payu_error_message` text AFTER `payu_error`,
ADD COLUMN IF NOT EXISTS `processed_at` timestamp NULL DEFAULT NULL AFTER `updated_at`;

-- Add missing indexes
ALTER TABLE `payment_history` 
ADD INDEX IF NOT EXISTS `idx_txnid` (`txnid`),
ADD INDEX IF NOT EXISTS `idx_transaction_id` (`transaction_id`),
ADD INDEX IF NOT EXISTS `idx_reference_number` (`reference_number`),
ADD INDEX IF NOT EXISTS `idx_payu_mihpayid` (`payu_mihpayid`);

-- =====================================================
-- WALLETS TABLE
-- =====================================================
-- Ensure wallets table exists for balance management

CREATE TABLE IF NOT EXISTS `wallets` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL UNIQUE,
  `balance` decimal(10,2) NOT NULL DEFAULT '0.00',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_wallet` (`user_id`),
  KEY `idx_balance` (`balance`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- PAYMENT LOGS TABLE (for debugging and monitoring)
-- =====================================================
-- Create a separate table for detailed payment logs

CREATE TABLE IF NOT EXISTS `payment_logs` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `request_id` varchar(100) NOT NULL,
  `txnid` varchar(100) DEFAULT NULL,
  `user_id` bigint(20) DEFAULT NULL,
  `payment_method` varchar(50) NOT NULL,
  `log_level` enum('INFO','WARNING','ERROR','DEBUG') NOT NULL DEFAULT 'INFO',
  `message` text NOT NULL,
  `request_data` longtext,
  `response_data` longtext,
  `processing_time_ms` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  
  PRIMARY KEY (`id`),
  KEY `idx_request_id` (`request_id`),
  KEY `idx_txnid` (`txnid`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_payment_method` (`payment_method`),
  KEY `idx_log_level` (`log_level`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- STORED PROCEDURES FOR PAYMENT PROCESSING
-- =====================================================

DELIMITER //

-- Procedure to safely update wallet balance
CREATE PROCEDURE IF NOT EXISTS `UpdateWalletBalance`(
    IN p_user_id BIGINT,
    IN p_amount DECIMAL(10,2),
    IN p_transaction_id BIGINT,
    OUT p_success BOOLEAN,
    OUT p_new_balance DECIMAL(10,2)
)
BEGIN
    DECLARE v_current_balance DECIMAL(10,2) DEFAULT 0.00;
    DECLARE v_error_count INT DEFAULT 0;
    
    DECLARE CONTINUE HANDLER FOR SQLEXCEPTION 
    BEGIN
        SET p_success = FALSE;
        ROLLBACK;
    END;
    
    START TRANSACTION;
    
    -- Get current balance with lock
    SELECT COALESCE(balance, 0.00) INTO v_current_balance 
    FROM wallets 
    WHERE user_id = p_user_id 
    FOR UPDATE;
    
    -- Calculate new balance
    SET p_new_balance = v_current_balance + p_amount;
    
    -- Update or insert wallet record
    INSERT INTO wallets (user_id, balance, created_at, updated_at) 
    VALUES (p_user_id, p_new_balance, NOW(), NOW())
    ON DUPLICATE KEY UPDATE 
        balance = p_new_balance,
        updated_at = NOW();
    
    -- Update payment history to mark as processed
    UPDATE payment_history 
    SET processed_at = NOW() 
    WHERE id = p_transaction_id;
    
    SET p_success = TRUE;
    COMMIT;
END //

-- Procedure to find transaction by various ID fields
CREATE PROCEDURE IF NOT EXISTS `FindTransactionByTxnId`(
    IN p_txnid VARCHAR(100),
    OUT p_transaction_id BIGINT,
    OUT p_found BOOLEAN
)
BEGIN
    DECLARE v_count INT DEFAULT 0;
    
    -- Try direct txnid match first
    SELECT id INTO p_transaction_id 
    FROM payment_history 
    WHERE txnid = p_txnid 
    LIMIT 1;
    
    SELECT FOUND_ROWS() INTO v_count;
    
    IF v_count = 0 THEN
        -- Try transaction_id field
        SELECT id INTO p_transaction_id 
        FROM payment_history 
        WHERE transaction_id = p_txnid 
        LIMIT 1;
        
        SELECT FOUND_ROWS() INTO v_count;
    END IF;
    
    IF v_count = 0 THEN
        -- Try reference_number field
        SELECT id INTO p_transaction_id 
        FROM payment_history 
        WHERE reference_number = p_txnid 
        LIMIT 1;
        
        SELECT FOUND_ROWS() INTO v_count;
    END IF;
    
    SET p_found = (v_count > 0);
END //

DELIMITER ;

-- =====================================================
-- VIEWS FOR REPORTING
-- =====================================================

-- View for payment statistics
CREATE OR REPLACE VIEW `payment_statistics` AS
SELECT 
    DATE(created_at) as payment_date,
    payment_method,
    status,
    COUNT(*) as transaction_count,
    SUM(amount) as total_amount,
    AVG(amount) as average_amount,
    MIN(amount) as min_amount,
    MAX(amount) as max_amount
FROM payment_history 
GROUP BY DATE(created_at), payment_method, status
ORDER BY payment_date DESC, payment_method, status;

-- View for PayU specific transactions
CREATE OR REPLACE VIEW `payu_transactions` AS
SELECT 
    id,
    user_id,
    amount,
    status,
    txnid,
    payu_status,
    payu_mihpayid,
    payu_mode,
    payu_bankcode,
    payu_error,
    created_at,
    updated_at,
    processed_at
FROM payment_history 
WHERE payment_method = 'payu'
ORDER BY created_at DESC;

-- =====================================================
-- SAMPLE DATA FOR TESTING (optional)
-- =====================================================

-- Insert sample test data (uncomment if needed for testing)
/*
INSERT INTO payment_history (user_id, amount, status, payment_method, txnid, remark) VALUES
(1, 100.00, 'PENDING', 'payu', 'TEST_TXN_001', 'Test transaction 1'),
(1, 250.00, 'COMPLETED', 'payu', 'TEST_TXN_002', 'Test transaction 2'),
(2, 500.00, 'REJECTED', 'payu', 'TEST_TXN_003', 'Test transaction 3');

INSERT INTO wallets (user_id, balance) VALUES
(1, 350.00),
(2, 0.00);
*/
