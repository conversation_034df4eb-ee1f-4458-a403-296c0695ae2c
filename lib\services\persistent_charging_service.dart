import 'dart:async';
import 'dart:io';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:ecoplug/services/charging_session_service.dart';
import 'package:ecoplug/models/charging_session.dart';

/// Persistent Charging Service
///
/// Manages charging session persistence across all app states:
/// - Foreground (app is active)
/// - Background (app is minimized but running)
/// - Terminated (app is completely closed)
///
/// Key Features:
/// - Starts Android foreground service for background operation
/// - Manages charging session state persistence
/// - Handles background API data polling
/// - Provides deep linking support for notification taps
/// - Automatic service lifecycle management
class PersistentChargingService {
  static final PersistentChargingService _instance =
      PersistentChargingService._internal();
  factory PersistentChargingService() => _instance;
  PersistentChargingService._internal();

  // Method channels for native communication
  static const MethodChannel _backgroundServiceChannel =
      MethodChannel('charging_background_service');
  static const MethodChannel _customNotificationChannel =
      MethodChannel('custom_charging_notification');

  // Service state
  bool _isInitialized = false;
  bool _isChargingSessionActive = false;
  ChargingSession? _currentSession;
  Timer? _dataPollingTimer;

  // Charging session data
  String? _stationUid;
  String? _connectorId;
  String? _transactionId;
  String? _authorizationReference;

  // Callbacks for session updates
  Function(Map<String, dynamic>)? _onDataReceived;
  Function(String)? _onError;
  Function()? _onSessionComplete;

  // Services
  final ChargingSessionService _chargingService = ChargingSessionService();

  /// Initialize the persistent charging service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint('🔋 ===== INITIALIZING PERSISTENT CHARGING SERVICE =====');

      // Set up method channel handlers
      _setupMethodChannelHandlers();

      // Check for existing charging session
      await _checkForExistingSession();

      _isInitialized = true;
      debugPrint('✅ Persistent charging service initialized successfully');
    } catch (e) {
      debugPrint('❌ Error initializing persistent charging service: $e');
      rethrow;
    }
  }

  /// Start persistent charging session
  Future<void> startPersistentChargingSession({
    required String stationUid,
    required String connectorId,
    required String transactionId,
    required String authorizationReference,
    required Function(Map<String, dynamic>) onDataReceived,
    required Function(String) onError,
    required Function() onSessionComplete,
  }) async {
    try {
      debugPrint('🔋 ===== STARTING PERSISTENT CHARGING SESSION =====');
      debugPrint('🔋 Station: $stationUid');
      debugPrint('🔋 Connector: $connectorId');
      debugPrint('🔋 Transaction ID: $transactionId');
      debugPrint('🔋 Auth Reference: $authorizationReference');

      // Store session data
      _stationUid = stationUid;
      _connectorId = connectorId;
      _transactionId = transactionId;
      _authorizationReference = authorizationReference;
      _onDataReceived = onDataReceived;
      _onError = onError;
      _onSessionComplete = onSessionComplete;

      // Save session data to persistent storage
      await _saveSessionData();

      // Start Android foreground service
      if (Platform.isAndroid) {
        await _startAndroidForegroundService();
      }

      // Start data polling
      await _startDataPolling();

      _isChargingSessionActive = true;

      debugPrint('✅ Persistent charging session started successfully');
    } catch (e) {
      debugPrint('❌ Error starting persistent charging session: $e');
      _onError?.call('Failed to start persistent charging session: $e');
    }
  }

  /// Stop persistent charging session
  Future<void> stopPersistentChargingSession() async {
    try {
      debugPrint('🔋 ===== STOPPING PERSISTENT CHARGING SESSION =====');

      // Stop data polling
      _stopDataPolling();

      // Stop Android foreground service
      if (Platform.isAndroid) {
        await _stopAndroidForegroundService();
      }

      // Clear session data
      await _clearSessionData();

      _isChargingSessionActive = false;
      _currentSession = null;

      debugPrint('✅ Persistent charging session stopped successfully');
    } catch (e) {
      debugPrint('❌ Error stopping persistent charging session: $e');
    }
  }

  /// Update charging session data
  Future<void> updateChargingData(Map<String, dynamic> data) async {
    try {
      if (!_isChargingSessionActive) return;

      debugPrint('🔋 Updating charging data: $data');

      // Extract data
      final chargePercentage =
          (data['chargePercentage'] as num?)?.toDouble() ?? 0.0;
      final currentPower = data['currentPower'] as String? ?? '0.0 kW';
      final energyDelivered = data['energyDelivered'] as String? ?? '0.00 kWh';
      final cost = data['cost'] as String? ?? '₹0.00';
      final co2Saved = data['co2Saved'] as String? ?? '0.0 kg';
      final chargingTimer = data['chargingTimer'] as String? ?? '00:00:00';

      // Update Android foreground service
      if (Platform.isAndroid) {
        await _updateAndroidForegroundService(
          chargePercentage: chargePercentage,
          currentPower: currentPower,
          energyDelivered: energyDelivered,
          cost: cost,
          co2Saved: co2Saved,
          chargingTimer: chargingTimer,
        );
      }

      // Update session data in storage
      await _updateSessionDataInStorage(data);

      // Notify callbacks
      _onDataReceived?.call(data);

      debugPrint('✅ Charging data updated successfully');
    } catch (e) {
      debugPrint('❌ Error updating charging data: $e');
    }
  }

  /// Check if charging session is active
  bool get isChargingSessionActive => _isChargingSessionActive;

  /// Get current session
  ChargingSession? get currentSession => _currentSession;

  /// Handle app state changes
  Future<void> handleAppStateChange(AppLifecycleState state) async {
    debugPrint('🔋 App state changed to: $state');

    switch (state) {
      case AppLifecycleState.resumed:
        // App came to foreground
        await _handleAppResumed();
        break;
      case AppLifecycleState.paused:
        // App went to background
        await _handleAppPaused();
        break;
      case AppLifecycleState.detached:
        // App is being terminated
        await _handleAppTerminated();
        break;
      default:
        break;
    }
  }

  /// Setup method channel handlers
  void _setupMethodChannelHandlers() {
    debugPrint('🔋 Setting up method channel handlers');

    // Handle background service communication
    _backgroundServiceChannel.setMethodCallHandler((call) async {
      switch (call.method) {
        case 'pollChargingData':
          return await _handleBackgroundPollingRequest(call.arguments);
        case 'onChargingComplete':
          await _handleChargingComplete();
          break;
        case 'onChargingError':
          await _handleChargingError(call.arguments['error']);
          break;
        default:
          debugPrint('⚠️ Unknown method call: ${call.method}');
      }
    });

    debugPrint('✅ Method channel handlers setup complete');
  }

  /// Handle background polling request from Android service
  Future<Map<String, dynamic>> _handleBackgroundPollingRequest(
      Map<String, dynamic> arguments) async {
    try {
      debugPrint('📊 Handling background polling request');

      final authReference = arguments['authReference'] as String?;
      final transactionId = arguments['transactionId'] as String?;

      if (authReference == null || transactionId == null) {
        throw Exception('Missing auth reference or transaction ID');
      }

      // Fetch charging data using existing service
      final result =
          await _fetchChargingDataFromAPI(authReference, transactionId);

      debugPrint('📊 Background polling result: $result');
      return result;
    } catch (e) {
      debugPrint('❌ Error in background polling: $e');
      return {
        'error': e.toString(),
        'isComplete': false,
      };
    }
  }

  /// Fetch charging data from API
  Future<Map<String, dynamic>> _fetchChargingDataFromAPI(
      String authReference, String transactionId) async {
    try {
      debugPrint('📊 Fetching charging data from API');
      debugPrint('📊 Auth Reference: $authReference');
      debugPrint('📊 Transaction ID: $transactionId');

      // Use existing charging service to fetch data
      final response = await _chargingService.getChargingData(
        authorizationReference: authReference,
      );

      if (response.success && response.data != null) {
        final data = response.data!;

        // Extract and format data
        final chargePercentage =
            (data['chargePercentage'] as num?)?.toDouble() ?? 0.0;
        final currentPower = data['currentPower'] as String? ?? '0.0 kW';
        final energyDelivered =
            data['energyDelivered'] as String? ?? '0.00 kWh';
        final cost = data['cost'] as String? ?? '₹0.00';
        final co2Saved = data['co2Saved'] as String? ?? '0.0 kg';
        final chargingTimer = data['chargingTimer'] as String? ?? '00:00:00';
        final isComplete = data['isComplete'] as bool? ?? false;

        return {
          'chargePercentage': chargePercentage,
          'currentPower': currentPower,
          'energyDelivered': energyDelivered,
          'cost': cost,
          'co2Saved': co2Saved,
          'chargingTimer': chargingTimer,
          'isComplete': isComplete,
        };
      } else {
        throw Exception(response.message.isNotEmpty
            ? response.message
            : 'Failed to fetch charging data');
      }
    } catch (e) {
      debugPrint('❌ Error fetching charging data from API: $e');
      rethrow;
    }
  }

  /// Start Android foreground service
  Future<void> _startAndroidForegroundService() async {
    try {
      debugPrint('🔋 Starting Android foreground service');

      await _customNotificationChannel.invokeMethod('startForegroundService', {
        'stationUid': _stationUid,
        'connectorId': _connectorId,
        'transactionId': _transactionId,
        'authReference': _authorizationReference,
      });

      debugPrint('✅ Android foreground service started');
    } catch (e) {
      debugPrint('❌ Error starting Android foreground service: $e');
      rethrow;
    }
  }

  /// Stop Android foreground service
  Future<void> _stopAndroidForegroundService() async {
    try {
      debugPrint('🔋 Stopping Android foreground service');

      await _customNotificationChannel.invokeMethod('stopForegroundService');

      debugPrint('✅ Android foreground service stopped');
    } catch (e) {
      debugPrint('❌ Error stopping Android foreground service: $e');
    }
  }

  /// Update Android foreground service with new data
  Future<void> _updateAndroidForegroundService({
    required double chargePercentage,
    required String currentPower,
    required String energyDelivered,
    required String cost,
    required String co2Saved,
    required String chargingTimer,
  }) async {
    try {
      await _customNotificationChannel.invokeMethod('updateForegroundService', {
        'chargePercentage': chargePercentage,
        'currentPower': currentPower,
        'energyDelivered': energyDelivered,
        'cost': cost,
        'co2Saved': co2Saved,
        'chargingTimer': chargingTimer,
      });
    } catch (e) {
      debugPrint('❌ Error updating Android foreground service: $e');
    }
  }

  /// Start data polling
  Future<void> _startDataPolling() async {
    if (_authorizationReference == null || _transactionId == null) {
      debugPrint(
          '❌ Cannot start data polling: missing auth reference or transaction ID');
      return;
    }

    debugPrint('🔋 Starting data polling');

    // Stop any existing timer
    _dataPollingTimer?.cancel();

    // Start periodic polling every 15 seconds (matching backend logic)
    _dataPollingTimer = Timer.periodic(const Duration(seconds: 15), (_) async {
      await _pollChargingData();
    });

    // Do initial poll immediately
    await _pollChargingData();

    debugPrint('✅ Data polling started');
  }

  /// Stop data polling
  void _stopDataPolling() {
    debugPrint('🔋 Stopping data polling');

    _dataPollingTimer?.cancel();
    _dataPollingTimer = null;

    debugPrint('✅ Data polling stopped');
  }

  /// Poll charging data
  Future<void> _pollChargingData() async {
    try {
      if (!_isChargingSessionActive ||
          _authorizationReference == null ||
          _transactionId == null) {
        return;
      }

      debugPrint('📊 Polling charging data...');

      final result = await _fetchChargingDataFromAPI(
          _authorizationReference!, _transactionId!);

      // Update charging data
      await updateChargingData(result);

      // Check if charging is complete
      final isComplete = result['isComplete'] as bool? ?? false;
      if (isComplete) {
        await _handleChargingComplete();
      }
    } catch (e) {
      debugPrint('❌ Error polling charging data: $e');
      _onError?.call('Error polling charging data: $e');
    }
  }

  /// Handle charging completion
  Future<void> _handleChargingComplete() async {
    try {
      debugPrint('🔋 ===== CHARGING SESSION COMPLETED =====');

      // Notify callback
      _onSessionComplete?.call();

      // Stop persistent session after a delay
      Timer(const Duration(seconds: 30), () async {
        await stopPersistentChargingSession();
      });
    } catch (e) {
      debugPrint('❌ Error handling charging completion: $e');
    }
  }

  /// Handle charging error
  Future<void> _handleChargingError(String error) async {
    try {
      debugPrint('🔋 ===== CHARGING SESSION ERROR =====');
      debugPrint('🔋 Error: $error');

      // Notify callback
      _onError?.call(error);

      // Stop persistent session
      await stopPersistentChargingSession();
    } catch (e) {
      debugPrint('❌ Error handling charging error: $e');
    }
  }

  /// Save session data to persistent storage
  Future<void> _saveSessionData() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      await prefs.setString('charging_station_uid', _stationUid ?? '');
      await prefs.setString('charging_connector_id', _connectorId ?? '');
      await prefs.setString('charging_transaction_id', _transactionId ?? '');
      await prefs.setString(
          'charging_auth_reference', _authorizationReference ?? '');
      await prefs.setBool('charging_session_active', true);
      await prefs.setInt(
          'charging_session_start_time', DateTime.now().millisecondsSinceEpoch);

      debugPrint('✅ Session data saved to persistent storage');
    } catch (e) {
      debugPrint('❌ Error saving session data: $e');
    }
  }

  /// Update session data in storage
  Future<void> _updateSessionDataInStorage(Map<String, dynamic> data) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      await prefs.setString(
          'charging_last_update', DateTime.now().toIso8601String());
      await prefs.setString('charging_data', data.toString());

      debugPrint('✅ Session data updated in storage');
    } catch (e) {
      debugPrint('❌ Error updating session data in storage: $e');
    }
  }

  /// Clear session data from persistent storage
  Future<void> _clearSessionData() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      await prefs.remove('charging_station_uid');
      await prefs.remove('charging_connector_id');
      await prefs.remove('charging_transaction_id');
      await prefs.remove('charging_auth_reference');
      await prefs.remove('charging_session_active');
      await prefs.remove('charging_session_start_time');
      await prefs.remove('charging_last_update');
      await prefs.remove('charging_data');

      debugPrint('✅ Session data cleared from persistent storage');
    } catch (e) {
      debugPrint('❌ Error clearing session data: $e');
    }
  }

  /// Check for existing charging session
  Future<void> _checkForExistingSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      final isActive = prefs.getBool('charging_session_active') ?? false;
      if (!isActive) {
        debugPrint('🔋 No existing charging session found');
        return;
      }

      // Restore session data
      _stationUid = prefs.getString('charging_station_uid');
      _connectorId = prefs.getString('charging_connector_id');
      _transactionId = prefs.getString('charging_transaction_id');
      _authorizationReference = prefs.getString('charging_auth_reference');

      if (_stationUid != null &&
          _connectorId != null &&
          _transactionId != null &&
          _authorizationReference != null) {
        debugPrint('🔋 ===== EXISTING CHARGING SESSION FOUND =====');
        debugPrint('🔋 Station: $_stationUid');
        debugPrint('🔋 Connector: $_connectorId');
        debugPrint('🔋 Transaction ID: $_transactionId');

        _isChargingSessionActive = true;

        // Resume data polling
        await _startDataPolling();

        debugPrint('✅ Existing charging session restored');
      }
    } catch (e) {
      debugPrint('❌ Error checking for existing session: $e');
    }
  }

  /// Handle app resumed
  Future<void> _handleAppResumed() async {
    debugPrint('🔋 App resumed - checking charging session state');

    if (_isChargingSessionActive) {
      // Resume data polling if needed
      if (_dataPollingTimer == null || !_dataPollingTimer!.isActive) {
        await _startDataPolling();
      }
    }
  }

  /// Handle app paused
  Future<void> _handleAppPaused() async {
    debugPrint('🔋 App paused - maintaining background service');

    // Keep background service running
    // Data polling continues in background service
  }

  /// Handle app terminated
  Future<void> _handleAppTerminated() async {
    debugPrint('🔋 App terminated - background service will continue');

    // Background service will continue running
    // No action needed here
  }

  /// Dispose the service
  void dispose() {
    debugPrint('🔋 ===== DISPOSING PERSISTENT CHARGING SERVICE =====');

    _stopDataPolling();
    _isInitialized = false;
    _isChargingSessionActive = false;
    _currentSession = null;

    debugPrint('✅ Persistent charging service disposed');
  }

  /// Get session restoration data for deep linking
  Map<String, dynamic>? getSessionRestorationData() {
    if (!_isChargingSessionActive) return null;

    return {
      'stationUid': _stationUid,
      'connectorId': _connectorId,
      'transactionId': _transactionId,
      'authorizationReference': _authorizationReference,
      'isActive': _isChargingSessionActive,
    };
  }

  /// Restore session from deep link data
  Future<void> restoreSessionFromData(Map<String, dynamic> data) async {
    try {
      debugPrint('🔋 ===== RESTORING SESSION FROM DEEP LINK DATA =====');

      _stationUid = data['stationUid'] as String?;
      _connectorId = data['connectorId'] as String?;
      _transactionId = data['transactionId'] as String?;
      _authorizationReference = data['authorizationReference'] as String?;
      _isChargingSessionActive = data['isActive'] as bool? ?? false;

      if (_isChargingSessionActive) {
        await _startDataPolling();
      }

      debugPrint('✅ Session restored from deep link data');
    } catch (e) {
      debugPrint('❌ Error restoring session from data: $e');
    }
  }
}
