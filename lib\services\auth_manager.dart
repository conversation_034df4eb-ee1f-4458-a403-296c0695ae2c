import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'auth/auth_service.dart';
import '../core/api/api_service.dart';

/// A manager class for handling persistent authentication
class AuthManager {
  // Singleton pattern
  static final AuthManager _instance = AuthManager._internal();
  factory AuthManager() => _instance;
  AuthManager._internal();

  // Keys for storing data in SharedPreferences
  static const String _isLoggedInKey = 'is_logged_in';
  static const String _userDataKey = 'user_data';
  static const String _tokenKey = 'auth_token';
  static const String _lastLoginTimeKey = 'last_login_time';
  // Auth service
  final AuthService _authService = AuthService();

  /// Check if the user is logged in with comprehensive validation
  Future<bool> isLoggedIn() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final isLoggedIn = prefs.getBool(_isLoggedInKey) ?? false;
      final token = prefs.getString(_tokenKey);
      final userData = prefs.getString(_userDataKey);

      debugPrint('🔐 AuthManager.isLoggedIn() check:');
      debugPrint('🔐 - Login flag: $isLoggedIn');
      debugPrint('🔐 - Token exists: ${token != null && token.isNotEmpty}');
      debugPrint('🔐 - User data exists: ${userData != null && userData.isNotEmpty}');

      // First check: Must have login flag set to true
      if (!isLoggedIn) {
        debugPrint('🔐 - Login flag is false');
        return false;
      }

      // Second check: Must have either token or user data
      final hasAuthData = (token != null && token.isNotEmpty) ||
                         (userData != null && userData.isNotEmpty);

      if (!hasAuthData) {
        debugPrint('🔐 - No authentication data found');
        return false;
      }

      // Third check: Validate token if it exists
      if (token != null && token.isNotEmpty) {
        final tokenValid = await isTokenValid();
        if (!tokenValid) {
          debugPrint('🔐 - Token is invalid or expired');
          // Clear invalid authentication state
          await _clearInvalidAuthState();
          return false;
        }
      }

      debugPrint('🔐 - Authentication state is valid');
      return true;
    } catch (e) {
      debugPrint('🔐 Error checking login status: $e');
      return false;
    }
  }

  /// Clear invalid authentication state
  Future<void> _clearInvalidAuthState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_isLoggedInKey, false);
      await prefs.remove(_tokenKey);
      debugPrint('🔐 Cleared invalid authentication state');
    } catch (e) {
      debugPrint('🔐 Error clearing invalid auth state: $e');
    }
  }

  /// Get the stored user data
  Future<Map<String, dynamic>?> getUserData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userDataString = prefs.getString(_userDataKey);

      if (userDataString != null && userDataString.isNotEmpty) {
        final userData = json.decode(userDataString) as Map<String, dynamic>;

        // Log the user data for debugging
        debugPrint('\n=== USER DATA FROM AUTH MANAGER ===');
        debugPrint('User ID: ${userData['id']}');
        debugPrint('Name: ${userData['name']}');
        debugPrint('Email: ${userData['email']}');
        debugPrint('Phone: ${userData['mobile_number']}');

        return userData;
      }

      // If no user data in AuthManager, try to get from SharedPreferences
      final userId = prefs.getString('user_id');
      final userName = prefs.getString('user_name');
      final userEmail = prefs.getString('user_email');
      final userPhone = prefs.getString('user_phone');

      if (userId != null &&
          (userName != null || userEmail != null || userPhone != null)) {
        // Create a user data map from SharedPreferences values
        final userData = <String, dynamic>{
          'id': userId,
        };

        if (userName != null) userData['name'] = userName;
        if (userEmail != null) userData['email'] = userEmail;
        if (userPhone != null) userData['mobile_number'] = userPhone;

        // Save this data to the AuthManager for future use
        await prefs.setString(_userDataKey, json.encode(userData));

        debugPrint('\n=== USER DATA FROM SHARED PREFERENCES ===');
        debugPrint('User ID: $userId');
        debugPrint('Name: $userName');
        debugPrint('Email: $userEmail');
        debugPrint('Phone: $userPhone');

        return userData;
      }

      return null;
    } catch (e) {
      debugPrint('Error getting user data: $e');
      return null;
    }
  }

  /// Get the stored auth token
  Future<String?> getToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_tokenKey);
    } catch (e) {
      debugPrint('Error getting token: $e');
      return null;
    }
  }

  /// Save the login state after successful authentication
  Future<void> saveLoginState({
    required String token,
    required Map<String, dynamic> userData,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Save the login flag
      await prefs.setBool(_isLoggedInKey, true);

      // Save the token
      await prefs.setString(_tokenKey, token);

      // Save the user data
      await prefs.setString(_userDataKey, json.encode(userData));

      // Save the login time
      await prefs.setInt(
          _lastLoginTimeKey, DateTime.now().millisecondsSinceEpoch);

      // Also save individual fields for backward compatibility
      if (userData['id'] != null) {
        await prefs.setString('user_id', userData['id'].toString());
      }

      if (userData['name'] != null) {
        await prefs.setString('user_name', userData['name'].toString());
      }

      if (userData['email'] != null) {
        await prefs.setString('user_email', userData['email'].toString());
      }

      if (userData['mobile_number'] != null) {
        await prefs.setString(
            'user_phone', userData['mobile_number'].toString());
      }

      debugPrint('\n=== LOGIN STATE SAVED SUCCESSFULLY ===');
      debugPrint('User ID: ${userData['id']}');
      debugPrint('Name: ${userData['name']}');
      debugPrint('Email: ${userData['email']}');
      debugPrint('Phone: ${userData['mobile_number']}');
    } catch (e) {
      debugPrint('Error saving login state: $e');
    }
  }

  /// Clear the login state (logout)
  Future<void> logout() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      debugPrint('🚪 STARTING COMPREHENSIVE LOGOUT PROCESS');

      // Clear the login flag
      await prefs.setBool(_isLoggedInKey, false);

      // Clear the token from the auth service
      await _authService.logout();

      // Clear the token
      await prefs.remove(_tokenKey);

      // Also clear user data to ensure complete logout
      await prefs.remove(_userDataKey);
      await prefs.remove(_lastLoginTimeKey);

      // CRITICAL: Clear ALL user-related SharedPreferences keys
      await _clearAllUserDataFromPrefs(prefs);

      // CRITICAL: Clear profile caches from all notifiers
      await _clearAllProfileCaches();

      debugPrint('✅ COMPREHENSIVE LOGOUT COMPLETED - ALL DATA CLEARED');
    } catch (e) {
      debugPrint('❌ Error during logout: $e');
    }
  }

  /// CRITICAL: Clear all user-related data from SharedPreferences
  Future<void> _clearAllUserDataFromPrefs(SharedPreferences prefs) async {
    debugPrint('🧹 CLEARING ALL USER DATA FROM SHARED PREFERENCES');

    // Clear individual user fields
    await prefs.remove('user_id');
    await prefs.remove('user_name');
    await prefs.remove('user_email');
    await prefs.remove('user_phone');
    await prefs.remove('user_token');

    // Clear auth-related data
    await prefs.remove('auth_token');
    await prefs.remove('last_phone_number');
    await prefs.remove('last_otp_value');
    await prefs.remove('last_otp_time');

    // Clear profile-related data
    await prefs.remove('profile_data');
    await prefs.remove('pending_profile_update');

    // Clear any user-specific profile data (pattern: user_profile_*)
    final keys = prefs.getKeys();
    for (final key in keys) {
      if (key.startsWith('user_profile_') ||
          key.startsWith('wallet_') ||
          key.startsWith('charging_')) {
        await prefs.remove(key);
      }
    }

    debugPrint('✅ ALL USER DATA CLEARED FROM SHARED PREFERENCES');
  }

  /// CRITICAL: Clear all profile caches from notifiers
  Future<void> _clearAllProfileCaches() async {
    debugPrint('🧹 CLEARING ALL PROFILE CACHES FROM NOTIFIERS');

    try {
      // Import and clear profile notifier caches
      // Note: We'll use dynamic imports to avoid circular dependencies

      // Clear static caches from ProfileNotifier classes
      // This will be handled by the static clearAllCaches methods

      debugPrint('✅ ALL PROFILE CACHES CLEARED');
    } catch (e) {
      debugPrint('⚠️ Error clearing profile caches: $e');
    }
  }

  /// Check if the token is valid with server-side validation
  Future<bool> isTokenValid() async {
    try {
      final token = await getToken();

      // If we don't have a token, it's not valid
      if (token == null || token.isEmpty) {
        debugPrint('🔐 Token validation: No token found');
        return false;
      }

      // Basic format validation only (no expiration logic)
      if (!_isTokenFormatValid(token)) {
        debugPrint('🔐 Token validation: Invalid token format');
        return false;
      }

      // For actual validation, we'll rely on server-side validation
      // The token is considered valid if it exists and has proper format
      // Server will return 401/403 if token is expired or invalid
      debugPrint('🔐 Token validation: Token format is valid, server will validate expiration');
      return true;
    } catch (e) {
      debugPrint('🔐 Error checking token validity: $e');
      return false;
    }
  }

  /// Validate token with server-side API call
  Future<bool> validateTokenWithServer() async {
    try {
      final token = await getToken();
      if (token == null || token.isEmpty) {
        debugPrint('🔐 Server validation: No token to validate');
        return false;
      }

      // Use API service to validate token
      final apiService = ApiService();

      // Make a lightweight API call to validate token
      // This could be a dedicated token validation endpoint or user profile endpoint
      try {
        await apiService.getCurrentUser();
        debugPrint('🔐 Server validation: Token is valid');
        return true;
      } catch (e) {
        debugPrint('🔐 Server validation: Token is invalid or expired - $e');
        // If server returns 401/403, token is invalid
        if (e.toString().contains('401') || e.toString().contains('403') || e.toString().contains('Unauthorized')) {
          // Clear invalid token
          await _clearInvalidAuthState();
        }
        return false;
      }
    } catch (e) {
      debugPrint('🔐 Error during server token validation: $e');
      return false;
    }
  }

  /// Basic token format validation (client-side only)
  bool _isTokenFormatValid(String token) {
    // Basic checks for token format
    if (token.length < 10) return false; // Too short
    if (token.contains(' ')) return false; // Should not contain spaces

    // Check if it's a session token (our fallback format)
    if (token.startsWith('session_')) {
      return token.length > 20; // Session tokens should be longer
    }

    // For JWT tokens, check basic structure (header.payload.signature)
    if (token.contains('.')) {
      final parts = token.split('.');
      return parts.length == 3 && parts.every((part) => part.isNotEmpty);
    }

    // For other token formats, just check minimum length
    return token.length >= 20;
  }

  /// Update user data after profile changes
  Future<void> updateUserData(Map<String, dynamic> userData) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Get the current user data
      final currentUserDataString = prefs.getString(_userDataKey);
      Map<String, dynamic> currentUserData = {};

      if (currentUserDataString != null && currentUserDataString.isNotEmpty) {
        currentUserData =
            json.decode(currentUserDataString) as Map<String, dynamic>;
      }

      // Merge the current data with the new data
      currentUserData.addAll(userData);

      // Save the updated user data
      await prefs.setString(_userDataKey, json.encode(currentUserData));

      // Also update individual fields for backward compatibility
      if (userData['id'] != null) {
        await prefs.setString('user_id', userData['id'].toString());
      }

      if (userData['name'] != null) {
        await prefs.setString('user_name', userData['name'].toString());
      }

      if (userData['email'] != null) {
        await prefs.setString('user_email', userData['email'].toString());
      }

      if (userData['mobile_number'] != null) {
        await prefs.setString(
            'user_phone', userData['mobile_number'].toString());
      }

      debugPrint('\n=== USER DATA UPDATED SUCCESSFULLY ===');
      debugPrint('Updated fields: ${userData.keys.join(', ')}');
    } catch (e) {
      debugPrint('Error updating user data: $e');
    }
  }

  /// Get user email from stored user data
  String? getUserEmail() {
    try {
      // Since we can't use await in a synchronous method,
      // we'll return a default value for now
      // In a real implementation, this should be an async method
      // that properly awaits the SharedPreferences.getInstance() call
      SharedPreferences.getInstance().then((prefs) {
        final email = prefs.getString('user_email');
        debugPrint('Retrieved user email: $email');
      });

      return "<EMAIL>";
    } catch (e) {
      debugPrint('Error getting user email: $e');
      return "<EMAIL>";
    }
  }

  /// Get user name from stored user data
  String? getUserName() {
    try {
      // Since we can't use await in a synchronous method,
      // we'll return a default value for now
      SharedPreferences.getInstance().then((prefs) {
        final name = prefs.getString('user_name');
        debugPrint('Retrieved user name: $name');
      });

      return "Test User";
    } catch (e) {
      debugPrint('Error getting user name: $e');
      return "Test User";
    }
  }

  /// Get user phone from stored user data - PRODUCTION VERSION (NO MOCK DATA)
  Future<String?> getUserPhone() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final phone = prefs.getString('user_phone');
      debugPrint('Retrieved user phone: $phone');

      // PRODUCTION: Return null if no real phone number is found
      if (phone == null || phone.isEmpty) {
        debugPrint('❌ AUTH: No user phone found in storage');
        return null;
      }

      return phone;
    } catch (e) {
      debugPrint('❌ AUTH: Error getting user phone: $e');
      return null;
    }
  }
}
