/// Station detail model for backward compatibility
class StationDetail {
  final String uid;
  final String name;
  final String address;
  final String? city;
  final String? state;
  final double latitude;
  final double longitude;
  final String status;
  final List<ConnectorDetail> connectors;
  final List<String> amenities;
  final String operatingHours;
  final List<String> images;
  final double rating;
  final int totalReviews;
  final String operatorName;

  StationDetail({
    required this.uid,
    required this.name,
    required this.address,
    this.city,
    this.state,
    required this.latitude,
    required this.longitude,
    required this.status,
    required this.connectors,
    required this.amenities,
    required this.operatingHours,
    required this.images,
    required this.rating,
    required this.totalReviews,
    required this.operatorName,
  });

  factory StationDetail.fromJson(Map<String, dynamic> json) {
    // CRITICAL: Validate required fields - throw exceptions for missing data
    // This prevents showing default/fallback data when API fails

    if (json['uid'] == null || (json['uid'] as String).isEmpty) {
      throw FormatException(
          'CRITICAL: Station UID is missing from API response');
    }

    if (json['name'] == null || (json['name'] as String).isEmpty) {
      throw FormatException(
          'CRITICAL: Station name is missing from API response');
    }

    if (json['address'] == null || (json['address'] as String).isEmpty) {
      throw FormatException(
          'CRITICAL: Station address is missing from API response');
    }

    // Validate coordinates
    final coordinates = json['coordinates'] as Map<String, dynamic>?;
    if (coordinates == null ||
        coordinates['latitude'] == null ||
        coordinates['longitude'] == null) {
      throw FormatException(
          'CRITICAL: Station coordinates are missing from API response');
    }

    final latitude = (coordinates['latitude'] as num?)?.toDouble();
    final longitude = (coordinates['longitude'] as num?)?.toDouble();

    if (latitude == null ||
        longitude == null ||
        latitude == 0.0 ||
        longitude == 0.0) {
      throw FormatException(
          'CRITICAL: Invalid station coordinates from API response');
    }

    return StationDetail(
      uid: json['uid'] as String,
      name: json['name'] as String,
      address: json['address'] as String,
      city: json['location_details']?['city'] ?? json['city'],
      state: json['location_details']?['state'] ?? json['state'],
      latitude: latitude,
      longitude: longitude,
      status: json['status'] as String? ?? '',
      connectors: (json['connectors'] as List<dynamic>?)
              ?.map((c) => ConnectorDetail.fromJson(c))
              .toList() ??
          [],
      amenities: (json['amenities'] as List<dynamic>?)
              ?.map((a) => a.toString())
              .toList() ??
          [],
      operatingHours: json['operating_hours'] as String? ?? '',
      images: (json['images'] as List<dynamic>?)
              ?.map((i) => i.toString())
              .toList() ??
          [],
      rating: json['rating']?.toDouble() ?? 0.0,
      totalReviews: json['total_reviews'] ?? 0,
      operatorName: json['operator_name'] as String? ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'uid': uid,
      'name': name,
      'address': address,
      'city': city,
      'state': state,
      'coordinates': {
        'latitude': latitude,
        'longitude': longitude,
      },
      'status': status,
      'connectors': connectors.map((c) => c.toJson()).toList(),
      'amenities': amenities,
      'operating_hours': operatingHours,
      'images': images,
      'rating': rating,
      'total_reviews': totalReviews,
      'operator_name': operatorName,
    };
  }
}

/// Connector detail model
class ConnectorDetail {
  final String id;
  final String type;
  final String status;
  final double powerKw;
  final String priceText;

  ConnectorDetail({
    required this.id,
    required this.type,
    required this.status,
    required this.powerKw,
    required this.priceText,
  });

  factory ConnectorDetail.fromJson(Map<String, dynamic> json) {
    return ConnectorDetail(
      id: json['id'] ?? '',
      type: json['type'] ?? '',
      status: json['status'] ?? '',
      powerKw: json['power_kw']?.toDouble() ?? 0.0,
      priceText: json['price_text'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'status': status,
      'power_kw': powerKw,
      'price_text': priceText,
    };
  }
}
