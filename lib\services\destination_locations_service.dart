import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import '../core/api/api_service.dart';
import '../core/api/api_config.dart';
import '../models/destination_station.dart';

/// Service for fetching charging stations along a route
class DestinationLocationsService {
  final ApiService _apiService;

  DestinationLocationsService(this._apiService);

  /// Fetch charging stations along a route using polyline coordinates
  ///
  /// [polylinePoints] - List of LatLng coordinates from the route polyline
  /// Returns list of charging stations along the route
  Future<List<DestinationStation>> getStationsAlongRoute(
    List<LatLng> polylinePoints,
  ) async {
    try {
      if (polylinePoints.isEmpty) {
        debugPrint('❌ DESTINATION: No polyline points provided');
        return [];
      }

      debugPrint('🗺️ DESTINATION: Fetching stations for ${polylinePoints.length} route points');

      // Convert LatLng points to the required format: [[lat, lng], [lat, lng], ...]
      final List<List<double>> allPoints = polylinePoints
          .map((point) => [point.latitude, point.longitude])
          .toList();

      // Prepare the payload
      final Map<String, dynamic> payload = {
        'allpoints': allPoints,
      };

      debugPrint('📍 DESTINATION: Sending ${allPoints.length} coordinate pairs to API');
      if (kDebugMode) {
        debugPrint('🔍 DESTINATION: First few points: ${allPoints.take(3).toList()}');
        debugPrint('🔍 DESTINATION: Last few points: ${allPoints.reversed.take(3).toList().reversed.toList()}');

        // DETAILED PAYLOAD DEBUGGING - SHOW ALL COORDINATES
        debugPrint('🔍 === COMPLETE PAYLOAD DEBUG ===');
        debugPrint('📦 PAYLOAD TYPE: ${payload.runtimeType}');
        debugPrint('📦 PAYLOAD KEYS: ${payload.keys.toList()}');
        debugPrint('📦 ALLPOINTS TYPE: ${payload['allpoints'].runtimeType}');
        debugPrint('📦 ALLPOINTS LENGTH: ${(payload['allpoints'] as List).length}');
        debugPrint('📦 COMPLETE PAYLOAD WITH ALL COORDINATES:');
        debugPrint('   {');
        debugPrint('     "allpoints": [');
        final allpoints = payload['allpoints'] as List;
        for (int i = 0; i < allpoints.length; i++) {
          final point = allpoints[i];
          debugPrint('       [$i]: $point (lat: ${point[0]}, lng: ${point[1]})');
        }
        debugPrint('     ]');
        debugPrint('   }');
        debugPrint('🔍 === END COMPLETE PAYLOAD DEBUG ===');
      }

      // COMPLETE API REQUEST DEBUGGING - SHOW ALL REQUEST DATA
      if (kDebugMode) {
        debugPrint('🚀 === COMPLETE API REQUEST DEBUG ===');
        debugPrint('🌐 ENDPOINT: ${ApiConfig.destinationLocations}');
        debugPrint('🌐 FULL URL: ${ApiConfig.apiUrl}${ApiConfig.destinationLocations}');
        debugPrint('📦 REQUEST METHOD: POST');
        debugPrint('📦 REQUEST HEADERS: Content-Type: application/json');
        debugPrint('📦 REQUEST BODY (JSON):');
        debugPrint('   ${jsonEncode(payload)}');
        debugPrint('📦 REQUEST BODY (PRETTY):');
        const encoder = JsonEncoder.withIndent('  ');
        debugPrint(encoder.convert(payload));
        debugPrint('🚀 === END API REQUEST DEBUG ===');
      }

      // Make the API call
      final responseData = await _apiService.post(
        ApiConfig.destinationLocations,
        data: payload,
      );

      debugPrint('✅ DESTINATION: Received API response');

      // COMPREHENSIVE JSON RESPONSE STRUCTURE DEBUGGING
      if (kDebugMode) {
        debugPrint('\n📥 === COMPLETE /user/getdestinationlocations API RESPONSE DEBUG ===');

        // 1. RAW JSON STRING OUTPUT
        debugPrint('📄 === RAW JSON RESPONSE STRING ===');
        try {
          final rawJsonString = jsonEncode(responseData);
          debugPrint('📄 RAW JSON LENGTH: ${rawJsonString.length} characters');
          debugPrint('📄 RAW JSON STRING:');
          debugPrint(rawJsonString);
        } catch (e) {
          debugPrint('📄 ERROR ENCODING RAW JSON: $e');
          debugPrint('📄 RAW RESPONSE OBJECT: $responseData');
        }
        debugPrint('📄 === END RAW JSON ===\n');

        // 2. PRETTY-PRINTED JSON WITH INDENTATION
        debugPrint('🎨 === PRETTY-PRINTED JSON RESPONSE ===');
        try {
          const encoder = JsonEncoder.withIndent('  ');
          final prettyJson = encoder.convert(responseData);
          debugPrint('🎨 FORMATTED JSON:');
          debugPrint(prettyJson);
        } catch (e) {
          debugPrint('🎨 ERROR FORMATTING JSON: $e');
        }
        debugPrint('🎨 === END PRETTY JSON ===\n');

        // 3. RESPONSE STRUCTURE ANALYSIS
        debugPrint('🔍 === JSON STRUCTURE ANALYSIS ===');
        debugPrint('🔍 RESPONSE DATA TYPE: ${responseData.runtimeType}');
        debugPrint('🔍 RESPONSE IS NULL: ${responseData == null}');
        debugPrint('🔍 RESPONSE IS EMPTY: ${responseData.toString().isEmpty}');

        if (responseData is Map<String, dynamic>) {
          debugPrint('🔍 RESPONSE IS MAP - ANALYZING STRUCTURE:');
          debugPrint('🔍 ROOT LEVEL KEYS: ${responseData.keys.toList()}');
          debugPrint('🔍 ROOT LEVEL KEY COUNT: ${responseData.keys.length}');

          // Analyze each root-level key
          responseData.forEach((key, value) {
            debugPrint('🔍 KEY: "$key"');
            debugPrint('   TYPE: ${value.runtimeType}');
            debugPrint('   IS_NULL: ${value == null}');

            if (value is List) {
              debugPrint('   ARRAY_LENGTH: ${value.length}');
              if (value.isNotEmpty) {
                debugPrint('   FIRST_ITEM_TYPE: ${value.first.runtimeType}');
                if (value.first is Map) {
                  final firstItem = value.first as Map;
                  debugPrint('   FIRST_ITEM_KEYS: ${firstItem.keys.toList()}');
                }
              }
            } else if (value is Map) {
              debugPrint('   MAP_KEYS: ${value.keys.toList()}');
              debugPrint('   MAP_SIZE: ${value.length}');
            } else if (value is String) {
              debugPrint('   STRING_LENGTH: ${value.length}');
              debugPrint('   STRING_VALUE: "$value"');
            } else {
              debugPrint('   VALUE: $value');
            }
          });

          // 4. DETAILED STATION DATA ANALYSIS
          if (responseData.containsKey('data')) {
            final data = responseData['data'];
            debugPrint('\n📊 === STATION DATA DETAILED ANALYSIS ===');
            debugPrint('📊 DATA FIELD TYPE: ${data.runtimeType}');

            if (data is List) {
              debugPrint('📊 STATIONS ARRAY LENGTH: ${data.length}');
              debugPrint('📊 STATIONS ARRAY IS_EMPTY: ${data.isEmpty}');

              if (data.isNotEmpty) {
                debugPrint('📊 ANALYZING FIRST STATION STRUCTURE:');
                final firstStation = data.first;
                debugPrint('📊 FIRST STATION TYPE: ${firstStation.runtimeType}');

                if (firstStation is Map<String, dynamic>) {
                  debugPrint('📊 FIRST STATION KEYS: ${firstStation.keys.toList()}');
                  debugPrint('📊 FIRST STATION FIELD COUNT: ${firstStation.keys.length}');

                  // Analyze each field in the station
                  firstStation.forEach((fieldKey, fieldValue) {
                    debugPrint('📊 STATION FIELD: "$fieldKey"');
                    debugPrint('   FIELD_TYPE: ${fieldValue.runtimeType}');
                    debugPrint('   FIELD_VALUE: $fieldValue');
                    debugPrint('   IS_NULL: ${fieldValue == null}');

                    // Special handling for coordinate fields
                    if (fieldKey.toLowerCase().contains('lat') ||
                        fieldKey.toLowerCase().contains('lng') ||
                        fieldKey.toLowerCase().contains('longitude')) {
                      if (fieldValue is num) {
                        debugPrint('   COORDINATE_VALID: ${fieldValue >= -180 && fieldValue <= 180}');
                      }
                    }
                  });
                }

                // Show complete structure of first few stations
                debugPrint('\n📊 === COMPLETE STATION STRUCTURES ===');
                final stationsToShow = data.length > 3 ? 3 : data.length;
                for (int i = 0; i < stationsToShow; i++) {
                  debugPrint('📊 STATION ${i + 1} COMPLETE STRUCTURE:');
                  try {
                    const encoder = JsonEncoder.withIndent('    ');
                    debugPrint(encoder.convert(data[i]));
                  } catch (e) {
                    debugPrint('    ${data[i]}');
                  }
                  debugPrint('');
                }

                if (data.length > 3) {
                  debugPrint('📊 ... and ${data.length - 3} more stations with similar structure');
                }
              }
            } else {
              debugPrint('📊 DATA FIELD IS NOT AN ARRAY: $data');
            }
          }

          // 5. METADATA AND PAGINATION ANALYSIS
          debugPrint('\n📋 === METADATA AND PAGINATION ANALYSIS ===');
          final metadataFields = ['success', 'message', 'status', 'code', 'total', 'count', 'page', 'limit', 'pagination', 'meta'];
          for (final field in metadataFields) {
            if (responseData.containsKey(field)) {
              debugPrint('📋 METADATA FIELD: "$field" = ${responseData[field]} (${responseData[field].runtimeType})');
            }
          }

          // 6. ERROR AND STATUS ANALYSIS
          debugPrint('\n⚠️ === ERROR AND STATUS ANALYSIS ===');
          if (responseData.containsKey('success')) {
            debugPrint('⚠️ SUCCESS STATUS: ${responseData['success']}');
          }
          if (responseData.containsKey('error')) {
            debugPrint('⚠️ ERROR FIELD: ${responseData['error']}');
          }
          if (responseData.containsKey('message')) {
            debugPrint('⚠️ MESSAGE FIELD: ${responseData['message']}');
          }
          if (responseData.containsKey('errors')) {
            debugPrint('⚠️ ERRORS FIELD: ${responseData['errors']}');
          }

        } else if (responseData is List) {
          debugPrint('🔍 RESPONSE IS ARRAY - ANALYZING:');
          debugPrint('🔍 ARRAY LENGTH: ${responseData.length}');
          debugPrint('🔍 ARRAY IS_EMPTY: ${responseData.isEmpty}');

          if (responseData.isNotEmpty) {
            debugPrint('🔍 FIRST ITEM TYPE: ${responseData.first.runtimeType}');
            debugPrint('🔍 FIRST ITEM STRUCTURE:');
            try {
              const encoder = JsonEncoder.withIndent('  ');
              debugPrint(encoder.convert(responseData.first));
            } catch (e) {
              debugPrint('  ${responseData.first}');
            }
          }
        } else {
          debugPrint('🔍 RESPONSE IS PRIMITIVE TYPE:');
          debugPrint('🔍 VALUE: $responseData');
        }

        debugPrint('🔍 === END STRUCTURE ANALYSIS ===');
        debugPrint('📥 === END COMPLETE API RESPONSE DEBUG ===\n');
      }

      // Parse the response
      final response = DestinationLocationsResponse.fromJson(responseData);

      debugPrint('📊 DESTINATION: Found ${response.stations.length} stations along route');

      if (response.stations.isNotEmpty && kDebugMode) {
        debugPrint('🏪 DESTINATION: === STATION DETAILS DEBUG ===');
        debugPrint('🏪 Total Stations: ${response.stations.length}');

        // Log coordinate ranges
        final latitudes = response.stations.map((s) => s.latitude).toList();
        final longitudes = response.stations.map((s) => s.longitude).toList();
        debugPrint('📍 Latitude Range: ${latitudes.reduce((a, b) => a < b ? a : b)} to ${latitudes.reduce((a, b) => a > b ? a : b)}');
        debugPrint('📍 Longitude Range: ${longitudes.reduce((a, b) => a < b ? a : b)} to ${longitudes.reduce((a, b) => a > b ? a : b)}');

        // Log all stations with detailed info
        for (int i = 0; i < response.stations.length; i++) {
          final station = response.stations[i];
          debugPrint('🏪 Station ${i + 1}:');
          debugPrint('   ID: ${station.id}');
          debugPrint('   Name: ${station.name}');
          debugPrint('   Address: ${station.address}');
          debugPrint('   Status: ${station.status}');
          debugPrint('   Coordinates: (${station.latitude}, ${station.longitude})');
          debugPrint('   Connector Type: ${station.connectorType ?? 'N/A'}');
          debugPrint('   Distance: ${station.distance ?? 'N/A'} km');
          debugPrint('   Free Guns: ${station.freeGuns ?? 'N/A'}');
          if (i >= 4) {
            debugPrint('🏪 ... and ${response.stations.length - 5} more stations');
            break;
          }
        }

        debugPrint('🏪 === END STATION DETAILS ===');
      }

      return response.stations;
    } catch (e) {
      debugPrint('❌ DESTINATION: Error fetching stations along route: $e');
      return [];
    }
  }

  /// Fetch charging stations along a route with simplified coordinate sampling
  ///
  /// This method samples the polyline points to reduce API payload size
  /// [polylinePoints] - List of LatLng coordinates from the route polyline
  /// [sampleRate] - Take every nth point (default: 5 for performance)
  /// Returns list of charging stations along the route
  Future<List<DestinationStation>> getStationsAlongRouteSampled(
    List<LatLng> polylinePoints, {
    int sampleRate = 5,
  }) async {
    try {
      if (polylinePoints.isEmpty) {
        debugPrint('❌ DESTINATION: No polyline points provided');
        return [];
      }

      // Sample the points to reduce payload size
      final List<LatLng> sampledPoints = [];

      // Always include the first point
      sampledPoints.add(polylinePoints.first);

      // Sample intermediate points
      for (int i = sampleRate; i < polylinePoints.length; i += sampleRate) {
        sampledPoints.add(polylinePoints[i]);
      }

      // Always include the last point if it's not already included
      if (polylinePoints.length > 1 &&
          sampledPoints.last != polylinePoints.last) {
        sampledPoints.add(polylinePoints.last);
      }

      debugPrint('🎯 DESTINATION: Sampled ${sampledPoints.length} points from ${polylinePoints.length} total points');

      return await getStationsAlongRoute(sampledPoints);
    } catch (e) {
      debugPrint('❌ DESTINATION: Error in sampled route fetch: $e');
      return [];
    }
  }

  /// Validate polyline points before sending to API
  bool _validatePolylinePoints(List<LatLng> points) {
    if (points.isEmpty) return false;

    // Check for valid coordinates
    for (final point in points) {
      if (point.latitude < -90 || point.latitude > 90 ||
          point.longitude < -180 || point.longitude > 180) {
        debugPrint('❌ DESTINATION: Invalid coordinates found: $point');
        return false;
      }
    }

    return true;
  }

  /// Get stations along route with error handling and validation
  Future<List<DestinationStation>> getStationsAlongRouteRobust(
    List<LatLng> polylinePoints, {
    int sampleRate = 5,
    int maxRetries = 2,
  }) async {
    try {
      // Validate input
      if (!_validatePolylinePoints(polylinePoints)) {
        debugPrint('❌ DESTINATION: Invalid polyline points provided');
        return [];
      }

      // Try with sampling first for better performance
      List<DestinationStation> stations = await getStationsAlongRouteSampled(
        polylinePoints,
        sampleRate: sampleRate,
      );

      // If no stations found and we have many points, try with more detailed sampling
      if (stations.isEmpty && polylinePoints.length > 20 && sampleRate > 2) {
        debugPrint('🔄 DESTINATION: Retrying with more detailed sampling');
        stations = await getStationsAlongRouteSampled(
          polylinePoints,
          sampleRate: sampleRate ~/ 2,
        );
      }

      return stations;
    } catch (e) {
      debugPrint('❌ DESTINATION: Error in robust route fetch: $e');
      return [];
    }
  }
}
