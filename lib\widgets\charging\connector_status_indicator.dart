import 'package:flutter/material.dart';

/// Animated status indicator widget for connector states
class ConnectorStatusIndicator extends StatefulWidget {
  final bool isConnected;
  final bool isCharging;
  final Color color;
  final double size;

  const ConnectorStatusIndicator({
    Key? key,
    required this.isConnected,
    required this.isCharging,
    required this.color,
    this.size = 10,
  }) : super(key: key);

  @override
  State<ConnectorStatusIndicator> createState() => _ConnectorStatusIndicatorState();
}

class _ConnectorStatusIndicatorState extends State<ConnectorStatusIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _animation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    if (widget.isConnected || widget.isCharging) {
      _controller.repeat(reverse: true);
    }
  }

  @override
  void didUpdateWidget(ConnectorStatusIndicator oldWidget) {
    super.didUpdateWidget(oldWidget);
    if ((widget.isConnected || widget.isCharging) &&
        !(oldWidget.isConnected || oldWidget.isCharging)) {
      _controller.repeat(reverse: true);
    } else if (!(widget.isConnected || widget.isCharging) &&
        (oldWidget.isConnected || oldWidget.isCharging)) {
      _controller.stop();
      _controller.reset();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.isConnected && !widget.isCharging) {
      return const SizedBox.shrink();
    }

    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          width: widget.size,
          height: widget.size,
          decoration: BoxDecoration(
            color: widget.color,
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: widget.color.withAlpha((120 * _animation.value).toInt()),
                blurRadius: widget.size * 0.8 * _animation.value,
                spreadRadius: widget.size * 0.2 * _animation.value,
              ),
            ],
          ),
        );
      },
    );
  }
}

/// Status badge widget for connector states
class ConnectorStatusBadge extends StatelessWidget {
  final bool isConnected;
  final bool isCharging;
  final Color color;
  final Color backgroundColor;
  final Color borderColor;

  const ConnectorStatusBadge({
    Key? key,
    required this.isConnected,
    required this.isCharging,
    required this.color,
    required this.backgroundColor,
    required this.borderColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (!isConnected && !isCharging) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: borderColor,
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: color.withAlpha(40),
            blurRadius: 8,
            spreadRadius: 0,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            isCharging ? Icons.battery_charging_full : Icons.power,
            size: 16,
            color: color,
          ),
          const SizedBox(width: 6),
          Text(
            isCharging ? 'Currently Charging' : 'Gun Connected',
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(width: 6),
          ConnectorStatusIndicator(
            isConnected: isConnected,
            isCharging: isCharging,
            color: color,
            size: 8,
          ),
        ],
      ),
    );
  }
}
