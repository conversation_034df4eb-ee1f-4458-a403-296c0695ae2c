import 'package:flutter_cashfree_pg_sdk/api/cferrorresponse/cferrorresponse.dart';
import 'package:flutter_cashfree_pg_sdk/api/cfpayment/cfwebcheckoutpayment.dart';
import 'package:flutter_cashfree_pg_sdk/api/cfpaymentgateway/cfpaymentgatewayservice.dart';
import 'package:flutter_cashfree_pg_sdk/api/cfsession/cfsession.dart';
import 'package:flutter_cashfree_pg_sdk/api/cftheme/cftheme.dart';
import 'package:flutter_cashfree_pg_sdk/utils/cfenums.dart';
import 'package:flutter_cashfree_pg_sdk/utils/cfexceptions.dart';
import 'package:flutter/foundation.dart';
import 'dart:async';
import '../../shared/utils/error_handler.dart';

/// Enum for different payment result types according to Cashfree official documentation
enum CashfreePaymentResultType {
  success,
  cancelled,
  failed,
  pending,
  timeout,
  networkError,
  appCrash,
  invalidResponse,
  backPressed,
  interrupted,
  unknown
}

/// Payment result class for Cashfree payments (matching PhonePe patterns)
class CashfreePaymentResult {
  final CashfreePaymentResultType type;
  final String message;
  final Map<String, dynamic>? data;

  const CashfreePaymentResult({
    required this.type,
    required this.message,
    this.data,
  });

  /// Factory constructors for different result types
  factory CashfreePaymentResult.success(
      [String? message, Map<String, dynamic>? data]) {
    return CashfreePaymentResult(
      type: CashfreePaymentResultType.success,
      message: message ?? 'Payment completed successfully',
      data: data,
    );
  }

  factory CashfreePaymentResult.failed(
      [String? message, Map<String, dynamic>? data]) {
    return CashfreePaymentResult(
      type: CashfreePaymentResultType.failed,
      message: message ?? 'Payment failed',
      data: data,
    );
  }

  factory CashfreePaymentResult.cancelled([String? message]) {
    return CashfreePaymentResult(
      type: CashfreePaymentResultType.cancelled,
      message: message ?? 'Payment cancelled by user',
    );
  }

  factory CashfreePaymentResult.timeout([String? message]) {
    return CashfreePaymentResult(
      type: CashfreePaymentResultType.timeout,
      message: message ?? 'Payment timed out',
    );
  }

  factory CashfreePaymentResult.networkError([String? message]) {
    return CashfreePaymentResult(
      type: CashfreePaymentResultType.networkError,
      message: message ?? 'Network error occurred',
    );
  }

  factory CashfreePaymentResult.appCrash([String? message]) {
    return CashfreePaymentResult(
      type: CashfreePaymentResultType.appCrash,
      message: message ?? 'App crashed during payment',
    );
  }

  factory CashfreePaymentResult.pending(String message, {Map<String, dynamic>? data}) {
    return CashfreePaymentResult(
      type: CashfreePaymentResultType.pending,
      message: message,
      data: data,
    );
  }

  factory CashfreePaymentResult.interrupted(String message, {Map<String, dynamic>? data}) {
    return CashfreePaymentResult(
      type: CashfreePaymentResultType.interrupted,
      message: message,
      data: data,
    );
  }

  factory CashfreePaymentResult.invalidResponse([String? message]) {
    return CashfreePaymentResult(
      type: CashfreePaymentResultType.invalidResponse,
      message: message ?? 'Invalid response received',
    );
  }

  factory CashfreePaymentResult.backPressed([String? message]) {
    return CashfreePaymentResult(
      type: CashfreePaymentResultType.backPressed,
      message: message ?? 'User pressed back button',
    );
  }

  factory CashfreePaymentResult.unknown([String? message]) {
    return CashfreePaymentResult(
      type: CashfreePaymentResultType.unknown,
      message: message ?? 'Unknown error occurred',
    );
  }

  @override
  String toString() {
    return 'CashfreePaymentResult(type: $type, message: $message, data: $data)';
  }
}

/// Service wrapper for Cashfree payment SDK integration with comprehensive exception handling
/// Following the exact same patterns as PhonePeService
class CashfreeService {
  static bool _isInitialized = false;
  static Timer? _paymentTimeoutTimer;
  static Completer<CashfreePaymentResult>? _paymentCompleter;
  static CFPaymentGatewayService? _cfPaymentGatewayService;

  /// Initialize Cashfree SDK with comprehensive error handling according to official documentation
  static Future<bool> init({
    required CFEnvironment environment,
    bool enableLogging = false,
  }) async {
    debugPrint('🔔 CASHFREE: ========== SDK INITIALIZATION START ==========');
    debugPrint('🔔 CASHFREE: Checking initialization status...');
    debugPrint('🔔 CASHFREE: Current _isInitialized: $_isInitialized');

    // CRITICAL FIX: Comprehensive parameter validation according to Cashfree documentation
    debugPrint('🔔 CASHFREE: Validating initialization parameters...');

    // Validate environment parameter
    final validEnvironments = [CFEnvironment.SANDBOX, CFEnvironment.PRODUCTION];
    if (!validEnvironments.contains(environment)) {
      debugPrint('❌ CASHFREE: Invalid environment parameter');
      debugPrint('🔔 CASHFREE: ========== SDK INITIALIZATION FAILED (INVALID ENVIRONMENT) ==========');
      return false;
    }

    debugPrint('✅ CASHFREE: Parameter validation successful');
    debugPrint('🔔 CASHFREE: Environment: $environment');
    debugPrint('🔔 CASHFREE: Enable Logging: $enableLogging');

    if (_isInitialized) {
      debugPrint('🔔 CASHFREE: SDK already initialized, returning true');
      debugPrint(
          '🔔 CASHFREE: ========== SDK INITIALIZATION END (CACHED) ==========');
      return true;
    }

    debugPrint('🔔 CASHFREE: Starting fresh SDK initialization...');

    try {
      debugPrint('🔔 CASHFREE: Creating CFPaymentGatewayService instance...');
      final startTime = DateTime.now();

      // Create Cashfree payment gateway service instance
      _cfPaymentGatewayService = CFPaymentGatewayService();

      // Set up payment callbacks
      _cfPaymentGatewayService!.setCallback(_onPaymentVerify, _onPaymentError);

      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);

      debugPrint(
          '🔔 CASHFREE: SDK initialization completed in ${duration.inMilliseconds}ms');
      debugPrint('🔔 CASHFREE: Service instance created successfully');

      _isInitialized = true;

      debugPrint(
          '🔔 CASHFREE: ========== SDK INITIALIZATION END (SUCCESS) ==========');
      return true;
    } catch (e) {
      debugPrint('❌ CASHFREE: SDK initialization failed with error: $e');
      debugPrint('❌ CASHFREE: Error type: ${e.runtimeType}');
      debugPrint(
          '🔔 CASHFREE: ========== SDK INITIALIZATION END (FAILED) ==========');

      _isInitialized = false;
      _cfPaymentGatewayService = null;
      return false;
    }
  }

  /// Start a Web Checkout transaction with comprehensive error handling according to Cashfree documentation
  static Future<CashfreePaymentResult> startWebCheckoutTransaction({
    required String orderId,
    required String paymentSessionId,
    required CFEnvironment environment,
    CFTheme? theme,
    Duration timeout = const Duration(minutes: 5),
  }) async {
    debugPrint('💳 CASHFREE: ========== TRANSACTION START ==========');
    debugPrint('💳 CASHFREE: Initiating Cashfree Web Checkout transaction...');
    debugPrint('💳 CASHFREE: SDK Version: v2.2.8+46');
    debugPrint('💳 CASHFREE: Timeout Duration: ${timeout.inMinutes} minutes');

    // CRITICAL FIX: Comprehensive input parameter validation according to Cashfree documentation
    debugPrint('💳 CASHFREE: Validating input parameters...');

    if (orderId.isEmpty) {
      debugPrint('❌ CASHFREE: Order ID parameter is empty');
      return CashfreePaymentResult.failed('Invalid order ID. Please try again.');
    }

    if (paymentSessionId.isEmpty) {
      debugPrint('❌ CASHFREE: Payment session ID parameter is empty');
      return CashfreePaymentResult.failed('Invalid payment session. Please try again.');
    }

    // Validate environment parameter
    final validEnvironments = [CFEnvironment.SANDBOX, CFEnvironment.PRODUCTION];
    if (!validEnvironments.contains(environment)) {
      debugPrint('❌ CASHFREE: Invalid environment parameter');
      return CashfreePaymentResult.failed('Payment configuration error. Please contact support.');
    }

    // Validate timeout parameter
    if (timeout.inSeconds <= 0) {
      debugPrint('❌ CASHFREE: Invalid timeout parameter');
      return CashfreePaymentResult.failed('Payment timeout configuration error.');
    }

    debugPrint('✅ CASHFREE: Parameter validation successful');
    debugPrint('💳 CASHFREE: Order ID: "$orderId"');
    debugPrint(
        '💳 CASHFREE: Payment Session ID: "${paymentSessionId.length > 20 ? "${paymentSessionId.substring(0, 20)}..." : paymentSessionId}"');
    debugPrint('💳 CASHFREE: Environment: $environment');

    // Check SDK initialization status
    debugPrint('💳 CASHFREE: Checking SDK initialization status...');
    debugPrint('💳 CASHFREE: SDK Initialized: $_isInitialized');

    if (!_isInitialized || _cfPaymentGatewayService == null) {
      debugPrint(
          '❌ CASHFREE: SDK not initialized! Cannot proceed with transaction');
      return CashfreePaymentResult.failed(
          'Payment service is temporarily unavailable. Please try again later.');
    }

    // Cancel any existing payment flow
    debugPrint('💳 CASHFREE: Cleaning up any existing payment flows...');
    _cancelExistingPayment();

    // Create a new completer for this payment
    debugPrint('💳 CASHFREE: Creating new payment completer...');
    _paymentCompleter = Completer<CashfreePaymentResult>();

    // Set up timeout timer
    debugPrint('💳 CASHFREE: Setting up timeout timer...');
    _paymentTimeoutTimer = Timer(timeout, () {
      debugPrint('⏰ CASHFREE: Payment timeout reached');
      if (!_paymentCompleter!.isCompleted) {
        debugPrint('⏰ CASHFREE: Completing payment with timeout result');
        _paymentCompleter!.complete(CashfreePaymentResult.timeout());
      }
    });

    try {
      debugPrint('💳 CASHFREE: ========== CASHFREE SDK CALL START ==========');
      final sdkCallStartTime = DateTime.now();

      // Create session
      final session = _createSession(
        orderId: orderId,
        paymentSessionId: paymentSessionId,
        environment: environment,
      );

      if (session == null) {
        debugPrint('❌ CASHFREE: Failed to create session');
        return CashfreePaymentResult.failed('Failed to create payment session');
      }

      // Create web checkout payment
      final cfWebCheckoutBuilder =
          CFWebCheckoutPaymentBuilder().setSession(session);

      // Only set theme if provided
      if (theme != null) {
        cfWebCheckoutBuilder.setTheme(theme);
      }

      final cfWebCheckout = cfWebCheckoutBuilder.build();

      debugPrint(
          '💳 CASHFREE: Starting payment with CFPaymentGatewayService...');

      // Start the payment
      _cfPaymentGatewayService!.doPayment(cfWebCheckout);

      final sdkCallEndTime = DateTime.now();
      final sdkDuration = sdkCallEndTime.difference(sdkCallStartTime);

      debugPrint('💳 CASHFREE: ========== CASHFREE SDK CALL END ==========');
      debugPrint(
          '💳 CASHFREE: SDK call completed in ${sdkDuration.inSeconds} seconds');

      // Wait for the payment result
      debugPrint('💳 CASHFREE: Waiting for payment result...');
      final paymentResult = await _paymentCompleter!.future;

      debugPrint('💳 CASHFREE: Payment result received: ${paymentResult.type}');
      debugPrint(
          '💳 CASHFREE: Payment result message: ${paymentResult.message}');

      debugPrint(
          '💳 CASHFREE: ========== TRANSACTION END (SUCCESS) ==========');
      return paymentResult;
    } catch (e) {
      debugPrint('❌ CASHFREE: Exception during payment: $e');
      debugPrint('❌ CASHFREE: Exception type: ${e.runtimeType}');

      // Clean up
      _cancelExistingPayment();

      // Convert exception to user-friendly message
      final userFriendlyMessage =
          ErrorHandler.getUserFriendlyMessage(e.toString());
      debugPrint(
          '❌ CASHFREE: User-friendly exception message: $userFriendlyMessage');

      debugPrint(
          '💳 CASHFREE: ========== TRANSACTION END (EXCEPTION) ==========');
      return CashfreePaymentResult.failed(userFriendlyMessage);
    }
  }

  /// Create CFSession with comprehensive error handling according to Cashfree documentation
  static CFSession? _createSession({
    required String orderId,
    required String paymentSessionId,
    required CFEnvironment environment,
  }) {
    try {
      debugPrint('🔧 CASHFREE: Creating session...');
      debugPrint('🔧 CASHFREE: Order ID: $orderId');
      debugPrint('🔧 CASHFREE: Payment Session ID: ${paymentSessionId.substring(0, 20)}...');
      debugPrint('🔧 CASHFREE: Environment: $environment');

      // CRITICAL FIX: Additional validation before session creation
      if (orderId.trim().isEmpty) {
        debugPrint('❌ CASHFREE: Order ID is empty after trimming');
        return null;
      }

      if (paymentSessionId.trim().isEmpty) {
        debugPrint('❌ CASHFREE: Payment session ID is empty after trimming');
        return null;
      }

      final sessionBuilder = CFSessionBuilder();

      // Set parameters with additional error checking
      try {
        sessionBuilder.setEnvironment(environment);
        debugPrint('✅ CASHFREE: Environment set successfully');
      } catch (e) {
        debugPrint('❌ CASHFREE: Failed to set environment: $e');
        return null;
      }

      try {
        sessionBuilder.setOrderId(orderId.trim());
        debugPrint('✅ CASHFREE: Order ID set successfully');
      } catch (e) {
        debugPrint('❌ CASHFREE: Failed to set order ID: $e');
        return null;
      }

      try {
        sessionBuilder.setPaymentSessionId(paymentSessionId.trim());
        debugPrint('✅ CASHFREE: Payment session ID set successfully');
      } catch (e) {
        debugPrint('❌ CASHFREE: Failed to set payment session ID: $e');
        return null;
      }

      final session = sessionBuilder.build();
      debugPrint('✅ CASHFREE: Session created successfully');
      return session;

    } on CFException catch (e) {
      debugPrint('❌ CASHFREE: CFException during session creation: ${e.message}');
      debugPrint('❌ CASHFREE: CFException details: $e');
      return null;
    } catch (e) {
      debugPrint('❌ CASHFREE: Unexpected error creating session: $e');
      debugPrint('❌ CASHFREE: Error type: ${e.runtimeType}');
      return null;
    }
  }

  /// Payment verification callback (following PhonePe patterns)
  static void _onPaymentVerify(String orderId) {
    debugPrint('✅ CASHFREE: Payment verification callback triggered');
    debugPrint('✅ CASHFREE: Order ID: $orderId');

    if (_paymentCompleter != null && !_paymentCompleter!.isCompleted) {
      debugPrint('✅ CASHFREE: Completing payment with success result');
      _paymentCompleter!.complete(CashfreePaymentResult.success(
        'Payment completed successfully',
        {'orderId': orderId},
      ));
    } else {
      debugPrint('⚠️ CASHFREE: Payment completer already completed or null');
    }

    _cleanupPayment();
  }

  /// Payment error callback (following PhonePe patterns)
  static void _onPaymentError(CFErrorResponse errorResponse, String orderId) {
    debugPrint('❌ CASHFREE: Payment error callback triggered');
    debugPrint('❌ CASHFREE: Order ID: $orderId');
    debugPrint('❌ CASHFREE: Raw Error Message: ${errorResponse.getMessage()}');
    debugPrint('❌ CASHFREE: Error Code: ${errorResponse.getCode()}');

    if (_paymentCompleter != null && !_paymentCompleter!.isCompleted) {
      debugPrint('❌ CASHFREE: Completing payment with error result');

      // Convert raw error message to user-friendly message
      final rawMessage = errorResponse.getMessage() ?? 'Unknown payment error';
      final userFriendlyMessage =
          ErrorHandler.getUserFriendlyMessage(rawMessage);
      debugPrint('❌ CASHFREE: User-friendly message: $userFriendlyMessage');

      _paymentCompleter!.complete(CashfreePaymentResult.failed(
        userFriendlyMessage,
        {'orderId': orderId, 'errorCode': errorResponse.getCode()},
      ));
    } else {
      debugPrint('⚠️ CASHFREE: Payment completer already completed or null');
    }

    _cleanupPayment();
  }

  /// Cancel existing payment flow (following PhonePe patterns)
  static void _cancelExistingPayment() {
    debugPrint('🧹 CASHFREE: Cancelling existing payment flow...');

    if (_paymentTimeoutTimer != null) {
      debugPrint('🧹 CASHFREE: Cancelling timeout timer');
      _paymentTimeoutTimer!.cancel();
      _paymentTimeoutTimer = null;
    }

    if (_paymentCompleter != null && !_paymentCompleter!.isCompleted) {
      debugPrint(
          '🧹 CASHFREE: Completing existing payment completer with cancellation');
      _paymentCompleter!.complete(CashfreePaymentResult.cancelled());
    }

    _paymentCompleter = null;
    debugPrint('🧹 CASHFREE: Cleanup completed');
  }

  /// Clean up payment resources (following PhonePe patterns)
  static void _cleanupPayment() {
    debugPrint('🧹 CASHFREE: Cleaning up payment resources...');

    if (_paymentTimeoutTimer != null) {
      _paymentTimeoutTimer!.cancel();
      _paymentTimeoutTimer = null;
    }

    _paymentCompleter = null;
    debugPrint('🧹 CASHFREE: Payment cleanup completed');
  }

  /// Check if SDK is initialized
  static bool get isInitialized => _isInitialized;

  /// Reset SDK state (for testing purposes)
  static void reset() {
    debugPrint('🔄 CASHFREE: Resetting SDK state...');
    _cancelExistingPayment();
    _isInitialized = false;
    _cfPaymentGatewayService = null;
    debugPrint('🔄 CASHFREE: SDK state reset completed');
  }
}
