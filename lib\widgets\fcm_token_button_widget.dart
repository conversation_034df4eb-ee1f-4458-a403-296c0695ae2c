import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:ecoplug/tests/fcm_token_test_service.dart';

/// Compact FCM Token Button Widget
/// A reusable button that can be added to any screen for quick FCM token generation
class FCMTokenButtonWidget extends StatefulWidget {
  final String? label;
  final IconData? icon;
  final ButtonStyle? style;
  final bool showAsCard;
  final bool showUsageInfo;

  const FCMTokenButtonWidget({
    super.key,
    this.label,
    this.icon,
    this.style,
    this.showAsCard = false,
    this.showUsageInfo = true,
  });

  @override
  State<FCMTokenButtonWidget> createState() => _FCMTokenButtonWidgetState();
}

class _FCMTokenButtonWidgetState extends State<FCMTokenButtonWidget> {
  bool _isGenerating = false;
  String? _lastGeneratedToken;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (widget.showAsCard) {
      return _buildCardVersion(theme);
    } else {
      return _buildButtonVersion(theme);
    }
  }

  Widget _buildButtonVersion(ThemeData theme) {
    return ElevatedButton.icon(
      onPressed: _isGenerating ? null : _generateAndShowToken,
      icon: _isGenerating
          ? const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(strokeWidth: 2),
            )
          : Icon(widget.icon ?? Icons.token),
      label: Text(
        _isGenerating 
            ? 'Generating...' 
            : (widget.label ?? 'Get FCM Token'),
      ),
      style: widget.style ?? ElevatedButton.styleFrom(
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: theme.colorScheme.onPrimary,
      ),
    );
  }

  Widget _buildCardVersion(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                Icon(
                  widget.icon ?? Icons.token,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'FCM Token Generator',
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Generate FCM tokens for notification testing.',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isGenerating ? null : _generateAndShowToken,
                    icon: _isGenerating
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.token),
                    label: Text(_isGenerating ? 'Generating...' : 'Generate Token'),
                  ),
                ),
                if (widget.showUsageInfo) ...[
                  const SizedBox(width: 8),
                  IconButton(
                    onPressed: _showUsageInfo,
                    icon: const Icon(Icons.info_outline),
                    tooltip: 'Usage Info',
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Generate FCM token and show in bottom sheet
  Future<void> _generateAndShowToken() async {
    setState(() => _isGenerating = true);

    try {
      final testService = FCMTokenTestService();
      final result = await testService.quickTokenTest();

      if (result['success'] == true) {
        final token = result['token'] as String;
        setState(() => _lastGeneratedToken = token);
        
        if (mounted) {
          _showTokenBottomSheet(token);
        }
      } else {
        _showSnackBar('Failed to generate FCM token: ${result['message']}', Colors.red);
      }
    } catch (e) {
      _showSnackBar('Error generating FCM token: $e', Colors.red);
    } finally {
      setState(() => _isGenerating = false);
    }
  }

  /// Show token in bottom sheet
  void _showTokenBottomSheet(String token) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.6,
        maxChildSize: 0.9,
        minChildSize: 0.4,
        expand: false,
        builder: (context, scrollController) => FCMTokenBottomSheet(
          token: token,
          scrollController: scrollController,
        ),
      ),
    );
  }

  /// Show usage information dialog
  void _showUsageInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('How to Use FCM Token'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                '🔥 Firebase Console:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              Text('Go to Firebase Console → Cloud Messaging → Send test message\n'),
              
              Text(
                '👥 Backend Team:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              Text('Share token with backend team for server-side testing\n'),
              
              Text(
                '🔧 API Testing:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              Text('Use with curl, Postman, or other API testing tools\n'),
              
              Text(
                'ℹ️ Note: Tokens are unique per app installation and may refresh periodically.',
                style: TextStyle(fontStyle: FontStyle.italic, fontSize: 12),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Got it'),
          ),
        ],
      ),
    );
  }

  /// Show snackbar message
  void _showSnackBar(String message, Color backgroundColor) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: backgroundColor,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }
}

/// FCM Token Bottom Sheet
class FCMTokenBottomSheet extends StatelessWidget {
  final String token;
  final ScrollController scrollController;

  const FCMTokenBottomSheet({
    super.key,
    required this.token,
    required this.scrollController,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Handle bar
          Center(
            child: Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.4),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Header
          Row(
            children: [
              Icon(
                Icons.check_circle,
                color: Colors.green,
              ),
              const SizedBox(width: 8),
              Text(
                'FCM Token Generated! 🎉',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.green,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Token info
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: theme.colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: theme.colorScheme.outline.withValues(alpha: 0.3),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Token Length: ${token.length} characters',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Preview:',
                  style: theme.textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${token.substring(0, 60)}...',
                  style: theme.textTheme.bodySmall?.copyWith(
                    fontFamily: 'monospace',
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),

          // Action buttons
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _copyToClipboard(context, token),
                  icon: const Icon(Icons.copy),
                  label: const Text('Copy Token'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () => _showFullToken(context, token),
                  icon: const Icon(Icons.visibility),
                  label: const Text('View Full'),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Usage instructions
          Expanded(
            child: SingleChildScrollView(
              controller: scrollController,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'How to Use This Token:',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  _buildUsageStep(
                    '1. Firebase Console Testing',
                    'Go to Firebase Console → Cloud Messaging → Send test message',
                    Icons.cloud,
                    theme,
                  ),
                  _buildUsageStep(
                    '2. Backend Team Testing',
                    'Share this token with your backend team for server-side notification testing',
                    Icons.group,
                    theme,
                  ),
                  _buildUsageStep(
                    '3. API Testing',
                    'Use with curl, Postman, or other API testing tools to send notifications',
                    Icons.api,
                    theme,
                  ),
                  const SizedBox(height: 16),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.info,
                          color: theme.colorScheme.primary,
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'Tokens are unique per app installation and may refresh periodically.',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.colorScheme.onPrimaryContainer,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUsageStep(String title, String description, IconData icon, ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            size: 20,
            color: theme.colorScheme.primary,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  description,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _copyToClipboard(BuildContext context, String token) {
    Clipboard.setData(ClipboardData(text: token));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('FCM token copied to clipboard! 📋'),
        backgroundColor: Colors.green,
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _showFullToken(BuildContext context, String token) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Full FCM Token'),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: SingleChildScrollView(
            child: SelectableText(
              token,
              style: const TextStyle(fontFamily: 'monospace', fontSize: 12),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          TextButton(
            onPressed: () {
              _copyToClipboard(context, token);
              Navigator.of(context).pop();
            },
            child: const Text('Copy & Close'),
          ),
        ],
      ),
    );
  }
}
