import 'package:flutter/material.dart';
import 'package:ecoplug/utils/app_themes.dart';
import 'package:ecoplug/models/station.dart';

/// Dynamic Station Card Widget that displays REAL API data
/// NO MORE HARDCODED VALUES - All data comes from Station model
class StationCard extends StatelessWidget {
  final Station station;
  final VoidCallback? onTap;

  const StationCard({
    super.key,
    required this.station,
    this.onTap,
  });

  // Color constants for connector status
  static final Color _chargingLimeGreen = AppThemes.primaryColor; // Use app theme color for charging connectors
  static const Color _gunConnectedYellow = Color(0xFFFFEB3B); // Yellow for gun connected
  static const Color _unifiedBadgeColor = Color(0xFF1E88E5); // Blue for other statuses

  /// Get connector color based on status with priority logic
  /// Priority: charging (lime green) > gun connected (yellow) > offline (red) > default (blue)
  Color _getConnectorColor(String? status) {
    if (status == null || status.isEmpty) {
      return _unifiedBadgeColor; // Default blue color
    }

    final statusLower = status.toLowerCase().trim();

    // Priority 1: If status is "charging" - use lime green
    if (statusLower == 'charging') {
      return _chargingLimeGreen;
    }

    // Priority 2: If status is "gun connected" - use yellow
    if (statusLower == 'gun connected') {
      return _gunConnectedYellow;
    }

    // Priority 3: If status is "offline" - use red
    if (statusLower == 'offline') {
      return Colors.red;
    }

    // Priority 4: For all other statuses - use blue
    return _unifiedBadgeColor;
  }

  /// Get appropriate icon for connector type
  IconData _getConnectorIcon(String? connectorType) {
    if (connectorType == null || connectorType.isEmpty) {
      return Icons.electric_bolt;
    }

    switch (connectorType.toLowerCase()) {
      case 'ccs2':
      case 'ccs':
      case 'ccs combo':
        return Icons.flash_on;
      case 'chademo':
        return Icons.electrical_services;
      case 'type2':
      case 'type 2':
        return Icons.power;
      case 'gb/t':
      case 'gbt':
        return Icons.bolt;
      case 'ac':
        return Icons.power;
      case 'dc':
        return Icons.flash_on;
      default:
        return Icons.electric_bolt;
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // Calculate real availability from API data
    final totalConnectors = station.connectors.length;
    final availableConnectors = station.connectors
        .where((connector) =>
            connector.status?.toLowerCase() == 'available' ||
            connector.status?.toLowerCase() == 'online' ||
            connector.status?.toLowerCase() == 'operational')
        .length;

    // Create availability text from real API data
    final availabilityText = totalConnectors > 0
        ? '$availableConnectors/$totalConnectors Connectors Available'
        : 'No connector data';

    // Determine station status color based on normalized status values
    Color statusColor;
    final statusLower = station.status.toLowerCase();

    if (statusLower == 'available') {
      statusColor = AppThemes.primaryColor;
    } else if (statusLower == 'closed') {
      statusColor = Colors.red;
    } else if (statusLower == 'in use') {
      statusColor = Colors.orange;
    } else {
      // Fallback for any other status values
      if (statusLower.contains('open') || statusLower.contains('available')) {
        statusColor = AppThemes.primaryColor;
      } else if (statusLower.contains('closed') || statusLower.contains('unavailable')) {
        statusColor = Colors.red;
      } else {
        statusColor = const Color.fromRGBO(255, 152, 0, 1); // Default for unknown statuses
      }
    }

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        // Add border for dark mode consistency
        side: isDarkMode
            ? BorderSide(color: AppThemes.darkBorder, width: 1)
            : BorderSide.none,
      ),
      color: Theme.of(context)
          .colorScheme
          .surface, // Use theme-adaptive surface color
      elevation: isDarkMode
          ? 0
          : 2, // No elevation in dark mode, subtle elevation in light mode
      child: InkWell(
        borderRadius: BorderRadius.circular(16),
        onTap: onTap ??
            () {
              // Default navigation if no custom onTap provided
              Navigator.of(context).pushNamed('/stationDetails', arguments: {
                'uid': station.uid,
                'station': station,
              });
            },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // Station icon with status indicator
              Stack(
                children: [
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary.withValues(
                          alpha:
                              0.1), // Use theme primary color with low opacity
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      Icons.ev_station,
                      color: Theme.of(context)
                          .colorScheme
                          .primary, // Use theme primary color
                      size: 28,
                    ),
                  ),
                  // Status indicator dot
                  Positioned(
                    right: 4,
                    top: 4,
                    child: Container(
                      width: 12,
                      height: 12,
                      decoration: BoxDecoration(
                        color: statusColor,
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: Theme.of(context)
                              .colorScheme
                              .surface, // Use theme-adaptive surface color for border
                          width: 2,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Real station name from API
                    Text(
                      station.name.isNotEmpty
                          ? station.name
                          : 'Station Name Not Available',
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                        color: Theme.of(context)
                            .colorScheme
                            .onSurface, // Use theme-adaptive text color
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    // Connector availability from API
                    Text(
                      availabilityText,
                      style: TextStyle(
                        fontSize: 14,
                        color: Theme.of(context).colorScheme.onSurface.withValues(
                            alpha:
                                0.7), // Use theme-adaptive secondary text color
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    // Real station status from API
                    Row(
                      children: [
                        Icon(
                          Icons.circle,
                          size: 8,
                          color: statusColor,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          station.status.isNotEmpty
                              ? station.status
                              : 'Status Unknown',
                          style: TextStyle(
                            fontSize: 12,
                            color: statusColor,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        if (station.rating > 0) ...[
                          const SizedBox(width: 8),
                          Icon(
                            Icons.star,
                            size: 12,
                            color: Colors.amber,
                          ),
                          const SizedBox(width: 2),
                          Text(
                            station.rating.toStringAsFixed(1),
                            style: TextStyle(
                              fontSize: 12,
                              color: Theme.of(context)
                                  .colorScheme
                                  .onSurface
                                  .withValues(
                                      alpha:
                                          0.6), // Use theme-adaptive tertiary text color
                            ),
                          ),
                        ],
                      ],
                    ),
                    const SizedBox(height: 8),
                    // Connector icons with status-based colors
                    if (station.connectors.isNotEmpty)
                      Wrap(
                        spacing: 6,
                        runSpacing: 4,
                        children: station.connectors.take(4).map((connector) {
                          final connectorColor = _getConnectorColor(connector.status);
                          return Container(
                            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 3),
                            decoration: BoxDecoration(
                              color: connectorColor.withValues(alpha: 0.15),
                              borderRadius: BorderRadius.circular(6),
                              border: Border.all(
                                color: connectorColor.withValues(alpha: 0.3),
                                width: 1,
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  _getConnectorIcon(connector.type),
                                  size: 12,
                                  color: connectorColor,
                                ),
                                const SizedBox(width: 3),
                                Text(
                                  connector.type ?? 'AC',
                                  style: TextStyle(
                                    fontSize: 10,
                                    fontWeight: FontWeight.w500,
                                    color: connectorColor,
                                  ),
                                ),
                              ],
                            ),
                          );
                        }).toList(),
                      ),
                  ],
                ),
              ),
              const SizedBox(width: 12),
              Icon(
                Icons.chevron_right,
                color: Theme.of(context)
                    .colorScheme
                    .onSurface
                    .withValues(alpha: 0.5), // Use theme-adaptive icon color
              ),
            ],
          ),
        ),
      ),
    );
  }
}
