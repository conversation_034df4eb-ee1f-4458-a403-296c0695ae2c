import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:fl_chart/fl_chart.dart';

class ConnectorAnalyticsPage extends StatefulWidget {
  final String connectorType;
  final String stationName;
  final String stationUid;

  const ConnectorAnalyticsPage({
    super.key,
    required this.connectorType,
    required this.stationName,
    required this.stationUid,
  });

  @override
  State<ConnectorAnalyticsPage> createState() => _ConnectorAnalyticsPageState();
}

class _ConnectorAnalyticsPageState extends State<ConnectorAnalyticsPage>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late AnimationController _pulseController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _pulseAnimation;

  int _selectedTimeRange = 0;
  bool _isRefreshing = false;
  double _scrollProgress = 0.0;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);

    // Initialize animations
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.5),
      end: Offset.zero,
    ).animate(
        CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic));
    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.1).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    // Start animations
    _fadeController.forward();
    _slideController.forward();
    _pulseController.repeat(reverse: true);

    // Scroll listener for progress indicator
    _scrollController.addListener(() {
      final maxScroll = _scrollController.position.maxScrollExtent;
      final currentScroll = _scrollController.position.pixels;
      setState(() {
        _scrollProgress = maxScroll > 0 ? currentScroll / maxScroll : 0.0;
      });
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _fadeController.dispose();
    _slideController.dispose();
    _pulseController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final size = MediaQuery.of(context).size;
    final isTablet = size.width > 600;
    final isLandscape = size.width > size.height;

    return Scaffold(
      backgroundColor: colorScheme.surface,
      body: RefreshIndicator(
        onRefresh: _handleRefresh,
        color: colorScheme.primary,
        backgroundColor: colorScheme.surface,
        strokeWidth: 3,
        child: CustomScrollView(
          controller: _scrollController,
          physics: const BouncingScrollPhysics(),
          slivers: [
            // Modern glass-morphism app bar
            _buildModernAppBar(colorScheme, isTablet, isLandscape),

            // Enhanced content with better spacing
            SliverToBoxAdapter(
              child: SlideTransition(
                position: _slideAnimation,
                child: _buildMainContent(colorScheme, isTablet, isLandscape),
              ),
            ),

            // Improved tab content
            SliverFillRemaining(
              child: _buildEnhancedTabView(colorScheme, isTablet, isLandscape),
            ),
          ],
        ),
      ),
      // Modern floating action button
      floatingActionButton: _buildScrollProgressFAB(colorScheme),
    );
  }

  Widget _buildModernAppBar(
      ColorScheme colorScheme, bool isTablet, bool isLandscape) {
    return SliverAppBar(
      expandedHeight:
          isTablet ? (isLandscape ? 180 : 240) : (isLandscape ? 160 : 200),
      floating: false,
      pinned: true,
      elevation: 0,
      backgroundColor: colorScheme.surface.withValues(alpha: 0.95),
      foregroundColor: colorScheme.onSurface,
      systemOverlayStyle: SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Theme.of(context).brightness == Brightness.dark
            ? Brightness.light
            : Brightness.dark,
      ),
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                colorScheme.primary.withValues(alpha: 0.1),
                colorScheme.secondary.withValues(alpha: 0.05),
                colorScheme.surface,
              ],
            ),
          ),
          child: SafeArea(
            child: Padding(
              padding: EdgeInsets.all(isTablet ? 28 : 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  FadeTransition(
                    opacity: _fadeAnimation,
                    child: Row(
                      children: [
                        // Modern animated icon
                        ScaleTransition(
                          scale: _pulseAnimation,
                          child: Container(
                            padding: EdgeInsets.all(isTablet ? 18 : 14),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  colorScheme.primary,
                                  colorScheme.secondary,
                                ],
                              ),
                              borderRadius: BorderRadius.circular(24),
                              boxShadow: [
                                BoxShadow(
                                  color: colorScheme.primary
                                      .withValues(alpha: 0.4),
                                  blurRadius: 16,
                                  offset: const Offset(0, 8),
                                ),
                              ],
                            ),
                            child: Icon(
                              Icons.analytics_rounded,
                              color: colorScheme.onPrimary,
                              size: isTablet ? 40 : 32,
                            ),
                          ),
                        ),
                        const SizedBox(width: 20),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Analytics',
                                style: TextStyle(
                                  fontSize: isTablet ? 36 : 28,
                                  fontWeight: FontWeight.w900,
                                  color: colorScheme.onSurface,
                                  letterSpacing: -1.0,
                                  height: 1.0,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: isTablet ? 16 : 12,
                                  vertical: isTablet ? 8 : 6,
                                ),
                                decoration: BoxDecoration(
                                  color: colorScheme.primary
                                      .withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(16),
                                  border: Border.all(
                                    color: colorScheme.primary
                                        .withValues(alpha: 0.2),
                                  ),
                                ),
                                child: Text(
                                  '${widget.connectorType} • ${widget.stationName}',
                                  style: TextStyle(
                                    fontSize: isTablet ? 16 : 14,
                                    color: colorScheme.primary,
                                    fontWeight: FontWeight.w600,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Enhanced scroll progress indicator
                  if (_scrollProgress > 0.02) ...[
                    const SizedBox(height: 20),
                    Container(
                      height: 4,
                      decoration: BoxDecoration(
                        color: colorScheme.outline.withValues(alpha: 0.15),
                        borderRadius: BorderRadius.circular(2),
                      ),
                      child: FractionallySizedBox(
                        alignment: Alignment.centerLeft,
                        widthFactor: _scrollProgress.clamp(0.0, 1.0),
                        child: Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                colorScheme.primary,
                                colorScheme.secondary,
                              ],
                            ),
                            borderRadius: BorderRadius.circular(2),
                            boxShadow: [
                              BoxShadow(
                                color:
                                    colorScheme.primary.withValues(alpha: 0.5),
                                blurRadius: 4,
                                offset: const Offset(0, 1),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ),
      ),
      actions: [
        _buildModernActionButton(
          icon: Icons.share_rounded,
          tooltip: 'Share Analytics',
          onPressed: _shareAnalytics,
          colorScheme: colorScheme,
        ),
        _buildModernActionButton(
          icon: _isRefreshing
              ? Icons.hourglass_empty_rounded
              : Icons.refresh_rounded,
          tooltip: 'Refresh Data',
          onPressed: _isRefreshing ? null : _refreshData,
          colorScheme: colorScheme,
        ),
        const SizedBox(width: 12),
      ],
    );
  }

  Widget _buildModernActionButton({
    required IconData icon,
    required String tooltip,
    required VoidCallback? onPressed,
    required ColorScheme colorScheme,
  }) {
    return Padding(
      padding: const EdgeInsets.only(right: 4),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed != null
              ? () {
                  HapticFeedback.lightImpact();
                  onPressed();
                }
              : null,
          borderRadius: BorderRadius.circular(16),
          child: Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: onPressed != null
                  ? colorScheme.primary.withValues(alpha: 0.1)
                  : colorScheme.outline.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: onPressed != null
                    ? colorScheme.primary.withValues(alpha: 0.2)
                    : colorScheme.outline.withValues(alpha: 0.2),
              ),
            ),
            child: Icon(
              icon,
              color:
                  onPressed != null ? colorScheme.primary : colorScheme.outline,
              size: 20,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMainContent(
      ColorScheme colorScheme, bool isTablet, bool isLandscape) {
    return Padding(
      padding: EdgeInsets.all(isTablet ? 24 : 16),
      child: Column(
        children: [
          // Enhanced Time Range Selector
          _buildResponsiveTimeRangeSelector(colorScheme, isTablet),
          SizedBox(height: isTablet ? 24 : 16),

          // Enhanced Tab Bar
          _buildResponsiveTabBar(colorScheme, isTablet),
          SizedBox(height: isTablet ? 24 : 16),
        ],
      ),
    );
  }

  Widget _buildResponsiveTimeRangeSelector(
      ColorScheme colorScheme, bool isTablet) {
    return Container(
      padding: const EdgeInsets.all(6),
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withValues(alpha: 0.15),
            blurRadius: 12,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Row(
        children: [
          _buildTimeRangeButton('Week', 0, colorScheme, isTablet),
          _buildTimeRangeButton('Month', 1, colorScheme, isTablet),
          _buildTimeRangeButton('Year', 2, colorScheme, isTablet),
        ],
      ),
    );
  }

  Widget _buildTimeRangeButton(
      String label, int index, ColorScheme colorScheme, bool isTablet) {
    final isSelected = _selectedTimeRange == index;
    return Expanded(
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            HapticFeedback.selectionClick();
            setState(() => _selectedTimeRange = index);
          },
          borderRadius: BorderRadius.circular(16),
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOutCubic,
            padding: EdgeInsets.symmetric(
              vertical: isTablet ? 18 : 14,
              horizontal: 12,
            ),
            decoration: BoxDecoration(
              color: isSelected ? colorScheme.primary : Colors.transparent,
              borderRadius: BorderRadius.circular(16),
              boxShadow: isSelected
                  ? [
                      BoxShadow(
                        color: colorScheme.primary.withValues(alpha: 0.4),
                        blurRadius: 12,
                        offset: const Offset(0, 4),
                        spreadRadius: 0,
                      ),
                    ]
                  : null,
            ),
            child: AnimatedDefaultTextStyle(
              duration: const Duration(milliseconds: 300),
              style: TextStyle(
                color: isSelected
                    ? colorScheme.onPrimary
                    : colorScheme.onSurfaceVariant,
                fontWeight: isSelected ? FontWeight.w700 : FontWeight.w600,
                fontSize: isTablet ? 17 : 15,
                letterSpacing: isSelected ? -0.2 : 0,
              ),
              child: Text(
                label,
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildResponsiveTabBar(ColorScheme colorScheme, bool isTablet) {
    return Container(
      padding: const EdgeInsets.all(6),
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withValues(alpha: 0.15),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: TabBar(
        controller: _tabController,
        onTap: (index) => HapticFeedback.selectionClick(),
        indicator: BoxDecoration(
          gradient: LinearGradient(
            colors: [colorScheme.primary, colorScheme.secondary],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: colorScheme.primary.withValues(alpha: 0.4),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        labelColor: colorScheme.onPrimary,
        unselectedLabelColor: colorScheme.onSurfaceVariant,
        dividerColor: Colors.transparent,
        labelStyle: TextStyle(
          fontWeight: FontWeight.w700,
          fontSize: isTablet ? 17 : 15,
          letterSpacing: -0.2,
        ),
        unselectedLabelStyle: TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: isTablet ? 17 : 15,
        ),
        tabs: [
          Tab(
            child: Padding(
              padding: EdgeInsets.symmetric(vertical: isTablet ? 14 : 12),
              child: const Text('Usage'),
            ),
          ),
          Tab(
            child: Padding(
              padding: EdgeInsets.symmetric(vertical: isTablet ? 14 : 12),
              child: const Text('Energy'),
            ),
          ),
          Tab(
            child: Padding(
              padding: EdgeInsets.symmetric(vertical: isTablet ? 14 : 12),
              child: const Text('Sessions'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEnhancedTabView(
      ColorScheme colorScheme, bool isTablet, bool isLandscape) {
    return TabBarView(
      controller: _tabController,
      physics: const BouncingScrollPhysics(),
      children: [
        _buildUsageAnalytics(colorScheme, isTablet, isLandscape),
        _buildEnergyAnalytics(colorScheme, isTablet, isLandscape),
        _buildSessionsAnalytics(colorScheme, isTablet, isLandscape),
      ],
    );
  }

  Widget _buildScrollProgressFAB(ColorScheme colorScheme) {
    return AnimatedOpacity(
      opacity: _scrollProgress > 0.1 ? 1.0 : 0.0,
      duration: const Duration(milliseconds: 300),
      child: FloatingActionButton.small(
        onPressed: () {
          HapticFeedback.lightImpact();
          _scrollController.animateTo(
            0,
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeOutCubic,
          );
        },
        backgroundColor: colorScheme.primary,
        foregroundColor: colorScheme.onPrimary,
        elevation: 8,
        child: const Icon(Icons.keyboard_arrow_up),
      ),
    );
  }

  // Enhanced helper methods
  Future<void> _handleRefresh() async {
    setState(() => _isRefreshing = true);
    HapticFeedback.mediumImpact();

    await Future.delayed(const Duration(milliseconds: 1500));

    if (mounted) {
      setState(() => _isRefreshing = false);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Data refreshed successfully'),
          backgroundColor: Theme.of(context).colorScheme.primary,
          behavior: SnackBarBehavior.floating,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  void _shareAnalytics() {
    HapticFeedback.lightImpact();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Share functionality coming soon'),
        backgroundColor: Theme.of(context).colorScheme.secondary,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _refreshData() {
    setState(() => _isRefreshing = true);
    HapticFeedback.mediumImpact();

    Future.delayed(const Duration(milliseconds: 1500), () {
      if (mounted) {
        setState(() => _isRefreshing = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Data refreshed successfully'),
            backgroundColor: Theme.of(context).colorScheme.primary,
            behavior: SnackBarBehavior.floating,
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            duration: const Duration(seconds: 2),
          ),
        );
      }
    });
  }

  // Enhanced analytics sections with improved responsiveness
  Widget _buildUsageAnalytics(
      ColorScheme colorScheme, bool isTablet, bool isLandscape) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(isTablet ? 24 : 16),
      physics: const BouncingScrollPhysics(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Enhanced Usage Chart with touch interactions
          _buildInteractiveChartCard(
            title: 'Daily Usage Hours',
            subtitle: 'Track your charging patterns over time',
            icon: Icons.schedule_outlined,
            child: SizedBox(
              height: isTablet
                  ? (isLandscape ? 250 : 320)
                  : (isLandscape ? 200 : 260),
              child: LineChart(_buildUsageLineChart(colorScheme)),
            ),
            colorScheme: colorScheme,
            isTablet: isTablet,
          ),

          SizedBox(height: isTablet ? 28 : 20),

          // Enhanced responsive stats grid
          _buildResponsiveStatsGrid(colorScheme, isTablet, isLandscape),

          SizedBox(height: isTablet ? 28 : 20),

          // Enhanced insights with animations
          _buildAnimatedInsightsCard(colorScheme, isTablet),
        ],
      ),
    );
  }

  Widget _buildEnergyAnalytics(
      ColorScheme colorScheme, bool isTablet, bool isLandscape) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(isTablet ? 24 : 16),
      physics: const BouncingScrollPhysics(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildInteractiveChartCard(
            title: 'Energy Consumption',
            subtitle: 'Monitor your energy usage patterns',
            icon: Icons.bolt_outlined,
            child: SizedBox(
              height: isTablet
                  ? (isLandscape ? 250 : 320)
                  : (isLandscape ? 200 : 260),
              child: BarChart(_buildEnergyBarChart(colorScheme)),
            ),
            colorScheme: colorScheme,
            isTablet: isTablet,
          ),

          SizedBox(height: isTablet ? 28 : 20),

          // Enhanced efficiency card with animations
          _buildEnhancedEfficiencyCard(colorScheme, isTablet),

          SizedBox(height: isTablet ? 28 : 20),

          _buildResponsiveEnergyTrendsCard(colorScheme, isTablet, isLandscape),
        ],
      ),
    );
  }

  Widget _buildSessionsAnalytics(
      ColorScheme colorScheme, bool isTablet, bool isLandscape) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(isTablet ? 24 : 16),
      physics: const BouncingScrollPhysics(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildInteractiveChartCard(
            title: 'Session Distribution',
            subtitle: 'Analyze your charging session patterns',
            icon: Icons.pie_chart_outline,
            child: SizedBox(
              height: isTablet
                  ? (isLandscape ? 250 : 320)
                  : (isLandscape ? 200 : 260),
              child: PieChart(_buildSessionsPieChart(colorScheme)),
            ),
            colorScheme: colorScheme,
            isTablet: isTablet,
          ),
          SizedBox(height: isTablet ? 28 : 20),
          _buildInteractiveSessionsList(colorScheme, isTablet),
          SizedBox(height: isTablet ? 28 : 20),
          _buildAnimatedSessionInsightsCard(colorScheme, isTablet),
        ],
      ),
    );
  }

  // Enhanced UI Components with better responsiveness and interactions
  Widget _buildInteractiveChartCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required Widget child,
    required ColorScheme colorScheme,
    required bool isTablet,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          HapticFeedback.lightImpact();
          // Add chart interaction feedback
        },
        borderRadius: BorderRadius.circular(28),
        child: Container(
          decoration: BoxDecoration(
            color: colorScheme.surface,
            borderRadius: BorderRadius.circular(28),
            boxShadow: [
              BoxShadow(
                color: colorScheme.shadow.withValues(alpha: 0.12),
                blurRadius: 24,
                offset: const Offset(0, 8),
                spreadRadius: 0,
              ),
              BoxShadow(
                color: colorScheme.primary.withValues(alpha: 0.05),
                blurRadius: 40,
                offset: const Offset(0, 16),
                spreadRadius: 0,
              ),
            ],
          ),
          child: Padding(
            padding: EdgeInsets.all(isTablet ? 28 : 24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: EdgeInsets.all(isTablet ? 16 : 14),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            colorScheme.primary.withValues(alpha: 0.15),
                            colorScheme.secondary.withValues(alpha: 0.1),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color: colorScheme.primary.withValues(alpha: 0.2),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Icon(
                        icon,
                        color: colorScheme.primary,
                        size: isTablet ? 32 : 28,
                      ),
                    ),
                    const SizedBox(width: 20),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            title,
                            style: TextStyle(
                              fontSize: isTablet ? 22 : 20,
                              fontWeight: FontWeight.w800,
                              color: colorScheme.onSurface,
                              letterSpacing: -0.5,
                              height: 1.2,
                            ),
                          ),
                          const SizedBox(height: 6),
                          Text(
                            subtitle,
                            style: TextStyle(
                              fontSize: isTablet ? 15 : 13,
                              color: colorScheme.onSurfaceVariant,
                              fontWeight: FontWeight.w500,
                              height: 1.3,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                SizedBox(height: isTablet ? 28 : 24),
                child,
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildResponsiveStatsGrid(
      ColorScheme colorScheme, bool isTablet, bool isLandscape) {
    final crossAxisCount =
        isTablet ? (isLandscape ? 4 : 2) : (isLandscape ? 4 : 2);
    final childAspectRatio = isTablet ? 1.4 : (isLandscape ? 1.2 : 1.3);

    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: crossAxisCount,
      crossAxisSpacing: isTablet ? 20 : 16,
      mainAxisSpacing: isTablet ? 20 : 16,
      childAspectRatio: childAspectRatio,
      children: [
        _buildInteractiveStatCard(
          'Total Hours',
          '24.5h',
          Icons.access_time_outlined,
          colorScheme,
          isTablet,
          const LinearGradient(
            colors: [Color(0xFF4ADE80), Color(0xFF22C55E)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        _buildInteractiveStatCard(
          'Avg Session',
          '2.1h',
          Icons.trending_up_outlined,
          colorScheme,
          isTablet,
          const LinearGradient(
            colors: [Color(0xFF60A5FA), Color(0xFF3B82F6)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        _buildInteractiveStatCard(
          'Peak Usage',
          '18:00',
          Icons.schedule_outlined,
          colorScheme,
          isTablet,
          const LinearGradient(
            colors: [Color(0xFFFBBF24), Color(0xFFF59E0B)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        _buildInteractiveStatCard(
          'Efficiency',
          '94%',
          Icons.battery_charging_full_outlined,
          colorScheme,
          isTablet,
          const LinearGradient(
            colors: [Color(0xFF34D399), Color(0xFF10B981)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
      ],
    );
  }

  Widget _buildInteractiveStatCard(
    String title,
    String value,
    IconData icon,
    ColorScheme colorScheme,
    bool isTablet,
    LinearGradient gradient,
  ) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          HapticFeedback.lightImpact();
          // Add stat interaction
        },
        borderRadius: BorderRadius.circular(24),
        child: Container(
          decoration: BoxDecoration(
            color: colorScheme.surface,
            borderRadius: BorderRadius.circular(24),
            boxShadow: [
              BoxShadow(
                color: colorScheme.shadow.withValues(alpha: 0.1),
                blurRadius: 20,
                offset: const Offset(0, 8),
                spreadRadius: 0,
              ),
            ],
          ),
          child: Stack(
            children: [
              // Enhanced gradient overlay
              Positioned(
                top: 0,
                right: 0,
                child: Container(
                  width: isTablet ? 100 : 80,
                  height: isTablet ? 100 : 80,
                  decoration: BoxDecoration(
                    gradient: gradient.scale(0.08),
                    borderRadius: BorderRadius.only(
                      topRight: Radius.circular(24),
                      bottomLeft: Radius.circular(isTablet ? 100 : 80),
                    ),
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsets.all(isTablet ? 24 : 20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      padding: EdgeInsets.all(isTablet ? 12 : 10),
                      decoration: BoxDecoration(
                        gradient: gradient.scale(0.9),
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: gradient.colors.first.withValues(alpha: 0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Icon(
                        icon,
                        color: Colors.white,
                        size: isTablet ? 28 : 24,
                      ),
                    ),
                    const Spacer(),
                    Text(
                      value,
                      style: TextStyle(
                        fontSize: isTablet ? 28 : 24,
                        fontWeight: FontWeight.w800,
                        color: colorScheme.onSurface,
                        letterSpacing: -0.8,
                        height: 1.1,
                      ),
                    ),
                    const SizedBox(height: 6),
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: isTablet ? 15 : 13,
                        color: colorScheme.onSurfaceVariant,
                        fontWeight: FontWeight.w600,
                        height: 1.2,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Enhanced chart data methods with better interactivity
  LineChartData _buildUsageLineChart(ColorScheme colorScheme) {
    return LineChartData(
      gridData: FlGridData(
        show: true,
        drawVerticalLine: false,
        horizontalInterval: 2,
        getDrawingHorizontalLine: (value) => FlLine(
          color: colorScheme.outline.withValues(alpha: 0.15),
          strokeWidth: 1,
        ),
      ),
      titlesData: FlTitlesData(
        leftTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            reservedSize: 45,
            getTitlesWidget: (value, meta) => Padding(
              padding: const EdgeInsets.only(right: 8),
              child: Text(
                '${value.toInt()}h',
                style: TextStyle(
                  color: colorScheme.onSurfaceVariant,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ),
        bottomTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            reservedSize: 35,
            getTitlesWidget: (value, meta) {
              const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
              return Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Text(
                  days[value.toInt() % 7],
                  style: TextStyle(
                    color: colorScheme.onSurfaceVariant,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              );
            },
          ),
        ),
        rightTitles:
            const AxisTitles(sideTitles: SideTitles(showTitles: false)),
        topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
      ),
      borderData: FlBorderData(show: false),
      lineTouchData: LineTouchData(
        enabled: true,
        touchTooltipData: LineTouchTooltipData(
          getTooltipColor: (touchedSpot) => colorScheme.inverseSurface,
          tooltipRoundedRadius: 12,
          tooltipPadding: const EdgeInsets.all(12),
          getTooltipItems: (List<LineBarSpot> touchedBarSpots) {
            return touchedBarSpots.map((barSpot) {
              return LineTooltipItem(
                '${barSpot.y.toStringAsFixed(1)}h',
                TextStyle(
                  color: colorScheme.onInverseSurface,
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
              );
            }).toList();
          },
        ),
        handleBuiltInTouches: true,
        getTouchedSpotIndicator:
            (LineChartBarData barData, List<int> spotIndexes) {
          return spotIndexes.map((spotIndex) {
            return TouchedSpotIndicatorData(
              FlLine(
                color: colorScheme.primary,
                strokeWidth: 3,
              ),
              FlDotData(
                getDotPainter: (spot, percent, barData, index) =>
                    FlDotCirclePainter(
                  radius: 6,
                  color: colorScheme.primary,
                  strokeWidth: 3,
                  strokeColor: colorScheme.surface,
                ),
              ),
            );
          }).toList();
        },
      ),
      lineBarsData: [
        LineChartBarData(
          spots: const [
            FlSpot(0, 3.2),
            FlSpot(1, 4.1),
            FlSpot(2, 2.8),
            FlSpot(3, 5.2),
            FlSpot(4, 3.9),
            FlSpot(5, 6.1),
            FlSpot(6, 4.5),
          ],
          isCurved: true,
          gradient: LinearGradient(
            colors: [
              colorScheme.primary,
              colorScheme.secondary,
            ],
          ),
          barWidth: 4,
          isStrokeCapRound: true,
          dotData: FlDotData(
            show: true,
            getDotPainter: (spot, percent, barData, index) =>
                FlDotCirclePainter(
              radius: 5,
              color: colorScheme.primary,
              strokeWidth: 3,
              strokeColor: colorScheme.surface,
            ),
          ),
          belowBarData: BarAreaData(
            show: true,
            gradient: LinearGradient(
              colors: [
                colorScheme.primary.withValues(alpha: 0.2),
                colorScheme.primary.withValues(alpha: 0.05),
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
        ),
      ],
    );
  }

  // Add additional enhanced components
  Widget _buildAnimatedInsightsCard(ColorScheme colorScheme, bool isTablet) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: _buildInsightsCard(colorScheme, isTablet),
    );
  }

  Widget _buildEnhancedEfficiencyCard(ColorScheme colorScheme, bool isTablet) {
    return Container(
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withValues(alpha: 0.12),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => HapticFeedback.lightImpact(),
          borderRadius: BorderRadius.circular(24),
          child: Padding(
            padding: EdgeInsets.all(isTablet ? 28 : 24),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Energy Efficiency',
                        style: TextStyle(
                          fontSize: isTablet ? 22 : 20,
                          fontWeight: FontWeight.w800,
                          color: colorScheme.onSurface,
                          letterSpacing: -0.5,
                        ),
                      ),
                      SizedBox(height: isTablet ? 20 : 16),
                      ScaleTransition(
                        scale: _pulseAnimation,
                        child: Text(
                          '94.2%',
                          style: TextStyle(
                            fontSize: isTablet ? 42 : 36,
                            fontWeight: FontWeight.w900,
                            color: colorScheme.primary,
                            letterSpacing: -1.5,
                            height: 1.0,
                          ),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Overall Efficiency',
                        style: TextStyle(
                          fontSize: isTablet ? 16 : 15,
                          color: colorScheme.onSurfaceVariant,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: EdgeInsets.all(isTablet ? 24 : 20),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        colorScheme.primary.withValues(alpha: 0.15),
                        colorScheme.secondary.withValues(alpha: 0.1),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(24),
                    boxShadow: [
                      BoxShadow(
                        color: colorScheme.primary.withValues(alpha: 0.2),
                        blurRadius: 12,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Icon(
                    Icons.eco_outlined,
                    size: isTablet ? 64 : 56,
                    color: colorScheme.primary,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildResponsiveEnergyTrendsCard(
      ColorScheme colorScheme, bool isTablet, bool isLandscape) {
    return Container(
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withValues(alpha: 0.12),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => HapticFeedback.lightImpact(),
          borderRadius: BorderRadius.circular(24),
          child: Padding(
            padding: EdgeInsets.all(isTablet ? 28 : 24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: EdgeInsets.all(isTablet ? 16 : 14),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            colorScheme.secondary.withValues(alpha: 0.15),
                            colorScheme.tertiary.withValues(alpha: 0.1),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color: colorScheme.secondary.withValues(alpha: 0.2),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Icon(
                        Icons.trending_up_outlined,
                        color: colorScheme.secondary,
                        size: isTablet ? 32 : 28,
                      ),
                    ),
                    const SizedBox(width: 20),
                    Text(
                      'Energy Trends',
                      style: TextStyle(
                        fontSize: isTablet ? 22 : 20,
                        fontWeight: FontWeight.w800,
                        color: colorScheme.onSurface,
                        letterSpacing: -0.5,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: isTablet ? 24 : 20),
                isLandscape
                    ? Row(
                        children: [
                          Expanded(
                            child: _buildTrendItem(
                              'This Week',
                              '245.6 kWh',
                              '+12%',
                              isTablet: isTablet,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: _buildTrendItem(
                              'Last Week',
                              '219.2 kWh',
                              '-5%',
                              isTablet: isTablet,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: _buildTrendItem(
                              'This Month',
                              '1,024.3 kWh',
                              '+8%',
                              isTablet: isTablet,
                            ),
                          ),
                        ],
                      )
                    : Column(
                        children: [
                          Row(
                            children: [
                              Expanded(
                                child: _buildTrendItem(
                                  'This Week',
                                  '245.6 kWh',
                                  '+12%',
                                  isTablet: isTablet,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: _buildTrendItem(
                                  'Last Week',
                                  '219.2 kWh',
                                  '-5%',
                                  isTablet: isTablet,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          _buildTrendItem(
                            'This Month',
                            '1,024.3 kWh',
                            '+8%',
                            isTablet: isTablet,
                          ),
                        ],
                      ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInteractiveSessionsList(ColorScheme colorScheme, bool isTablet) {
    return Container(
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withValues(alpha: 0.12),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.all(isTablet ? 28 : 24),
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.all(isTablet ? 16 : 14),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        colorScheme.primary.withValues(alpha: 0.15),
                        colorScheme.secondary.withValues(alpha: 0.1),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: colorScheme.primary.withValues(alpha: 0.2),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Icon(
                    Icons.history_outlined,
                    color: colorScheme.primary,
                    size: isTablet ? 32 : 28,
                  ),
                ),
                const SizedBox(width: 20),
                Text(
                  'Recent Sessions',
                  style: TextStyle(
                    fontSize: isTablet ? 22 : 20,
                    fontWeight: FontWeight.w800,
                    color: colorScheme.onSurface,
                    letterSpacing: -0.5,
                  ),
                ),
              ],
            ),
          ),
          ...List.generate(
              5,
              (index) =>
                  _buildInteractiveSessionItem(index, colorScheme, isTablet)),
          SizedBox(height: isTablet ? 16 : 12),
        ],
      ),
    );
  }

  Widget _buildInteractiveSessionItem(
      int index, ColorScheme colorScheme, bool isTablet) {
    final sessions = [
      {
        'date': 'Today',
        'duration': '2.5h',
        'energy': '45.2 kWh',
        'status': 'completed'
      },
      {
        'date': 'Yesterday',
        'duration': '1.8h',
        'energy': '32.1 kWh',
        'status': 'completed'
      },
      {
        'date': '2 days ago',
        'duration': '3.2h',
        'energy': '58.7 kWh',
        'status': 'completed'
      },
      {
        'date': '3 days ago',
        'duration': '2.1h',
        'energy': '38.9 kWh',
        'status': 'interrupted'
      },
      {
        'date': '4 days ago',
        'duration': '2.8h',
        'energy': '51.3 kWh',
        'status': 'completed'
      },
    ];

    final session = sessions[index];
    final isCompleted = session['status'] == 'completed';

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          HapticFeedback.lightImpact();
          // Show session details
        },
        child: Padding(
          padding: EdgeInsets.symmetric(
            horizontal: isTablet ? 28 : 24,
            vertical: isTablet ? 16 : 12,
          ),
          child: Row(
            children: [
              // Status indicator
              Container(
                width: isTablet ? 16 : 14,
                height: isTablet ? 16 : 14,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: isCompleted
                        ? [colorScheme.primary, colorScheme.secondary]
                        : [Colors.grey.shade300, Colors.grey.shade400],
                  ),
                  shape: BoxShape.circle,
                ),
              ),
              SizedBox(width: isTablet ? 16 : 12),
              // Session details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      session['date'] ?? '',
                      style: TextStyle(
                        fontSize: isTablet ? 16 : 14,
                        fontWeight: FontWeight.w600,
                        color: colorScheme.onSurface,
                      ),
                    ),
                    SizedBox(height: isTablet ? 6 : 4),
                    Row(
                      children: [
                        Text(
                          session['duration'] ?? '',
                          style: TextStyle(
                            fontSize: isTablet ? 14 : 12,
                            color: colorScheme.onSurface.withValues(alpha: 0.7),
                          ),
                        ),
                        SizedBox(width: isTablet ? 16 : 12),
                        Text(
                          session['energy'] ?? '',
                          style: TextStyle(
                            fontSize: isTablet ? 14 : 12,
                            color: colorScheme.onSurface.withValues(alpha: 0.7),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              // Status badge
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: isTablet ? 12 : 8,
                  vertical: isTablet ? 6 : 4,
                ),
                decoration: BoxDecoration(
                  color: isCompleted
                      ? colorScheme.primary.withValues(alpha: 0.1)
                      : Colors.orange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(isTablet ? 8 : 6),
                ),
                child: Text(
                  session['status'] ?? '',
                  style: TextStyle(
                    fontSize: isTablet ? 12 : 10,
                    fontWeight: FontWeight.w500,
                    color: isCompleted ? colorScheme.primary : Colors.orange,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Helper method to build trend items
  Widget _buildTrendItem(String title, String value, String change,
      {bool isTablet = false}) {
    final isPositive = change.startsWith('+');
    final colorScheme = Theme.of(context).colorScheme;

    return Container(
      padding: EdgeInsets.all(isTablet ? 16 : 12),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(isTablet ? 12 : 8),
        border: Border.all(
          color: colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: isTablet ? 14 : 12,
              color: colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          SizedBox(height: isTablet ? 8 : 6),
          Text(
            value,
            style: TextStyle(
              fontSize: isTablet ? 18 : 16,
              fontWeight: FontWeight.bold,
              color: colorScheme.onSurface,
            ),
          ),
          SizedBox(height: isTablet ? 6 : 4),
          Row(
            children: [
              Icon(
                isPositive ? Icons.trending_up : Icons.trending_down,
                size: isTablet ? 16 : 14,
                color: isPositive ? Colors.green : Colors.red,
              ),
              SizedBox(width: 4),
              Text(
                change,
                style: TextStyle(
                  fontSize: isTablet ? 12 : 10,
                  fontWeight: FontWeight.w500,
                  color: isPositive ? Colors.green : Colors.red,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Energy bar chart data
  BarChartData _buildEnergyBarChart(ColorScheme colorScheme) {
    return BarChartData(
      alignment: BarChartAlignment.spaceAround,
      maxY: 100,
      barTouchData: BarTouchData(
        enabled: true,
        touchTooltipData: BarTouchTooltipData(
          getTooltipColor: (group) => colorScheme.inverseSurface,
          tooltipRoundedRadius: 8,
          getTooltipItem: (group, groupIndex, rod, rodIndex) {
            return BarTooltipItem(
              '${rod.toY.toStringAsFixed(1)} kWh',
              TextStyle(
                color: colorScheme.onInverseSurface,
                fontWeight: FontWeight.bold,
              ),
            );
          },
        ),
      ),
      titlesData: FlTitlesData(
        leftTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            reservedSize: 40,
            getTitlesWidget: (value, meta) => Text(
              '${value.toInt()}',
              style: TextStyle(
                color: colorScheme.onSurfaceVariant,
                fontSize: 12,
              ),
            ),
          ),
        ),
        bottomTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            reservedSize: 30,
            getTitlesWidget: (value, meta) {
              const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
              return Text(
                days[value.toInt() % 7],
                style: TextStyle(
                  color: colorScheme.onSurfaceVariant,
                  fontSize: 12,
                ),
              );
            },
          ),
        ),
        rightTitles:
            const AxisTitles(sideTitles: SideTitles(showTitles: false)),
        topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
      ),
      borderData: FlBorderData(show: false),
      barGroups: [
        BarChartGroupData(x: 0, barRods: [
          BarChartRodData(toY: 45, color: colorScheme.primary, width: 16)
        ]),
        BarChartGroupData(x: 1, barRods: [
          BarChartRodData(toY: 65, color: colorScheme.primary, width: 16)
        ]),
        BarChartGroupData(x: 2, barRods: [
          BarChartRodData(toY: 35, color: colorScheme.primary, width: 16)
        ]),
        BarChartGroupData(x: 3, barRods: [
          BarChartRodData(toY: 80, color: colorScheme.primary, width: 16)
        ]),
        BarChartGroupData(x: 4, barRods: [
          BarChartRodData(toY: 55, color: colorScheme.primary, width: 16)
        ]),
        BarChartGroupData(x: 5, barRods: [
          BarChartRodData(toY: 90, color: colorScheme.primary, width: 16)
        ]),
        BarChartGroupData(x: 6, barRods: [
          BarChartRodData(toY: 70, color: colorScheme.primary, width: 16)
        ]),
      ],
      gridData: FlGridData(
        show: true,
        drawVerticalLine: false,
        horizontalInterval: 20,
        getDrawingHorizontalLine: (value) => FlLine(
          color: colorScheme.outline.withValues(alpha: 0.2),
          strokeWidth: 1,
        ),
      ),
    );
  }

  // Sessions pie chart data
  PieChartData _buildSessionsPieChart(ColorScheme colorScheme) {
    return PieChartData(
      sectionsSpace: 2,
      centerSpaceRadius: 60,
      startDegreeOffset: -90,
      sections: [
        PieChartSectionData(
          color: colorScheme.primary,
          value: 45,
          title: '45%',
          radius: 80,
          titleStyle: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        PieChartSectionData(
          color: colorScheme.secondary,
          value: 30,
          title: '30%',
          radius: 75,
          titleStyle: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        PieChartSectionData(
          color: colorScheme.tertiary,
          value: 15,
          title: '15%',
          radius: 70,
          titleStyle: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        PieChartSectionData(
          color: Colors.grey.shade400,
          value: 10,
          title: '10%',
          radius: 65,
          titleStyle: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      ],
      pieTouchData: PieTouchData(
        enabled: true,
        touchCallback: (FlTouchEvent event, pieTouchResponse) {
          // Handle touch interactions
        },
      ),
    );
  }

  // Animated session insights card
  Widget _buildAnimatedSessionInsightsCard(
      ColorScheme colorScheme, bool isTablet) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        padding: EdgeInsets.all(isTablet ? 24 : 20),
        decoration: BoxDecoration(
          color: colorScheme.surface,
          borderRadius: BorderRadius.circular(24),
          boxShadow: [
            BoxShadow(
              color: colorScheme.shadow.withValues(alpha: 0.12),
              blurRadius: 20,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(isTablet ? 16 : 14),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        colorScheme.primary.withValues(alpha: 0.15),
                        colorScheme.secondary.withValues(alpha: 0.1),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Icon(
                    Icons.insights_outlined,
                    color: colorScheme.primary,
                    size: isTablet ? 32 : 28,
                  ),
                ),
                const SizedBox(width: 16),
                Text(
                  'Session Insights',
                  style: TextStyle(
                    fontSize: isTablet ? 22 : 20,
                    fontWeight: FontWeight.w800,
                    color: colorScheme.onSurface,
                  ),
                ),
              ],
            ),
            SizedBox(height: isTablet ? 20 : 16),
            Text(
              'Your charging patterns show optimal usage during peak hours with 94% efficiency rate.',
              style: TextStyle(
                fontSize: isTablet ? 16 : 14,
                color: colorScheme.onSurface.withValues(alpha: 0.7),
                height: 1.5,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Basic insights card
  Widget _buildInsightsCard(ColorScheme colorScheme, bool isTablet) {
    return Container(
      padding: EdgeInsets.all(isTablet ? 24 : 20),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withValues(alpha: 0.12),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(isTablet ? 16 : 14),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      colorScheme.tertiary.withValues(alpha: 0.15),
                      colorScheme.primary.withValues(alpha: 0.1),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Icon(
                  Icons.lightbulb_outline,
                  color: colorScheme.tertiary,
                  size: isTablet ? 32 : 28,
                ),
              ),
              const SizedBox(width: 16),
              Text(
                'Usage Insights',
                style: TextStyle(
                  fontSize: isTablet ? 22 : 20,
                  fontWeight: FontWeight.w800,
                  color: colorScheme.onSurface,
                ),
              ),
            ],
          ),
          SizedBox(height: isTablet ? 20 : 16),
          Text(
            'Peak usage occurs between 6-8 PM. Consider scheduling charges during off-peak hours for better rates.',
            style: TextStyle(
              fontSize: isTablet ? 16 : 14,
              color: colorScheme.onSurface.withValues(alpha: 0.7),
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }
}
