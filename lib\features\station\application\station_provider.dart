import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ecoplug/core/services/connectivity_service.dart';
import 'package:ecoplug/features/station/domain/station_state.dart';
import 'package:ecoplug/features/station/domain/station_list_mode.dart';
import 'package:ecoplug/features/station/services/station_service.dart';

import 'package:ecoplug/repositories/station_repository.dart';
import 'package:ecoplug/core/api/api_service.dart';
import 'package:ecoplug/models/station.dart';

/// Station notifier class to manage station state
class StationsNotifier extends StateNotifier<StationState> {
  final StationService _stationService;
  final Ref _ref;

  StationsNotifier(this._ref)
      : _stationService = _ref.read(stationServiceProvider),
        super(StationState.initial()) {
    fetchInitialStations();
  }

  Future<void> fetchInitialStations() async {
    state = state.copyWith(
        isLoading: true,
        mode: StationListMode.list,
        stations: [],
        currentPage: 1,
        currentSearchQuery: '');
    try {
      final response =
          await _stationService.fetchStationsPaginated(page: 1, limit: 10);

      // Convert PaginatedStation to Station objects
      final stations = response.data
              ?.where((paginatedStation) {
                return paginatedStation.name != null &&
                    paginatedStation.name!.isNotEmpty &&
                    paginatedStation.address != null &&
                    paginatedStation.address!.isNotEmpty &&
                    paginatedStation.latitude != null &&
                    paginatedStation.longitude != null &&
                    paginatedStation.uid != null &&
                    paginatedStation.uid!.isNotEmpty;
              })
              .map((paginatedStation) => Station(
                    id: paginatedStation.stationId?.toString() ?? '',
                    name: paginatedStation.name!,
                    address: paginatedStation.address!,
                    city: paginatedStation.city,
                    state: null,
                    latitude: paginatedStation.latitude!,
                    longitude: paginatedStation.longitude!,
                    distance: paginatedStation.distance ?? 0.0,
                    status: paginatedStation.status ?? 'Unknown',
                    rating: paginatedStation.rating ?? 0.0,
                    reviews: paginatedStation.reviewCount ?? 0,
                    connectors: [],
                    images: paginatedStation.imageUrl != null
                        ? [paginatedStation.imageUrl!]
                        : [],
                    evses: [],
                    uid: paginatedStation.uid!,
                    types: paginatedStation.types,
                  ))
              .toList() ??
          [];

      state = state.copyWith(
        stations: stations,
        currentPage: response.currentPage,
        totalPages: response.totalPages,
        isLoading: false,
        errorMessage: null,
      );
    } catch (e) {
      state = state
          .copyWith(isLoading: false, errorMessage: e.toString(), stations: []);
    }
  }

  Future<void> fetchMoreStations() async {
    if (state.isLoading ||
        state.currentPage >= state.totalPages ||
        state.mode == StationListMode.search) return;

    state = state.copyWith(isLoading: true);
    try {
      final response = await _stationService.fetchStationsPaginated(
          page: state.currentPage + 1, limit: 10);

      // Convert PaginatedStation to Station objects
      final newStations = response.data
              ?.where((paginatedStation) {
                return paginatedStation.name != null &&
                    paginatedStation.name!.isNotEmpty &&
                    paginatedStation.address != null &&
                    paginatedStation.address!.isNotEmpty &&
                    paginatedStation.latitude != null &&
                    paginatedStation.longitude != null &&
                    paginatedStation.uid != null &&
                    paginatedStation.uid!.isNotEmpty;
              })
              .map((paginatedStation) => Station(
                    id: paginatedStation.stationId?.toString() ?? '',
                    name: paginatedStation.name!,
                    address: paginatedStation.address!,
                    city: paginatedStation.city,
                    state: null,
                    latitude: paginatedStation.latitude!,
                    longitude: paginatedStation.longitude!,
                    distance: paginatedStation.distance ?? 0.0,
                    status: paginatedStation.status ?? 'Unknown',
                    rating: paginatedStation.rating ?? 0.0,
                    reviews: paginatedStation.reviewCount ?? 0,
                    connectors: [],
                    images: paginatedStation.imageUrl != null
                        ? [paginatedStation.imageUrl!]
                        : [],
                    evses: [],
                    uid: paginatedStation.uid!,
                    types: paginatedStation.types,
                  ))
              .toList() ??
          [];

      state = state.copyWith(
        stations: [...state.stations, ...newStations],
        currentPage: response.currentPage,
        isLoading: false,
        errorMessage: null,
      );
    } catch (e) {
      state = state.copyWith(isLoading: false, errorMessage: e.toString());
    }
  }

  Future<void> searchStations(String query) async {
    if (query.isEmpty) {
      fetchInitialStations();
      return;
    }
    state = state.copyWith(
        isLoading: true,
        mode: StationListMode.search,
        stations: [],
        currentPage: 1,
        currentSearchQuery: query);
    try {
      final results = await _stationService.searchStations(query);
      if (results.success && results.data != null) {
        // Convert StationDetail to Station objects
        final stations = results.data!.map((stationDetail) {
          // Convert StationDetail to Station - this is a simplified conversion
          // You may need to adjust this based on your actual data structure
          return Station(
            id: stationDetail.uid,
            name: stationDetail.name,
            address: stationDetail.address,
            latitude: stationDetail.coordinates.latitude,
            longitude: stationDetail.coordinates.longitude,
            distance: 0.0, // Distance not available in search results
            status: stationDetail.status,
            rating: stationDetail.rating ?? 0.0,
            reviews: stationDetail.totalReviews ?? 0,
            images: stationDetail.images ?? [],
            evses: [],
            connectors: [], // Connectors would need to be converted from stationDetail.connectors
            uid: stationDetail.uid,
          );
        }).toList();

        state = state.copyWith(
          stations: stations,
          currentPage: 1,
          totalPages: 1,
          isLoading: false,
          errorMessage: null,
        );
      } else {
        state = state.copyWith(
          stations: [],
          isLoading: false,
          errorMessage: results.message,
        );
      }
    } catch (e) {
      state = state
          .copyWith(isLoading: false, errorMessage: e.toString(), stations: []);
    }
  }
}

/// Main provider for station state management
final stationsProvider =
    StateNotifierProvider<StationsNotifier, StationState>((ref) {
  return StationsNotifier(ref);
});

/// Provider for station service
final stationServiceProvider = Provider<StationService>((ref) {
  final apiService = ref.watch(apiServiceProvider);
  final connectivityService = ref.watch(connectivityServiceProvider);
  final stationRepository = ref.watch(stationRepositoryProvider);
  return StationService(connectivityService, stationRepository, apiService);
});

/// Provider for connectivity service
final connectivityServiceProvider = Provider<ConnectivityService>((ref) {
  return ConnectivityService();
});

/// Provider for station repository
final stationRepositoryProvider = Provider<StationRepository>((ref) {
  return StationRepository();
});

/// Provider for ApiService
final apiServiceProvider = Provider<ApiService>((ref) {
  return ApiService();
});

/// Provider for fetching specific station details
final stationDetailProvider =
    FutureProvider.family<Station, String>((ref, stationUid) async {
  final stationService = ref.watch(stationServiceProvider);
  return stationService.getStationDetailsAsStation(stationUid);
});
