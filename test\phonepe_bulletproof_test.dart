import 'package:flutter_test/flutter_test.dart';
import 'package:ecoplug/services/payment/phonepe_service.dart';

void main() {
  group('PhonePe Bulletproof Response Handling Tests', () {
    
    test('SUCCESS status should create proper success result', () {
      final successResult = PaymentResult.success({
        'transactionId': 'TXN123',
        'amount': 100.0,
        'status': 'SUCCESS'
      });
      
      expect(successResult.type, PaymentResultType.success);
      expect(successResult.message, 'Payment completed successfully');
      expect(successResult.data?['transactionId'], 'TXN123');
      expect(successResult.data?['amount'], 100.0);
      expect(successResult.isUserCancelled, false);
    });

    test('FAILURE status should create proper failure result', () {
      final failureResult = PaymentResult.failed(
        'Insufficient funds',
        errorCode: 'INSUFFICIENT_BALANCE',
        data: {'balance': 50.0}
      );
      
      expect(failureResult.type, PaymentResultType.failed);
      expect(failureResult.message, 'Insufficient funds');
      expect(failureResult.errorCode, 'INSUFFICIENT_BALANCE');
      expect(failureResult.data?['balance'], 50.0);
      expect(failureResult.isUserCancelled, false);
    });

    test('INTERRUPTED status should create proper interrupted result', () {
      final interruptedResult = PaymentResult.interrupted();
      
      expect(interruptedResult.type, PaymentResultType.interrupted);
      expect(interruptedResult.message, 'Payment interrupted by user');
      expect(interruptedResult.isUserCancelled, true);
    });

    test('TIMEOUT should create proper timeout result', () {
      final timeoutResult = PaymentResult.timeout();
      
      expect(timeoutResult.type, PaymentResultType.timeout);
      expect(timeoutResult.message, 'Payment request timed out. Please try again.');
      expect(timeoutResult.isUserCancelled, false);
    });

    test('NETWORK_ERROR should create proper network error result', () {
      final networkErrorResult = PaymentResult.networkError();
      
      expect(networkErrorResult.type, PaymentResultType.networkError);
      expect(networkErrorResult.message, 'Network error occurred. Please check your connection and try again.');
      expect(networkErrorResult.isUserCancelled, false);
    });

    test('APP_CRASH should create proper app crash result', () {
      final appCrashResult = PaymentResult.appCrash();
      
      expect(appCrashResult.type, PaymentResultType.appCrash);
      expect(appCrashResult.message, 'PhonePe app encountered an error. Please try again.');
      expect(appCrashResult.isUserCancelled, false);
    });

    test('INVALID_RESPONSE should create proper invalid response result', () {
      final invalidResponseResult = PaymentResult.invalidResponse('Malformed JSON response');
      
      expect(invalidResponseResult.type, PaymentResultType.invalidResponse);
      expect(invalidResponseResult.message, 'Invalid response from payment gateway');
      expect(invalidResponseResult.data?['details'], 'Malformed JSON response');
      expect(invalidResponseResult.isUserCancelled, false);
    });

    test('UNKNOWN should create proper unknown result', () {
      final unknownResult = PaymentResult.unknown('Unexpected error occurred');
      
      expect(unknownResult.type, PaymentResultType.unknown);
      expect(unknownResult.message, 'Unknown error occurred during payment');
      expect(unknownResult.data?['details'], 'Unexpected error occurred');
      expect(unknownResult.isUserCancelled, false);
    });

    test('PENDING should create proper pending result', () {
      final pendingResult = PaymentResult.pending();
      
      expect(pendingResult.type, PaymentResultType.pending);
      expect(pendingResult.message, 'Payment is being processed. Please wait...');
      expect(pendingResult.isUserCancelled, false);
    });

    test('PaymentResult should handle minimal data gracefully', () {
      final resultWithMinimalData = PaymentResult.success({'status': 'SUCCESS'});

      expect(resultWithMinimalData.type, PaymentResultType.success);
      expect(resultWithMinimalData.message, 'Payment completed successfully');
      expect(resultWithMinimalData.data, isNotNull);
      expect(resultWithMinimalData.data?['status'], 'SUCCESS');
    });

    test('PaymentResult should handle empty data gracefully', () {
      final resultWithEmptyData = PaymentResult.success({});
      
      expect(resultWithEmptyData.type, PaymentResultType.success);
      expect(resultWithEmptyData.message, 'Payment completed successfully');
      expect(resultWithEmptyData.data, isNotNull);
      expect(resultWithEmptyData.data!.isEmpty, true);
    });

    test('PaymentResult should handle complex nested data', () {
      final complexData = {
        'transaction': {
          'id': 'TXN123',
          'amount': 100.0,
          'currency': 'INR',
          'metadata': {
            'source': 'app',
            'timestamp': DateTime.now().toIso8601String(),
          }
        },
        'user': {
          'id': 'USER456',
          'balance': 500.0,
        }
      };
      
      final resultWithComplexData = PaymentResult.success(complexData);
      
      expect(resultWithComplexData.type, PaymentResultType.success);
      expect(resultWithComplexData.data?['transaction']['id'], 'TXN123');
      expect(resultWithComplexData.data?['transaction']['amount'], 100.0);
      expect(resultWithComplexData.data?['user']['balance'], 500.0);
    });

    test('PaymentResult should handle error codes correctly', () {
      final errorCodes = [
        'INSUFFICIENT_FUNDS',
        'CARD_DECLINED',
        'NETWORK_ERROR',
        'TIMEOUT',
        'INVALID_CARD',
        'EXPIRED_CARD',
        'BLOCKED_CARD',
        'LIMIT_EXCEEDED',
      ];
      
      for (final errorCode in errorCodes) {
        final result = PaymentResult.failed(
          'Payment failed with code: $errorCode',
          errorCode: errorCode,
        );
        
        expect(result.type, PaymentResultType.failed);
        expect(result.errorCode, errorCode);
        expect(result.message, contains(errorCode));
      }
    });

    test('PaymentResult should handle very large data sets', () {
      final largeData = <String, dynamic>{};
      
      // Create large dataset
      for (int i = 0; i < 1000; i++) {
        largeData['field_$i'] = 'value_$i';
      }
      
      final resultWithLargeData = PaymentResult.success(largeData);
      
      expect(resultWithLargeData.type, PaymentResultType.success);
      expect(resultWithLargeData.data?.length, 1000);
      expect(resultWithLargeData.data?['field_0'], 'value_0');
      expect(resultWithLargeData.data?['field_999'], 'value_999');
    });

    test('PaymentResult should handle special characters in data', () {
      final specialData = {
        'message': 'Payment failed: ₹100.00 - Special chars: @#\$%^&*()[]{}|\\:";\'<>?,./`~',
        'unicode': '🎉💳💰🔒✅❌⚠️🚫',
        'newlines': 'Line 1\nLine 2\rLine 3\r\nLine 4',
        'tabs': 'Column1\tColumn2\tColumn3',
      };
      
      final resultWithSpecialData = PaymentResult.failed('Special character test', data: specialData);
      
      expect(resultWithSpecialData.type, PaymentResultType.failed);
      expect(resultWithSpecialData.data?['message'], contains('₹100.00'));
      expect(resultWithSpecialData.data?['unicode'], contains('🎉💳💰'));
      expect(resultWithSpecialData.data?['newlines'], contains('\n'));
      expect(resultWithSpecialData.data?['tabs'], contains('\t'));
    });

    test('PaymentResult should handle edge case amounts', () {
      final edgeCaseAmounts = [0.0, 0.01, 999999.99, double.maxFinite];
      
      for (final amount in edgeCaseAmounts) {
        final result = PaymentResult.success({'amount': amount});
        
        expect(result.type, PaymentResultType.success);
        expect(result.data?['amount'], amount);
      }
    });

    test('PaymentResult should handle boolean and numeric data types', () {
      final mixedData = {
        'success': true,
        'failed': false,
        'count': 42,
        'percentage': 85.5,
        'nullValue': null,
        'emptyString': '',
        'zeroValue': 0,
      };
      
      final result = PaymentResult.success(mixedData);
      
      expect(result.type, PaymentResultType.success);
      expect(result.data?['success'], true);
      expect(result.data?['failed'], false);
      expect(result.data?['count'], 42);
      expect(result.data?['percentage'], 85.5);
      expect(result.data?['nullValue'], null);
      expect(result.data?['emptyString'], '');
      expect(result.data?['zeroValue'], 0);
    });
  });
}
