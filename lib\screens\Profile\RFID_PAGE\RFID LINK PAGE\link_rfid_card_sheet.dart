import 'package:flutter/material.dart';
import '../../../../core/api/api_service.dart';
import '../../../../core/api/api_exception.dart';

class LinkRfidCardSheet extends StatefulWidget {
  const LinkRfidCardSheet({super.key});

  @override
  State<LinkRfidCardSheet> createState() => _LinkRfidCardSheetState();
}

class _LinkRfidCardSheetState extends State<LinkRfidCardSheet> {
  final TextEditingController _controller = TextEditingController();
  bool _isValid = false; // Tracks whether the entered RFID is valid.
  bool _isLoading = false;
  final FocusNode _focusNode = FocusNode();

  // API service instance
  final ApiService _apiService = ApiService();

  @override
  void initState() {
    super.initState();
    // Add listener to adjust view when keyboard appears
    _focusNode.addListener(() {
      if (_focusNode.hasFocus) {
        // Small delay to ensure the keyboard is fully shown
        Future.delayed(const Duration(milliseconds: 300), () {
          if (mounted) {
            // Ensure the widget is still in the tree
            Scrollable.ensureVisible(
              context,
              alignment: 0.5,
              duration: const Duration(milliseconds: 300),
            );
          }
        });
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _validateRfid(String value) {
    // Updated validation to match React code: alphanumeric characters only
    final isValidRfid = RegExp(r'^[0-9A-Za-z]+$').hasMatch(value) && value.isNotEmpty;

    setState(() {
      _isValid = isValidRfid;
    });

    // Auto-dismiss keyboard when valid RFID is entered and has reasonable length
    if (isValidRfid && value.length >= 8 && _focusNode.hasFocus) {
      FocusScope.of(context).unfocus();
    }
  }

  /// Convert RFID number from hexadecimal to decimal for display
  /// This matches the React code behavior: parseInt(rfid, 16)
  String _convertRfidToDecimal(String rfidHex) {
    try {
      // Convert hexadecimal RFID to decimal, matching React code behavior
      final decimalValue = int.parse(rfidHex, radix: 16);
      debugPrint('🔄 RFID Link Conversion: $rfidHex (hex) → $decimalValue (decimal)');
      return decimalValue.toString();
    } catch (e) {
      // If conversion fails, return the original value
      debugPrint('❌ Error converting RFID $rfidHex from hex to decimal: $e');
      return rfidHex;
    }
  }

  Future<void> _onLinkCard() async {
    // If user attempts to link without a valid RFID number, show error immediately.
    if (!_isValid) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter a valid RFID number'),
        ),
      );
      return;
    }

    // Otherwise, proceed with the linking logic using real API.
    setState(() => _isLoading = true);

    try {
      final response = await _apiService.addRfidCard(_controller.text.trim());

      setState(() => _isLoading = false);

      if (response['success'] == true && mounted) {
        Navigator.of(context).pop(true); // Return true to indicate success

        // Convert RFID to decimal for display (matching React behavior)
        final rfidDecimal = _convertRfidToDecimal(_controller.text.trim());

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(response['message'] ?? 'RFID $rfidDecimal Linked Successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        // Show error message from API
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(response['message'] ?? 'Failed to link RFID card'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      setState(() => _isLoading = false);

      String errorMessage = 'Failed to link RFID card';
      if (e is ApiException) {
        errorMessage = e.message;
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Changed from DraggableScrollableSheet to a fixed Container
    return Container(
      height: MediaQuery.of(context).size.height *
          0.9, // Set to 90% of screen height
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
      decoration: const BoxDecoration(
        color: Color(0xFF1A1A1A), // Slightly lighter dark gray
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(color: Colors.black26, blurRadius: 10, spreadRadius: 0),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Drag handle
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[600],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 16),

          // Title + close button row.
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Link RFID Card',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: const Icon(Icons.close, color: Colors.white),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // RFID input field
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Field label.
              const Text(
                'RFID Number',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),

              // Enhanced text field with animation - Green theme only with clean borders
              AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                decoration: BoxDecoration(
                  color: const Color(0xFF2A2A2A),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: _isValid
                        ? const Color(0xFF67C44C) // App's primary green
                        : (_focusNode.hasFocus
                            ? const Color(0xFF67C44C) // Green when focused
                            : const Color(0xFF3A3A3A)), // Gray when not focused
                    width: _focusNode.hasFocus || _isValid ? 2.0 : 1.0, // Clean integer widths
                  ),
                  boxShadow: _focusNode.hasFocus || _isValid
                      ? [
                          BoxShadow(
                            color: const Color(0xFF67C44C).withAlpha(60), // Slightly reduced opacity for cleaner look
                            blurRadius: 6,
                            spreadRadius: 0, // Remove spread for cleaner edges
                            offset: const Offset(0, 0), // Center the shadow
                          ),
                        ]
                      : [],
                ),
                child: TextField(
                  controller: _controller,
                  focusNode: _focusNode,
                  keyboardType: TextInputType.text,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    letterSpacing: 1.2,
                  ),
                  decoration: InputDecoration(
                    hintText: 'Enter your RFID number',
                    hintStyle: const TextStyle(color: Colors.white38),
                    counterText: '', // Hide the character counter.
                    filled: false,
                    prefixIcon: const Icon(
                      Icons.credit_card,
                      color: Colors.white54,
                    ),
                    suffixIcon: _controller.text.isNotEmpty
                        ? IconButton(
                            icon: Icon(
                              _isValid ? Icons.check_circle : Icons.cancel,
                              color: _isValid
                                  ? const Color(0xFF67C44C) // App's primary green
                                  : Colors.red
                                      .withAlpha(179), // 0.7 * 255 = ~179
                            ),
                            onPressed: () {
                              _controller.clear();
                              setState(() => _isValid = false);
                            },
                          )
                        : null,
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(
                      vertical: 16,
                    ),
                  ),
                  onChanged: _validateRfid,
                ),
              ),

              // Status message with animation
              AnimatedCrossFade(
                firstChild: _controller.text.isNotEmpty && !_isValid
                    ? Padding(
                        padding: const EdgeInsets.only(
                          top: 8.0,
                          left: 4.0,
                        ),
                        child: Row(
                          children: const [
                            Icon(
                              Icons.error_outline,
                              color: Colors.red,
                              size: 16,
                            ),
                            SizedBox(width: 8),
                            Text(
                              'Please enter a valid RFID number',
                              style: TextStyle(
                                color: Colors.red,
                                fontSize: 13,
                              ),
                            ),
                          ],
                        ),
                      )
                    : const SizedBox.shrink(),
                secondChild: _controller.text.isNotEmpty && _isValid
                    ? Padding(
                        padding: const EdgeInsets.only(
                          top: 8.0,
                          left: 4.0,
                        ),
                        child: Row(
                          children: const [
                            Icon(
                              Icons.check_circle,
                              color: Color(0xFF67C44C), // App's primary green
                              size: 16,
                            ),
                            SizedBox(width: 8),
                            Text(
                              'Valid RFID Number',
                              style: TextStyle(
                                color: Color(0xFF67C44C), // App's primary green
                                fontSize: 13,
                              ),
                            ),
                          ],
                        ),
                      )
                    : const SizedBox.shrink(),
                crossFadeState: _isValid
                    ? CrossFadeState.showSecond
                    : CrossFadeState.showFirst,
                duration: const Duration(milliseconds: 200),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Main content area - using Expanded to push button to bottom
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Information about RFID cards with improved design
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: const Color(0xFF2A2A2A),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: const Color(0xFF3A3A3A),
                        width: 1,
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: const [
                            Icon(
                              Icons.info_outline,
                              color: Colors.blue,
                              size: 20,
                            ),
                            SizedBox(width: 8),
                            Text(
                              'About RFID Cards',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        const Text(
                          'Linking your RFID card allows you to start charging sessions without using the app. Simply tap your card on compatible charging stations.',
                          style: TextStyle(
                            color: Colors.white70,
                            height: 1.5,
                          ),
                        ),
                        const SizedBox(height: 16),
                        // Added benefits list
                        ...[
                          'Quick authentication',
                          'Contactless payment',
                          'Secure transactions',
                        ].map(
                          (benefit) => Padding(
                            padding: const EdgeInsets.only(
                              bottom: 8.0,
                            ),
                            child: Row(
                              children: [
                                const Icon(
                                  Icons.check_circle,
                                  color: Color(0xFF67C44C), // App's primary green
                                  size: 16,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  benefit,
                                  style: const TextStyle(
                                    color: Colors.white70,
                                    fontSize: 14,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Added FAQ section
                  const Text(
                    'Frequently Asked Questions',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  _buildFaqItem(
                    'Where can I find my RFID number?',
                    'Your RFID number is printed on the back of your card, usually an alphanumeric code.',
                  ),
                  _buildFaqItem(
                    'Can I link multiple cards?',
                    'Yes, you can link multiple RFID cards to your account from the cards management section.',
                  ),
                  _buildFaqItem(
                    'What if I lose my card?',
                    'You can deactivate your card immediately from the app to prevent unauthorized use.',
                  ),
                  // Add padding at the bottom to ensure content doesn't get cut off
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),

          // Link Card Button - now outside the scrollable area and at the bottom
          SizedBox(
            width: double.infinity,
            height: 54,
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                boxShadow: _isValid && !_isLoading
                    ? [
                        BoxShadow(
                          color: const Color(0xFF67C44C)
                              .withAlpha(102), // App's primary green with 0.4 opacity
                          blurRadius: 8,
                          spreadRadius: 0,
                          offset: const Offset(0, 2),
                        ),
                      ]
                    : [],
              ),
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: _isValid
                      ? const Color(0xFF67C44C) // App's primary green
                      : Colors.grey.shade700,
                  foregroundColor: Colors.black,
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                onPressed: _isLoading || !_isValid ? null : _onLinkCard,
                child: _isLoading
                    ? const SizedBox(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(
                          color: Colors.black,
                          strokeWidth: 3,
                        ),
                      )
                    : Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.link,
                            color: _isValid ? Colors.black : Colors.white54,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Link Card',
                            style: TextStyle(
                              color: _isValid ? Colors.black : Colors.white54,
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to build FAQ items with clean expansion (no divider lines)
  Widget _buildFaqItem(String question, String answer) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: const Color(0xFF2A2A2A),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF3A3A3A), width: 1),
      ),
      child: Theme(
        data: Theme.of(context).copyWith(
          // Remove divider lines completely
          dividerColor: Colors.transparent,
          // Ensure expansion tile has no unwanted borders
          expansionTileTheme: const ExpansionTileThemeData(
            backgroundColor: Colors.transparent,
            collapsedBackgroundColor: Colors.transparent,
            shape: RoundedRectangleBorder(),
            collapsedShape: RoundedRectangleBorder(),
          ),
        ),
        child: ExpansionTile(
          title: Text(
            question,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          iconColor: Colors.white,
          collapsedIconColor: Colors.white70,
          backgroundColor: Colors.transparent, // Ensure no background color changes
          collapsedBackgroundColor: Colors.transparent,
          childrenPadding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
          // Remove any default borders/dividers
          shape: const RoundedRectangleBorder(),
          collapsedShape: const RoundedRectangleBorder(),
          children: [
            Text(
              answer,
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 14,
                height: 1.5,
              ),
            ),
          ],
        ),
      ),
    );
  }
}