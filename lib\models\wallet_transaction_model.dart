
import 'package:intl/intl.dart';
import '../utils/timezone_utils.dart';

class WalletModel {
  final double balance;
  final int userId;
  final List<TransactionModel> transactions;

  WalletModel({
    required this.balance,
    required this.userId,
    required this.transactions,
  });

  factory WalletModel.fromJson(Map<String, dynamic> json) {
    var list = json['payment_history'] as List;
    List<TransactionModel> transactionList =
        list.map((item) => TransactionModel.fromJson(item)).toList();

    return WalletModel(
      balance: double.parse(json['balance'].toString()),
      userId: json['user_id'],
      transactions: transactionList,
    );
  }
}

class TransactionModel {
  final int id;
  final double amount;
  final String status;
  final String type;
  final String remark;
  final DateTime createdAt;
  final String source;

  TransactionModel({
    required this.id,
    required this.amount,
    required this.status,
    required this.type,
    required this.remark,
    required this.createdAt,
    required this.source,
  });

  factory TransactionModel.fromJson(Map<String, dynamic> json) {
    return TransactionModel(
      id: json['id'],
      amount: double.parse(json['amount'].toString()),
      status: json['status'],
      type: json['type'],
      remark: json['remark'],
      createdAt: TimezoneUtils.parseUtcToLocal(json['created_at']),
      source: json['source'],
    );
  }

  // Helper methods for UI representation
  bool get isCredit => type == 'cr';
  bool get isDebit => type == 'dr';
  bool get isCompleted => status == 'COMPLETED';
  bool get isRejected => status == 'REJECTED';

  String get formattedAmount => NumberFormat('#,##0.00').format(amount.abs());
  String get formattedDate => DateFormat('MMM d, yyyy').format(createdAt);
  String get formattedTime => DateFormat('h:mm a').format(createdAt);
  String get formattedDateTime =>
      DateFormat('MMM d, yyyy • h:mm a').format(createdAt);
  String get formattedRemark => remark.isEmpty ? 'No description' : remark;

  // Get user-friendly date display
  String get userFriendlyDate {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = DateTime(now.year, now.month, now.day - 1);
    final dateToCheck =
        DateTime(createdAt.year, createdAt.month, createdAt.day);

    if (dateToCheck == today) {
      return 'Today, ${DateFormat('h:mm a').format(createdAt)}';
    } else if (dateToCheck == yesterday) {
      return 'Yesterday, ${DateFormat('h:mm a').format(createdAt)}';
    } else {
      return DateFormat('MMM d, yyyy').format(createdAt);
    }
  }

  // Get display amount with proper sign
  String get displayAmount {
    if (isCredit) {
      return '+₹$formattedAmount';
    } else {
      return '-₹$formattedAmount';
    }
  }

  // Get status display text
  String get statusDisplayText {
    switch (status) {
      case 'COMPLETED':
        return 'Completed';
      case 'REJECTED':
        return 'Rejected';
      default:
        return status;
    }
  }
}
