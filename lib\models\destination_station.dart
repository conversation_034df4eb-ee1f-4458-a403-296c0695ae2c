import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

/// Model for charging stations along a route from destination locations API
class DestinationStation {
  final String id;
  final String uid;
  final String name;
  final String address;
  final String? city;
  final String? state;
  final String? postalCode;
  final double latitude;
  final double longitude;
  final String status;
  final String? companyId;
  final String? connectorType;
  final String? mapPinUrl;
  final String? focusedMapPinUrl;
  final int? freeGuns;
  final double? distance;
  final Map<String, dynamic>? marker;
  final List<dynamic>? evses;

  const DestinationStation({
    required this.id,
    required this.uid,
    required this.name,
    required this.address,
    this.city,
    this.state,
    this.postalCode,
    required this.latitude,
    required this.longitude,
    required this.status,
    this.companyId,
    this.connectorType,
    this.mapPinUrl,
    this.focusedMapPinUrl,
    this.freeGuns,
    this.distance,
    this.marker,
    this.evses,
  });

  /// Create DestinationStation from API response
  factory DestinationStation.fromJson(Map<String, dynamic> json) {
    // Debug logging for station parsing
    if (kDebugMode && json.isNotEmpty) {
      debugPrint('🏪 STATION PARSE: Keys available: ${json.keys.toList()}');
      debugPrint('🏪 STATION PARSE: ID type: ${json['id'].runtimeType}, value: ${json['id']}');
      debugPrint('🏪 STATION PARSE: Status type: ${json['status'].runtimeType}, value: ${json['status']}');
      debugPrint('🏪 STATION PARSE: Coordinates: ${json['coordinates']}');
    }

    // Extract coordinates from nested structure
    double latitude = 0.0;
    double longitude = 0.0;

    if (json['coordinates'] != null) {
      final coords = json['coordinates'];
      if (kDebugMode) {
        debugPrint('🏪 COORDINATES: Raw coordinates type: ${coords.runtimeType}');
        debugPrint('🏪 COORDINATES: Raw coordinates value: $coords');
      }

      if (coords is Map<String, dynamic>) {
        // Handle nested coordinates object
        latitude = double.tryParse(coords['latitude']?.toString() ?? '0.0') ?? 0.0;
        longitude = double.tryParse(coords['longitude']?.toString() ?? '0.0') ?? 0.0;
        if (kDebugMode) {
          debugPrint('🏪 COORDINATES: Extracted from nested Map - lat: $latitude, lng: $longitude');
        }
      } else if (coords is String) {
        // Handle coordinates as JSON string
        try {
          final coordsMap = jsonDecode(coords) as Map<String, dynamic>;
          latitude = double.tryParse(coordsMap['latitude']?.toString() ?? '0.0') ?? 0.0;
          longitude = double.tryParse(coordsMap['longitude']?.toString() ?? '0.0') ?? 0.0;
          if (kDebugMode) {
            debugPrint('🏪 COORDINATES: Extracted from JSON string - lat: $latitude, lng: $longitude');
          }
        } catch (e) {
          if (kDebugMode) {
            debugPrint('🏪 COORDINATES: Failed to parse JSON string: $e');
          }
          latitude = 0.0;
          longitude = 0.0;
        }
      } else {
        if (kDebugMode) {
          debugPrint('🏪 COORDINATES: Unknown coordinates format: ${coords.runtimeType}');
        }
        latitude = 0.0;
        longitude = 0.0;
      }
    } else {
      // Fallback to direct latitude/longitude fields
      latitude = double.tryParse(json['latitude']?.toString() ?? '0.0') ?? 0.0;
      longitude = double.tryParse(json['longitude']?.toString() ?? '0.0') ?? 0.0;
      if (kDebugMode) {
        debugPrint('🏪 COORDINATES: Extracted from direct fields - lat: $latitude, lng: $longitude');
      }
    }

    // Handle status - can be int (1) or string
    String status = 'offline';
    if (json['status'] != null) {
      if (json['status'] is int) {
        status = json['status'] == 1 ? 'available' : 'offline';
      } else {
        status = json['status'].toString();
      }
    }

    // Extract marker data if available
    String? mapPinUrl;
    String? focusedMapPinUrl;
    if (json['marker'] != null && json['marker'] is Map<String, dynamic>) {
      final marker = json['marker'] as Map<String, dynamic>;
      mapPinUrl = marker['map_pin_url'];
      focusedMapPinUrl = marker['focused_map_pin_url'];
    } else {
      mapPinUrl = json['map_pin_url'];
      focusedMapPinUrl = json['focused_map_pin_url'];
    }

    final station = DestinationStation(
      id: json['id']?.toString() ?? json['station_id']?.toString() ?? '',
      uid: json['uid']?.toString() ?? '',
      name: json['name'] ?? json['station_name'] ?? '',
      address: json['address'] ?? '',
      city: json['city'],
      state: json['state'],
      postalCode: json['postal_code'],
      latitude: latitude,
      longitude: longitude,
      status: status,
      companyId: json['company_id']?.toString(),
      connectorType: json['connector_type'],
      mapPinUrl: mapPinUrl,
      focusedMapPinUrl: focusedMapPinUrl,
      freeGuns: json['free_guns'] is num ? (json['free_guns'] as num).round() : null,
      distance: (json['distance'] as num?)?.toDouble(),
      marker: json['marker'] as Map<String, dynamic>?,
      evses: json['evses'] as List<dynamic>?,
    );

    if (kDebugMode) {
      debugPrint('🏪 STATION PARSED: ${station.name} at (${station.latitude}, ${station.longitude})');
      debugPrint('🏪 STATION DETAILS: UID=${station.uid}, City=${station.city}, State=${station.state}');
      debugPrint('🏪 STATION EVSES: ${station.evses?.length ?? 0} EVSE units available');
      debugPrint('🏪 STATION CONNECTORS: ${station.getConnectorTypes().join(', ')}');
      debugPrint('🏪 STATION MAX POWER: ${station.getMaxPower() ?? 'N/A'} kW');
      debugPrint('🏪 STATION AVAILABLE CONNECTORS: ${station.getAvailableConnectorCount()}');
    }
    return station;
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'uid': uid,
      'name': name,
      'address': address,
      'city': city,
      'state': state,
      'postal_code': postalCode,
      'coordinates': jsonEncode({
        'latitude': latitude.toString(),
        'longitude': longitude.toString(),
      }),
      'latitude': latitude,
      'longitude': longitude,
      'status': status,
      'company_id': companyId,
      'connector_type': connectorType,
      'map_pin_url': mapPinUrl,
      'focused_map_pin_url': focusedMapPinUrl,
      'free_guns': freeGuns,
      'distance': distance,
      'marker': marker,
      'evses': evses,
    };
  }

  /// Get coordinates as LatLng
  LatLng get coordinates => LatLng(latitude, longitude);

  /// Convert to Station format for compatibility with existing widgets
  /// This includes all data needed for navigation to station detail page
  Map<String, dynamic> toStationMap() {
    return {
      'id': id,
      'uid': uid, // Essential for navigation to station detail page
      'name': name,
      'address': address,
      'city': city,
      'state': state,
      'postal_code': postalCode,
      'latitude': latitude,
      'longitude': longitude,
      'status': status,
      'company_id': companyId,
      'connectorType': connectorType,
      'mapPinUrl': mapPinUrl,
      'focusedMapPinUrl': focusedMapPinUrl,
      'freeGuns': freeGuns,
      'distance': distance ?? 0.0,
      'marker': marker,
      'evses': evses, // Complete EVSE data for station details
    };
  }

  /// Get available connector types from EVSE data
  List<String> getConnectorTypes() {
    final List<String> connectorTypes = [];

    if (evses != null) {
      for (final evse in evses!) {
        if (evse is Map<String, dynamic> && evse['connectors'] != null) {
          final connectors = evse['connectors'] as List<dynamic>;
          for (final connector in connectors) {
            if (connector is Map<String, dynamic> && connector['standard'] != null) {
              final standard = connector['standard'].toString();
              if (!connectorTypes.contains(standard)) {
                connectorTypes.add(standard);
              }
            }
          }
        }
      }
    }

    return connectorTypes;
  }

  /// Get maximum power available from EVSE data
  int? getMaxPower() {
    int? maxPower;

    if (evses != null) {
      for (final evse in evses!) {
        if (evse is Map<String, dynamic> && evse['connectors'] != null) {
          final connectors = evse['connectors'] as List<dynamic>;
          for (final connector in connectors) {
            if (connector is Map<String, dynamic> && connector['max_electric_power'] != null) {
              // Handle both int and double values from API
              final powerValue = connector['max_electric_power'];
              int? power;
              if (powerValue is int) {
                power = powerValue;
              } else if (powerValue is double) {
                power = powerValue.round(); // Convert double to int by rounding
              } else if (powerValue is String) {
                power = double.tryParse(powerValue)?.round();
              }

              if (power != null && (maxPower == null || power > maxPower)) {
                maxPower = power;
              }
            }
          }
        }
      }
    }

    return maxPower;
  }

  /// Get total number of available connectors
  int getAvailableConnectorCount() {
    int count = 0;

    if (evses != null) {
      for (final evse in evses!) {
        if (evse is Map<String, dynamic> &&
            evse['status'] == 'AVAILABLE' &&
            evse['connectors'] != null) {
          final connectors = evse['connectors'] as List<dynamic>;
          count += connectors.length;
        }
      }
    }

    return count;
  }

  /// Get formatted address string
  String getFormattedAddress() {
    final parts = <String>[];

    if (address.isNotEmpty) parts.add(address);
    if (city != null && city!.isNotEmpty) parts.add(city!);
    if (state != null && state!.isNotEmpty) parts.add(state!);
    if (postalCode != null && postalCode!.isNotEmpty) parts.add(postalCode!);

    return parts.join(', ');
  }

  @override
  String toString() {
    return 'DestinationStation(id: $id, uid: $uid, name: $name, status: $status, coordinates: ($latitude, $longitude))';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DestinationStation && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// Response model for destination locations API
class DestinationLocationsResponse {
  final bool success;
  final String? message;
  final List<DestinationStation> stations;

  const DestinationLocationsResponse({
    required this.success,
    this.message,
    required this.stations,
  });

  /// Create response from API JSON
  factory DestinationLocationsResponse.fromJson(dynamic json) {
    List<dynamic> stationsData = [];
    bool success = true;
    String? message;

    if (kDebugMode) {
      debugPrint('🔍 RESPONSE PARSE: Input type: ${json.runtimeType}');
    }

    if (json is Map<String, dynamic>) {
      // Standard response format with wrapper object
      stationsData = json['data'] ?? json['stations'] ?? [];
      success = json['success'] ?? true;
      message = json['message'];
      if (kDebugMode) {
        debugPrint('🔍 RESPONSE PARSE: Map format - found ${stationsData.length} stations');
      }
    } else if (json is List) {
      // Direct list response format
      stationsData = json;
      success = true;
      message = null;
      if (kDebugMode) {
        debugPrint('🔍 RESPONSE PARSE: List format - found ${stationsData.length} stations');
      }
    }

    if (kDebugMode) {
      debugPrint('🔍 RESPONSE PARSE: About to parse ${stationsData.length} station objects');
    }

    final List<DestinationStation> stations = [];

    for (int i = 0; i < stationsData.length; i++) {
      try {
        final stationJson = stationsData[i] as Map<String, dynamic>;
        final station = DestinationStation.fromJson(stationJson);
        stations.add(station);
      } catch (e) {
        if (kDebugMode) {
          debugPrint('❌ STATION PARSE ERROR: Failed to parse station $i: $e');
          debugPrint('❌ STATION DATA: ${stationsData[i]}');
        }
        // Continue with other stations instead of failing completely
      }
    }

    if (kDebugMode) {
      debugPrint('🔍 RESPONSE PARSE: Successfully parsed ${stations.length} stations');
    }

    return DestinationLocationsResponse(
      success: success,
      message: message,
      stations: stations,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'data': stations.map((station) => station.toJson()).toList(),
    };
  }

  @override
  String toString() {
    return 'DestinationLocationsResponse(success: $success, stations: ${stations.length})';
  }
}
