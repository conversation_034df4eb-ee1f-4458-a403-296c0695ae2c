/// Represents an active charging session
class ChargingSession {
  final String id;
  final String stationUid;
  final String connectorId;
  final DateTime startTime;
  final DateTime? endTime;
  final double currentCharge; // 0.0 to 1.0 (percentage)
  final double currentPower; // in kW
  final double energyDelivered; // in kWh
  final double cost; // in currency units
  final double co2Saved; // in kg
  final String status; // 'active', 'completed', 'error', etc.
  final String? errorMessage;

  ChargingSession({
    required this.id,
    required this.stationUid,
    required this.connectorId,
    required this.startTime,
    this.endTime,
    required this.currentCharge,
    required this.currentPower,
    required this.energyDelivered,
    required this.cost,
    required this.co2Saved,
    this.status = 'active',
    this.errorMessage,
  });

  /// Calculate the duration of the session so far
  Duration get duration {
    final end = endTime ?? DateTime.now();
    return end.difference(startTime);
  }

  /// Format the duration as a string (e.g., "1h 23m")
  String get formattedDuration {
    final dur = duration;
    final hours = dur.inHours;
    final minutes = dur.inMinutes % 60;
    return "${hours}h ${minutes}m";
  }

  /// Calculate the estimated time remaining (in minutes)
  int get estimatedMinutesRemaining {
    if (currentCharge >= 1.0) return 0;
    
    // Simple estimate: if it took X minutes to charge Y%,
    // it will take (X / Y) * (100 - Y)% to finish
    final minutesSoFar = duration.inMinutes;
    if (minutesSoFar <= 0 || currentCharge <= 0) return 75; // Default fallback
    
    final chargeRate = currentCharge / minutesSoFar; // charge per minute
    final remainingCharge = 1.0 - currentCharge;
    
    return (remainingCharge / chargeRate).round();
  }

  /// Format the estimated time remaining (e.g., "1h 23m")
  String get formattedEta {
    final minutes = estimatedMinutesRemaining;
    final hours = minutes ~/ 60;
    final mins = minutes % 60;
    return "${hours}h ${mins}m";
  }

  /// Create a copy of this session with some fields updated
  ChargingSession copyWith({
    String? id,
    String? stationUid,
    String? connectorId,
    DateTime? startTime,
    DateTime? endTime,
    double? currentCharge,
    double? currentPower,
    double? energyDelivered,
    double? cost,
    double? co2Saved,
    String? status,
    String? errorMessage,
  }) {
    return ChargingSession(
      id: id ?? this.id,
      stationUid: stationUid ?? this.stationUid,
      connectorId: connectorId ?? this.connectorId,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      currentCharge: currentCharge ?? this.currentCharge,
      currentPower: currentPower ?? this.currentPower,
      energyDelivered: energyDelivered ?? this.energyDelivered,
      cost: cost ?? this.cost,
      co2Saved: co2Saved ?? this.co2Saved,
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}
