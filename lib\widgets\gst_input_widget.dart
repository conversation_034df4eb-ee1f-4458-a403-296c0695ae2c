import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../utils/gst_formatter.dart';

class GSTInputWidget extends StatefulWidget {
  final String? initialGstNo;
  final String? initialBusinessName;
  final Function(String? gstNo, String? businessName) onChanged;
  final bool isExpanded;
  final VoidCallback onTap;

  const GSTInputWidget({
    super.key,
    this.initialGstNo,
    this.initialBusinessName,
    required this.onChanged,
    required this.isExpanded,
    required this.onTap,
  });

  @override
  State<GSTInputWidget> createState() => _GSTInputWidgetState();
}

class _GSTInputWidgetState extends State<GSTInputWidget>
    with TickerProviderStateMixin {
  late TextEditingController _gstController;
  late TextEditingController _businessNameController;
  late AnimationController _expandController;
  late AnimationController _fadeController;
  late Animation<double> _expandAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  bool _gstError = false;
  String? _gstErrorText;

  @override
  void initState() {
    super.initState();

    // Initialize controllers with clean GST data (remove any existing formatting)
    final cleanGstNo = widget.initialGstNo != null && widget.initialGstNo!.isNotEmpty
        ? GSTFormatter.cleanGSTForAPI(widget.initialGstNo!)
        : '';
    _gstController = TextEditingController(text: cleanGstNo);
    _businessNameController =
        TextEditingController(text: widget.initialBusinessName ?? '');

    // Animation controllers
    _expandController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // Animations
    _expandAnimation = CurvedAnimation(
      parent: _expandController,
      curve: Curves.easeInOut,
    );

    _fadeAnimation = CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeIn,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, -0.5),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _expandController,
      curve: Curves.easeOutBack,
    ));

    // Listen for changes
    _gstController.addListener(_onGstChanged);
    _businessNameController.addListener(_onBusinessNameChanged);
  }

  @override
  void didUpdateWidget(GSTInputWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Update GST data if it has changed, ensuring clean format
    if (widget.initialGstNo != oldWidget.initialGstNo) {
      final cleanGstNo = widget.initialGstNo != null && widget.initialGstNo!.isNotEmpty
          ? GSTFormatter.cleanGSTForAPI(widget.initialGstNo!)
          : '';
      _gstController.text = cleanGstNo;
    }

    // Update business name if it has changed
    if (widget.initialBusinessName != oldWidget.initialBusinessName) {
      _businessNameController.text = widget.initialBusinessName ?? '';
    }

    if (widget.isExpanded != oldWidget.isExpanded) {
      if (widget.isExpanded) {
        _expandController.forward();
        _fadeController.forward();
      } else {
        _expandController.reverse();
        _fadeController.reverse();
      }
    }
  }

  void _onGstChanged() {
    final gstNo = _gstController.text;
    _validateGST(gstNo);
    widget.onChanged(
        gstNo.isEmpty ? null : gstNo,
        _businessNameController.text.isEmpty
            ? null
            : _businessNameController.text);
  }

  void _onBusinessNameChanged() {
    widget.onChanged(
        _gstController.text.isEmpty ? null : _gstController.text,
        _businessNameController.text.isEmpty
            ? null
            : _businessNameController.text);
  }

  void _validateGST(String gstNo) {
    if (gstNo.isEmpty) {
      setState(() {
        _gstError = false;
        _gstErrorText = null;
      });
      return;
    }

    // Use the GST formatter for validation
    final errorMessage = GSTFormatter.getGSTValidationError(gstNo);

    setState(() {
      _gstError = errorMessage != null;
      _gstErrorText = errorMessage;
    });
  }

  @override
  void dispose() {
    _gstController.dispose();
    _businessNameController.dispose();
    _expandController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Get theme information for dark mode support
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final backgroundColor = isDarkMode ? Colors.grey.shade800 : Colors.grey[100]!;
    final borderColor = isDarkMode ? Colors.grey.shade700 : Colors.grey.shade300;
    final primaryTextColor = isDarkMode ? Colors.white : Colors.grey[700]!;
    final secondaryTextColor = isDarkMode ? Colors.grey.shade300 : Colors.grey[600]!;
    final iconColor = isDarkMode ? Colors.grey.shade400 : Colors.grey[600]!;
    final expandedIconColor = isDarkMode ? Colors.grey.shade500 : Colors.grey[400]!;

    return Column(
      children: [
        // Main GST input trigger
        GestureDetector(
          onTap: widget.onTap,
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            decoration: BoxDecoration(
              color: backgroundColor,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: widget.isExpanded
                    ? const Color(0xFF67C44C)
                    : borderColor,
                width: widget.isExpanded ? 2 : 1,
              ),
              boxShadow: widget.isExpanded
                  ? [
                      BoxShadow(
                        color: const Color(0xFF67C44C).withValues(alpha: isDarkMode ? 0.2 : 0.1),
                        blurRadius: 8,
                        spreadRadius: 2,
                      ),
                    ]
                  : [
                      if (isDarkMode)
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.3),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                    ],
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
              child: Row(
                children: [
                  Icon(
                    Icons.receipt_long_outlined,
                    color: widget.isExpanded
                        ? const Color(0xFF67C44C)
                        : iconColor,
                    size: 22,
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.isExpanded
                              ? 'GST Details (Tap to collapse)'
                              : (_gstController.text.isNotEmpty
                                  ? 'GST Details'
                                  : 'Add GST Details (Optional)'),
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: widget.isExpanded
                                ? const Color(0xFF67C44C)
                                : primaryTextColor,
                            letterSpacing: 0.3,
                          ),
                        ),
                        // Show GST number and business name when collapsed and data exists
                        if (!widget.isExpanded && _gstController.text.isNotEmpty) ...[
                          const SizedBox(height: 6),
                          Container(
                            padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                            decoration: BoxDecoration(
                              color: isDarkMode
                                  ? Colors.grey.shade700.withValues(alpha: 0.5)
                                  : Colors.grey.shade200.withValues(alpha: 0.7),
                              borderRadius: BorderRadius.circular(6),
                              border: Border.all(
                                color: isDarkMode
                                    ? Colors.grey.shade600
                                    : Colors.grey.shade300,
                                width: 0.5,
                              ),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'GST: ${_gstController.text}',
                                  style: TextStyle(
                                    fontSize: 13,
                                    fontWeight: FontWeight.w500,
                                    color: secondaryTextColor,
                                    fontFamily: 'monospace',
                                  ),
                                ),
                                if (_businessNameController.text.isNotEmpty) ...[
                                  const SizedBox(height: 2),
                                  Text(
                                    'Business: ${_businessNameController.text}',
                                    style: TextStyle(
                                      fontSize: 13,
                                      fontWeight: FontWeight.w500,
                                      color: secondaryTextColor,
                                    ),
                                  ),
                                ],
                              ],
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  AnimatedRotation(
                    turns: widget.isExpanded ? 0.5 : 0,
                    duration: const Duration(milliseconds: 300),
                    child: Icon(
                      Icons.keyboard_arrow_down,
                      color: widget.isExpanded
                          ? const Color(0xFF67C44C)
                          : expandedIconColor,
                      size: 20,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),

        // Expanded GST input fields
        SizeTransition(
          sizeFactor: _expandAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: Container(
                margin: const EdgeInsets.only(top: 16),
                child: Column(
                  children: [
                    // GST Number Input
                    _buildGSTTextField(
                      controller: _gstController,
                      label: 'GST Number',
                      hintText: 'Enter 15-digit GST number',
                      icon: Icons.numbers_outlined,
                      isError: _gstError,
                      errorText: _gstErrorText,
                      inputFormatters: GSTFormatter.getGSTInputFormatters(),
                      textCapitalization: TextCapitalization.characters,
                    ),

                    const SizedBox(height: 16),

                    // Business Name Input
                    _buildGSTTextField(
                      controller: _businessNameController,
                      label: 'Business Name',
                      hintText: 'Enter your business name',
                      icon: Icons.business_outlined,
                      textCapitalization: TextCapitalization.words,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildGSTTextField({
    required TextEditingController controller,
    required String label,
    required String hintText,
    required IconData icon,
    bool isError = false,
    String? errorText,
    List<TextInputFormatter>? inputFormatters,
    TextCapitalization textCapitalization = TextCapitalization.none,
  }) {
    // Get theme information for dark mode support
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final fieldBackgroundColor = isDarkMode ? Colors.grey.shade900 : Colors.white;
    final fieldBorderColor = isDarkMode ? Colors.grey.shade700 : Colors.grey.shade300;
    final textColor = isDarkMode ? Colors.white : Colors.black87;
    final labelColor = isDarkMode ? Colors.grey.shade400 : Colors.grey[600]!;
    final hintColor = isDarkMode ? Colors.grey.shade500 : Colors.grey[400]!;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      decoration: BoxDecoration(
        color: fieldBackgroundColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isError ? Colors.red : fieldBorderColor,
          width: isError ? 1.5 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: isError
                ? Colors.red.withValues(alpha: 0.1)
                : (isDarkMode
                    ? Colors.black.withValues(alpha: 0.2)
                    : Colors.grey.withValues(alpha: 0.05)),
            blurRadius: 4,
            spreadRadius: 1,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextField(
            controller: controller,
            textCapitalization: textCapitalization,
            inputFormatters: inputFormatters,
            style: TextStyle(
              color: textColor,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
            decoration: InputDecoration(
              contentPadding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
              border: InputBorder.none,
              prefixIcon: Container(
                margin: const EdgeInsets.only(left: 12, right: 8),
                child: Icon(
                  icon,
                  color: isError
                      ? Colors.red
                      : (isDarkMode
                          ? const Color(0xFF67C44C).withValues(alpha: 0.8)
                          : const Color(0xFF67C44C)),
                  size: 20,
                ),
              ),
              prefixIconConstraints: const BoxConstraints(minWidth: 48),
              labelText: label,
              hintText: hintText,
              labelStyle: TextStyle(
                color: isError ? Colors.red : labelColor,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
              hintStyle: TextStyle(
                color: hintColor,
                fontSize: 14,
              ),
              floatingLabelBehavior: FloatingLabelBehavior.never,
              focusedBorder: InputBorder.none,
              enabledBorder: InputBorder.none,
            ),
          ),
          if (isError && errorText != null)
            Container(
              margin: const EdgeInsets.only(left: 16, right: 16, bottom: 8),
              padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(6),
                border: Border.all(
                  color: Colors.red.withValues(alpha: 0.3),
                  width: 0.5,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.error_outline,
                    color: Colors.red,
                    size: 14,
                  ),
                  const SizedBox(width: 6),
                  Expanded(
                    child: Text(
                      errorText,
                      style: const TextStyle(
                        color: Colors.red,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }
}
