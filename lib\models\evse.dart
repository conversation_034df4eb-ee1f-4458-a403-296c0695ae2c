class Evse {
  final String id;
  final String name;
  final String status;
  final double power;
  final String connectorType;
  final int availableConnectors;

  Evse({
    required this.id,
    required this.name,
    required this.status,
    required this.power,
    required this.connectorType,
    required this.availableConnectors,
  });

  factory Evse.fromJson(Map<String, dynamic> json) {
    return Evse(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      status: json['status'] ?? '',
      power: (json['power'] as num?)?.toDouble() ?? 0.0,
      connectorType: json['connector_type'] ?? '',
      availableConnectors: json['available_connectors'] is num ? (json['available_connectors'] as num).round() : 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'status': status,
      'power': power,
      'connector_type': connectorType,
      'available_connectors': availableConnectors,
    };
  }
}
