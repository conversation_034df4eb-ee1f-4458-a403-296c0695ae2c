package com.eeil.ecoplug

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.IBinder
import android.util.Log
import android.widget.RemoteViews
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import android.Manifest
import android.app.Activity
import android.content.pm.PackageManager
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import io.flutter.plugin.common.MethodChannel.Result
import com.google.firebase.messaging.FirebaseMessaging
import java.util.concurrent.ConcurrentHashMap

class CustomChargingNotificationHandler() : Service(), MethodCallHandler {

    // Default constructor for Android system instantiation
    constructor(context: Context) : this() {
        this.context = context
    }

    private var context: Context? = null

    companion object {
        private const val CHANNEL_ID = "charging_session_custom"
        private const val CHANNEL_NAME = "Charging Session (Custom)"
        private const val CHANNEL_DESCRIPTION = "Custom pin bar style charging notifications"
        private const val FCM_TOPIC_PREFIX = "charging_"
    }

    // FCM topic subscription management
    private var firebaseMessaging: FirebaseMessaging? = null
    private val subscribedTopics = ConcurrentHashMap<String, String>() // transactionId -> topicName
    private var currentTransactionId: String? = null

    private var notificationManager: NotificationManagerCompat? = null

    override fun onCreate() {
        super.onCreate()
        if (context == null) {
            context = this
        }
        notificationManager = NotificationManagerCompat.from(context!!)
        createNotificationChannel()
        initializeFirebaseMessaging()
    }

    override fun onBind(intent: Intent?): IBinder? {
        return null
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        return START_STICKY
    }

    override fun onMethodCall(call: MethodCall, result: Result) {
        when (call.method) {
            "initialize" -> {
                initialize(result)
            }
            "showCustomNotification" -> {
                showCustomNotification(call, result)
            }
            "updateNotificationProgress" -> {
                updateNotificationProgress(call, result)
            }
            "hideNotification" -> {
                hideNotification(call, result)
            }
            "isSupported" -> {
                result.success(true)
            }
            "requestPermissions" -> {
                requestPermissions(result)
            }
            "startForegroundService" -> {
                startForegroundService(call, result)
            }
            "stopForegroundService" -> {
                stopForegroundService(result)
            }
            "updateForegroundService" -> {
                updateForegroundService(call, result)
            }
            "subscribeToChargingTopic" -> {
                subscribeToChargingTopic(call, result)
            }
            "unsubscribeFromChargingTopic" -> {
                unsubscribeFromChargingTopic(call, result)
            }
            else -> {
                result.notImplemented()
            }
        }
    }

    private fun initialize(result: Result) {
        try {
            Log.d("CustomNotification", "Initializing custom notification service...")
            Log.d("CustomNotification", "Android SDK version: ${Build.VERSION.SDK_INT}")

            createNotificationChannel()

            // Verify channel was created successfully
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                val channel = notificationManager?.getNotificationChannel(CHANNEL_ID)
                if (channel != null) {
                    Log.d("CustomNotification", "Notification channel created successfully: ${channel.name}")
                    Log.d("CustomNotification", "Channel importance: ${channel.importance}")
                } else {
                    Log.e("CustomNotification", "Failed to create notification channel")
                    result.error("INIT_ERROR", "Failed to create notification channel", "Channel is null")
                    return
                }
            }

            Log.d("CustomNotification", "Custom notification service initialized successfully")
            result.success(true)
        } catch (e: Exception) {
            Log.e("CustomNotification", "Error initializing: ${e.message}", e)
            result.error("INIT_ERROR", "Failed to initialize custom notifications", e.message)
        }
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            Log.d("CustomNotification", "Creating notification channel for Android 8.0+")

            // Use shared notification channel creation from NotificationHelper
            val notificationHelper = NotificationHelper(context!!)
            notificationHelper.createNotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                CHANNEL_DESCRIPTION,
                isChargingChannel = true
            )

            Log.d("CustomNotification", "Notification channel creation completed")
        } else {
            Log.d("CustomNotification", "Android version < 8.0, no notification channel needed")
        }
    }

    private fun showCustomNotification(call: MethodCall, result: Result) {
        try {
            Log.d("CustomNotification", "Showing custom notification...")

            // Check if notifications are enabled before proceeding
            if (context != null && !NotificationManagerCompat.from(context!!).areNotificationsEnabled()) {
                Log.w("CustomNotification", "Notifications are disabled by user")
                result.error("NOTIFICATIONS_DISABLED", "Notifications are disabled", "User has disabled notifications")
                return
            }

            val notificationId = call.argument<Int>("notificationId") ?: 1001
            val isCharging = call.argument<Boolean>("isCharging") ?: true
            val chargePercentage = call.argument<Double>("chargePercentage") ?: 0.0
            val currentPower = call.argument<String>("currentPower") ?: "0 kW"
            val energyDelivered = call.argument<String>("energyDelivered") ?: "0.0 kWh"
            val currentPrice = call.argument<String>("currentPrice") ?: "₹0.00"
            val co2Saved = call.argument<String>("co2Saved") ?: "0.0 kg"
            val chargingTimer = call.argument<String>("chargingTimer") ?: "00:00:00"
            val percentageText = call.argument<String>("percentageText") ?: "0%"
            val powerValue = call.argument<String>("powerValue") ?: "0"
            val energyValue = call.argument<String>("energyValue") ?: "0.0"
            val co2Value = call.argument<String>("co2Value") ?: "0.0"
            val renewableValue = call.argument<String>("renewableValue") ?: "85"
            val statusText = call.argument<String>("statusText") ?: "CHARGING"
            val chargingMode = call.argument<String>("chargingMode") ?: "normal"

            Log.d("CustomNotification", "Creating notification with data: charging=$isCharging, percentage=${(chargePercentage * 100).toInt()}%, power=$currentPower")

            val notification = createCustomNotification(
                isCharging, chargePercentage, currentPower, energyDelivered,
                currentPrice, co2Saved, chargingTimer, percentageText,
                powerValue, energyValue, co2Value, renewableValue, statusText, chargingMode
            )

            Log.d("CustomNotification", "Notification created successfully, showing with ID: $notificationId")
            notificationManager?.notify(notificationId, notification)
            Log.d("CustomNotification", "Notification shown successfully")

            result.success(true)
        } catch (e: Exception) {
            Log.e("CustomNotification", "Error showing notification: ${e.message}", e)
            result.error("SHOW_ERROR", "Failed to show custom notification", e.message)
        }
    }

    private fun createCustomNotification(
        isCharging: Boolean,
        chargePercentage: Double,
        currentPower: String,
        energyDelivered: String,
        currentPrice: String,
        co2Saved: String,
        chargingTimer: String,
        percentageText: String,
        powerValue: String,
        energyValue: String,
        co2Value: String,
        renewableValue: String,
        statusText: String,
        chargingMode: String
    ): Notification {

        // Create custom RemoteViews for expanded notification
        val expandedView = RemoteViews(context!!.packageName, R.layout.notification_charging_pin_bar)

        // Create custom RemoteViews for collapsed notification
        val collapsedView = RemoteViews(context!!.packageName, R.layout.notification_charging_pin_bar_collapsed)

        // Update expanded view with data
        updateExpandedView(expandedView, isCharging, chargePercentage, currentPower,
            energyDelivered, currentPrice, co2Saved, chargingTimer, percentageText,
            powerValue, energyValue, co2Value, renewableValue, statusText, chargingMode)

        // Update collapsed view with data
        updateCollapsedView(collapsedView, isCharging, percentageText, chargingTimer,
            currentPower, energyDelivered, currentPrice, statusText, chargingMode)

        // Create pending intent for notification tap
        val intent = Intent(context!!, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        }
        val pendingIntent = PendingIntent.getActivity(
            context!!, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        // Build the notification with enhanced visibility for notification panel
        val builder = NotificationCompat.Builder(context!!, CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_launcher) // Use official app launcher icon for better recognition

        // Set large icon using official launcher icon for consistent branding
        try {
            val largeBitmap = android.graphics.BitmapFactory.decodeResource(context!!.resources, R.drawable.ic_launcher)
            if (largeBitmap != null) {
                builder.setLargeIcon(largeBitmap)
            }
        } catch (e: Exception) {
            Log.w("CustomNotification", "Failed to load large icon for notification: ${e.message}")
        }

        builder
            .setCustomContentView(collapsedView)
            .setCustomBigContentView(expandedView)
            .setStyle(NotificationCompat.DecoratedCustomViewStyle())
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setCategory(NotificationCompat.CATEGORY_PROGRESS)
            .setOngoing(isCharging) // Persistent notification while charging
            .setAutoCancel(!isCharging) // Can be dismissed when not charging
            .setContentIntent(pendingIntent)
            .setProgress(100, (chargePercentage * 100).toInt(), false)
            .setColor(context?.getColor(R.color.charging_green) ?: 0xFF00FF00.toInt())
            .setVisibility(NotificationCompat.VISIBILITY_PUBLIC) // Show on lock screen
            .setShowWhen(true) // Show timestamp
            .setWhen(System.currentTimeMillis())
            .setContentTitle("Ecoplug Charging") // Fallback title for accessibility
            .setContentText("$statusText - $percentageText") // Fallback text
            .setSubText("Tap to view details") // Additional context

        return builder.build()
    }

    private fun updateExpandedView(
        remoteViews: RemoteViews,
        isCharging: Boolean,
        chargePercentage: Double,
        currentPower: String,
        energyDelivered: String,
        currentPrice: String,
        co2Saved: String,
        chargingTimer: String,
        percentageText: String,
        powerValue: String,
        energyValue: String,
        co2Value: String,
        renewableValue: String,
        statusText: String,
        chargingMode: String
    ) {
        // Update status and indicators - direct mode removed
        remoteViews.setTextViewText(R.id.status_text, statusText)
        remoteViews.setTextViewText(R.id.battery_percentage, percentageText)
        remoteViews.setTextViewText(R.id.charging_timer, chargingTimer)
        remoteViews.setTextViewText(R.id.current_percentage, percentageText)

        // Update metrics
        remoteViews.setTextViewText(R.id.power_value, powerValue)
        remoteViews.setTextViewText(R.id.energy_value, energyValue)
        remoteViews.setTextViewText(R.id.cost_value, currentPrice)
        remoteViews.setTextViewText(R.id.co2_value, co2Value)
        remoteViews.setTextViewText(R.id.renewable_value, renewableValue)

        // Update progress bar width (approximate)
        val progressWidth = (chargePercentage * 100).toInt()
        // Note: RemoteViews has limitations for dynamic width changes
        // This would require more complex implementation with custom drawable updates

        // Update charging indicator color based on status
        val indicatorColor = if (isCharging) R.color.charging_green else R.color.text_light
        remoteViews.setInt(R.id.charging_indicator, "setBackgroundResource",
            if (isCharging) R.drawable.charging_indicator_active else R.drawable.charging_indicator_inactive)
    }

    private fun updateCollapsedView(
        remoteViews: RemoteViews,
        isCharging: Boolean,
        percentageText: String,
        chargingTimer: String,
        currentPower: String,
        energyDelivered: String,
        currentPrice: String,
        statusText: String,
        chargingMode: String
    ) {
        // Update collapsed view elements - direct mode removed
        remoteViews.setTextViewText(R.id.status_text_collapsed, statusText)
        remoteViews.setTextViewText(R.id.battery_percentage_collapsed, percentageText)
        remoteViews.setTextViewText(R.id.charging_timer_collapsed, chargingTimer)
        remoteViews.setTextViewText(R.id.circular_percentage, percentageText.replace("%", ""))

        // Update power/energy info
        val infoText = "Power: $currentPower • Energy: $energyDelivered • Cost: $currentPrice"
        remoteViews.setTextViewText(R.id.power_energy_info, infoText)

        // Update charging indicator
        remoteViews.setInt(R.id.charging_indicator_collapsed, "setBackgroundResource",
            if (isCharging) R.drawable.charging_indicator_active else R.drawable.charging_indicator_inactive)
    }

    private fun updateNotificationProgress(call: MethodCall, result: Result) {
        try {
            // This would update an existing notification with new progress data
            // Implementation similar to showCustomNotification but for updates only
            showCustomNotification(call, result)
        } catch (e: Exception) {
            result.error("UPDATE_ERROR", "Failed to update notification progress", e.message)
        }
    }

    private fun hideNotification(call: MethodCall, result: Result) {
        try {
            val notificationId = call.argument<Int>("notificationId") ?: 1001
            notificationManager?.cancel(notificationId)
            result.success(true)
        } catch (e: Exception) {
            result.error("HIDE_ERROR", "Failed to hide notification", e.message)
        }
    }

    private fun requestPermissions(result: Result) {
        try {
            Log.d("CustomNotification", "Requesting notification permissions...")

            // For Android 13+ we need to explicitly request the POST_NOTIFICATIONS runtime permission
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                val permissionGranted = ContextCompat.checkSelfPermission(
                    context!!,
                    Manifest.permission.POST_NOTIFICATIONS
                ) == PackageManager.PERMISSION_GRANTED

                Log.d("CustomNotification", "Android 13+ permission check: $permissionGranted")

                if (!permissionGranted) {
                    Log.w("CustomNotification", "POST_NOTIFICATIONS permission not granted")
                    // Note: We can't directly request permissions from a service context
                    // The app should handle this at the activity level
                    result.success(false)
                    return
                }
            }

            // Check if notifications are enabled
            val areEnabled = NotificationManagerCompat.from(context!!).areNotificationsEnabled()
            Log.d("CustomNotification", "Notifications enabled: $areEnabled")

            // Additional check for notification channel
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                val channel = notificationManager?.getNotificationChannel(CHANNEL_ID)
                val channelEnabled = channel?.importance != NotificationManager.IMPORTANCE_NONE
                Log.d("CustomNotification", "Notification channel enabled: $channelEnabled")
                result.success(areEnabled && channelEnabled)
            } else {
                result.success(areEnabled)
            }
        } catch (e: Exception) {
            Log.e("CustomNotification", "Error checking permissions: ${e.message}", e)
            result.error("PERMISSION_ERROR", "Failed to request permissions", e.message)
        }
    }

    /**
     * Start foreground service for persistent charging notifications
     */
    private fun startForegroundService(call: MethodCall, result: Result) {
        try {
            Log.d("CustomNotification", "Starting foreground service for persistent charging")

            val stationUid = call.argument<String>("stationUid") ?: ""
            val connectorId = call.argument<String>("connectorId") ?: ""
            val transactionId = call.argument<String>("transactionId") ?: ""
            val authReference = call.argument<String>("authReference") ?: ""

            Log.d("CustomNotification", "Service params: station=$stationUid, connector=$connectorId, transaction=$transactionId")

            // Start the background service
            ChargingBackgroundService.startChargingService(
                context!!,
                stationUid,
                connectorId,
                transactionId,
                authReference
            )

            result.success(true)
            Log.d("CustomNotification", "✅ Foreground service started successfully")

        } catch (e: Exception) {
            Log.e("CustomNotification", "❌ Error starting foreground service: ${e.message}", e)
            result.error("START_SERVICE_ERROR", "Failed to start foreground service", e.message)
        }
    }

    /**
     * Stop foreground service
     */
    private fun stopForegroundService(result: Result) {
        try {
            Log.d("CustomNotification", "Stopping foreground service")

            ChargingBackgroundService.stopChargingService(context!!)

            result.success(true)
            Log.d("CustomNotification", "✅ Foreground service stopped successfully")

        } catch (e: Exception) {
            Log.e("CustomNotification", "❌ Error stopping foreground service: ${e.message}", e)
            result.error("STOP_SERVICE_ERROR", "Failed to stop foreground service", e.message)
        }
    }

    /**
     * Update foreground service with new charging data
     */
    private fun updateForegroundService(call: MethodCall, result: Result) {
        try {
            Log.d("CustomNotification", "Updating foreground service with new data")

            val chargePercentage = call.argument<Double>("chargePercentage") ?: 0.0
            val currentPower = call.argument<String>("currentPower") ?: "0.0 kW"
            val energyDelivered = call.argument<String>("energyDelivered") ?: "0.00 kWh"
            val cost = call.argument<String>("cost") ?: "₹0.00"
            val co2Saved = call.argument<String>("co2Saved") ?: "0.0 kg"
            val chargingTimer = call.argument<String>("chargingTimer") ?: "00:00:00"

            // Update the background service
            ChargingBackgroundService.updateChargingData(
                context!!,
                chargePercentage,
                currentPower,
                energyDelivered,
                cost,
                co2Saved,
                chargingTimer
            )

            result.success(true)
            Log.d("CustomNotification", "✅ Foreground service updated successfully")

        } catch (e: Exception) {
            Log.e("CustomNotification", "❌ Error updating foreground service: ${e.message}", e)
            result.error("UPDATE_SERVICE_ERROR", "Failed to update foreground service", e.message)
        }
    }

    // ==================== FCM TOPIC SUBSCRIPTION SYSTEM ====================

    /**
     * Initialize Firebase Messaging for topic subscriptions
     */
    private fun initializeFirebaseMessaging() {
        try {
            Log.d("FCMTopicSubscription", "Initializing Firebase Messaging for topic subscriptions")

            firebaseMessaging = FirebaseMessaging.getInstance()

            Log.d("FCMTopicSubscription", "✅ Firebase Messaging initialized successfully")
        } catch (e: Exception) {
            Log.e("FCMTopicSubscription", "❌ Error initializing Firebase Messaging: ${e.message}", e)
        }
    }

    /**
     * Subscribe to charging FCM topic when charging session starts
     * Format: charging_{transaction_id}
     */
    private fun subscribeToChargingTopic(call: MethodCall, result: Result) {
        try {
            val transactionId = call.argument<String>("transactionId")
            if (transactionId.isNullOrEmpty()) {
                Log.e("FCMTopicSubscription", "❌ Transaction ID is required for topic subscription")
                result.error("MISSING_TRANSACTION_ID", "Transaction ID is required", "transactionId parameter is missing or empty")
                return
            }

            val topicName = "$FCM_TOPIC_PREFIX$transactionId"
            Log.d("FCMTopicSubscription", "🔔 Subscribing to FCM topic: $topicName")

            // Store current transaction ID for later unsubscription
            currentTransactionId = transactionId
            subscribedTopics[transactionId] = topicName

            // Subscribe to FCM topic
            firebaseMessaging?.subscribeToTopic(topicName)
                ?.addOnCompleteListener { task ->
                    if (task.isSuccessful) {
                        Log.d("FCMTopicSubscription", "✅ Successfully subscribed to FCM topic: $topicName")
                        result.success(true)
                    } else {
                        Log.e("FCMTopicSubscription", "❌ Failed to subscribe to FCM topic: $topicName", task.exception)
                        result.error("SUBSCRIPTION_FAILED", "Failed to subscribe to FCM topic", task.exception?.message)
                    }
                }

        } catch (e: Exception) {
            Log.e("FCMTopicSubscription", "❌ Error subscribing to charging topic: ${e.message}", e)
            result.error("SUBSCRIPTION_ERROR", "Failed to subscribe to charging topic", e.message)
        }
    }

    /**
     * Unsubscribe from charging FCM topic when charging session completes
     */
    private fun unsubscribeFromChargingTopic(call: MethodCall, result: Result) {
        try {
            val transactionId = call.argument<String>("transactionId") ?: currentTransactionId

            if (transactionId.isNullOrEmpty()) {
                Log.e("FCMTopicSubscription", "❌ Transaction ID is required for topic unsubscription")
                result.error("MISSING_TRANSACTION_ID", "Transaction ID is required", "transactionId parameter is missing or empty")
                return
            }

            val topicName = subscribedTopics[transactionId]
            if (topicName == null) {
                Log.w("FCMTopicSubscription", "⚠️ No active subscription found for transaction ID: $transactionId")
                result.success(true) // Consider it successful if already unsubscribed
                return
            }

            Log.d("FCMTopicSubscription", "🔕 Unsubscribing from FCM topic: $topicName")

            // Unsubscribe from FCM topic
            firebaseMessaging?.unsubscribeFromTopic(topicName)
                ?.addOnCompleteListener { task ->
                    if (task.isSuccessful) {
                        // Remove from subscribed topics
                        subscribedTopics.remove(transactionId)

                        // Clear current transaction ID if it matches
                        if (currentTransactionId == transactionId) {
                            currentTransactionId = null
                        }

                        Log.d("FCMTopicSubscription", "✅ Successfully unsubscribed from FCM topic: $topicName")
                        result.success(true)
                    } else {
                        Log.e("FCMTopicSubscription", "❌ Failed to unsubscribe from FCM topic: $topicName", task.exception)
                        result.error("UNSUBSCRIPTION_FAILED", "Failed to unsubscribe from FCM topic", task.exception?.message)
                    }
                }

        } catch (e: Exception) {
            Log.e("FCMTopicSubscription", "❌ Error unsubscribing from charging topic: ${e.message}", e)
            result.error("UNSUBSCRIPTION_ERROR", "Failed to unsubscribe from charging topic", e.message)
        }
    }

    /**
     * Clean up FCM topic subscriptions when service is destroyed
     */
    override fun onDestroy() {
        try {
            Log.d("FCMTopicSubscription", "🧹 Cleaning up FCM topic subscriptions")

            // Unsubscribe from all active topics
            subscribedTopics.keys.forEach { transactionId ->
                val topicName = subscribedTopics[transactionId]
                Log.d("FCMTopicSubscription", "� Auto-unsubscribing from FCM topic: $topicName")

                firebaseMessaging?.unsubscribeFromTopic(topicName!!)
                    ?.addOnCompleteListener { task ->
                        if (task.isSuccessful) {
                            Log.d("FCMTopicSubscription", "✅ Successfully unsubscribed from topic: $topicName")
                        } else {
                            Log.e("FCMTopicSubscription", "❌ Failed to unsubscribe from topic: $topicName", task.exception)
                        }
                    }
            }

            // Clear all subscriptions
            subscribedTopics.clear()
            currentTransactionId = null

            Log.d("FCMTopicSubscription", "✅ FCM topic subscription cleanup completed")
        } catch (e: Exception) {
            Log.e("FCMTopicSubscription", "❌ Error during FCM subscription cleanup: ${e.message}", e)
        }

        super.onDestroy()
    }
}
