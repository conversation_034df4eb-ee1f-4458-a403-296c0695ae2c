import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';

/// Service to check and monitor network connectivity
class NetworkService {
  // Singleton pattern
  static final NetworkService _instance = NetworkService._internal();
  factory NetworkService() => _instance;
  NetworkService._internal();

  // Connectivity instance
  final Connectivity _connectivity = Connectivity();

  // Stream controller for connectivity changes
  final StreamController<bool> _connectionStatusController =
      StreamController<bool>.broadcast();

  // Stream of connectivity status
  Stream<bool> get connectionStatus => _connectionStatusController.stream;

  // Current connectivity status
  bool _isConnected = true;
  bool get isConnected => _isConnected;

  // Subscription to connectivity changes
  StreamSubscription<ConnectivityResult>? _connectivitySubscription;

  /// Initialize the network service
  Future<void> initialize() async {
    debugPrint('\n=== INITIALIZING NETWORK SERVICE ===');

    // Check initial connectivity
    await checkConnectivity();

    // Listen for connectivity changes
    _connectivitySubscription =
        _connectivity.onConnectivityChanged.listen(_updateConnectionStatus);

    debugPrint(
        'Network service initialized. Initial status: ${_isConnected ? 'Connected' : 'Disconnected'}');
  }

  /// Check current connectivity status
  Future<bool> checkConnectivity() async {
    try {
      final result = await _connectivity.checkConnectivity();
      _updateConnectionStatus(result);
      return _isConnected;
    } catch (e) {
      debugPrint('Error checking connectivity: $e');
      _isConnected = false;
      _connectionStatusController.add(false);
      return false;
    }
  }

  /// Update connection status based on connectivity result
  void _updateConnectionStatus(ConnectivityResult result) {
    final wasConnected = _isConnected;

    // Consider connected if result is not 'none'
    _isConnected = result != ConnectivityResult.none;

    // Only notify if status changed
    if (wasConnected != _isConnected) {
      debugPrint('\n=== NETWORK STATUS CHANGED ===');
      debugPrint(
          'Network is now: ${_isConnected ? 'Connected' : 'Disconnected'}');
      _connectionStatusController.add(_isConnected);
    }
  }

  /// Dispose of resources
  void dispose() {
    _connectivitySubscription?.cancel();
    _connectionStatusController.close();
  }
}
