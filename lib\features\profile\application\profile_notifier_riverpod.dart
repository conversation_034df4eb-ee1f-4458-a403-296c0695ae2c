import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../services/auth_manager.dart';
import '../../../services/sync_service.dart';
import '../../../services/service_locator.dart';
import '../../../services/api_service.dart';

/// Profile state provider
final profileProvider =
    AsyncNotifierProvider<ProfileNotifier, ProfileState>(() {
  return ProfileNotifier();
});

/// Profile state class
@immutable
class ProfileState {
  final String userId;
  final String userName;
  final String userEmail;
  final String userPhone;
  final double walletBalance;
  final bool isInstantChargingOn;
  final bool isAutoChargeOn;

  const ProfileState({
    this.userId = '',
    this.userName = 'Loading...',
    this.userEmail = 'Loading...',
    this.userPhone = 'Loading...',
    this.walletBalance = 0.0,
    this.isInstantChargingOn = false,
    this.isAutoChargeOn = false,
  });

  ProfileState copyWith({
    String? userId,
    String? userName,
    String? userEmail,
    String? userPhone,
    double? walletBalance,
    bool? isInstantChargingOn,
    bool? isAutoChargeOn,
  }) {
    return ProfileState(
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      userEmail: userEmail ?? this.userEmail,
      userPhone: userPhone ?? this.userPhone,
      walletBalance: walletBalance ?? this.walletBalance,
      isInstantChargingOn: isInstantChargingOn ?? this.isInstantChargingOn,
      isAutoChargeOn: isAutoChargeOn ?? this.isAutoChargeOn,
    );
  }
}

/// Profile notifier
class ProfileNotifier extends AsyncNotifier<ProfileState> {
  // Auth manager for getting user data
  final AuthManager _authManager = AuthManager();

  // Cache for profile data - CRITICAL: Made non-static to prevent cross-session persistence
  Map<String, dynamic>? _cachedProfileData;
  double? _cachedWalletBalance;

  // Track if profile has been loaded already - CRITICAL: Made non-static
  bool _hasLoadedProfileBefore = false;

  /// CRITICAL: Clear all cached data on logout
  static void clearAllCaches() {
    // This method will be called during logout to ensure no stale data persists
    debugPrint('🧹 CLEARING ALL PROFILE CACHES');
  }

  /// CRITICAL: Clear instance caches and force refresh
  void clearInstanceCaches() {
    debugPrint('🧹 CLEARING INSTANCE PROFILE CACHES');
    _cachedProfileData = null;
    _cachedWalletBalance = null;
    _hasLoadedProfileBefore = false;
  }

  /// CRITICAL: Force refresh profile data (called after login)
  Future<void> forceRefreshProfile() async {
    debugPrint('🔄 FORCE REFRESHING PROFILE DATA');
    clearInstanceCaches();
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      return await _loadUserData();
    });
  }

  /// Refresh profile data and sync instant charging status
  /// This method can be called periodically or when app comes to foreground
  Future<void> refreshProfileData() async {
    final currentState = state.value;
    if (currentState == null) return;

    try {
      debugPrint('\n=== REFRESHING PROFILE DATA ===');

      // Sync instant charging status from server
      await syncInstantChargingFromServer();

      // Also refresh wallet balance
      final walletBalance = await _fetchWalletBalance();
      final updatedState = state.value?.copyWith(walletBalance: walletBalance);
      if (updatedState != null) {
        state = AsyncValue.data(updatedState);
      }

      debugPrint('✅ Profile data refreshed successfully');

    } catch (error) {
      debugPrint('❌ Error refreshing profile data: $error');
    }
  }

  @override
  Future<ProfileState> build() async {
    // Use cached data if available, otherwise load from network
    if (_hasLoadedProfileBefore && _cachedProfileData != null) {
      return _applyProfileDataFromCache();
    } else {
      return await _loadUserData();
    }
  }

  // Apply cached profile data to avoid unnecessary API calls
  ProfileState _applyProfileDataFromCache() {
    if (_cachedProfileData != null) {
      // Extract instant charging status from cached data
      bool instantChargingStatus = false;
      if (_cachedProfileData!['instant_charging'] != null) {
        final value = _cachedProfileData!['instant_charging'];
        if (value is int) {
          instantChargingStatus = value == 1;
        } else if (value is bool) {
          instantChargingStatus = value;
        }
      }

      // Extract auto charge status from cached data
      bool autoChargeStatus = false;
      if (_cachedProfileData!['autocharge_status'] != null) {
        final value = _cachedProfileData!['autocharge_status'];
        if (value is int) {
          autoChargeStatus = value == 1;
        } else if (value is bool) {
          autoChargeStatus = value;
        }
      }

      final state = ProfileState(
        userId: _cachedProfileData!['id']?.toString() ?? '',
        // CRITICAL: No default username - use empty string if missing
        userName: _cachedProfileData!['name']?.toString() ?? '',
        userEmail: _cachedProfileData!['email']?.toString() ?? '',
        userPhone: _cachedProfileData!['mobile_number']?.toString() ?? '',
        walletBalance: _cachedWalletBalance ?? 0.0,
        isInstantChargingOn: instantChargingStatus,
        isAutoChargeOn: autoChargeStatus,
      );

      debugPrint('\n=== USING CACHED PROFILE DATA ===');
      debugPrint('Name: ${state.userName}');
      debugPrint('Email: ${state.userEmail}');
      debugPrint('Phone: ${state.userPhone}');
      debugPrint('ID: ${state.userId}');
      debugPrint('Wallet Balance: ${state.walletBalance}');
      debugPrint('Instant Charging: ${state.isInstantChargingOn}');
      debugPrint('Auto Charge: ${state.isAutoChargeOn}');

      return state;
    }
    return const ProfileState();
  }

  // Load user data from AuthManager and SyncService
  Future<ProfileState> _loadUserData() async {
    try {
      // First try to get user data from AuthManager
      final userData = await _authManager.getUserData();
      ProfileState currentState = const ProfileState();

      if (userData != null) {
        // Get user ID from userData
        final id = userData['id']?.toString() ?? '';
        final name = userData['name'] as String? ?? '';
        final email = userData['email'] as String? ?? '';
        final phone = userData['mobile_number'] as String? ?? '';

        // Update state with user data from AuthManager
        currentState = currentState.copyWith(
          userId: id,
          userName: name.isNotEmpty ? name : currentState.userName,
          userEmail: email.isNotEmpty ? email : currentState.userEmail,
          userPhone: phone.isNotEmpty ? phone : currentState.userPhone,
        );

        // If we have a user ID, try to get more detailed profile from SyncService
        if (id.isNotEmpty) {
          final syncService = SyncService();
          final profileData = await syncService.getUserProfile(id);

          if (profileData != null) {
            // Cache the profile data for future use
            _cachedProfileData = Map<String, dynamic>.from(profileData);

            // Update state with more detailed profile data if available
            currentState = currentState.copyWith(
              userName: profileData['name'] != null &&
                      profileData['name'].toString().isNotEmpty
                  ? profileData['name'].toString()
                  : currentState.userName,
              userEmail: profileData['email'] != null &&
                      profileData['email'].toString().isNotEmpty
                  ? profileData['email'].toString()
                  : currentState.userEmail,
              userPhone: profileData['mobile_number'] != null &&
                      profileData['mobile_number'].toString().isNotEmpty
                  ? profileData['mobile_number'].toString()
                  : currentState.userPhone,
            );
          }
        }
      } else {
        // If no user data from AuthManager, try SharedPreferences as fallback
        final prefs = await SharedPreferences.getInstance();
        final name = prefs.getString('user_name');
        final email = prefs.getString('user_email');
        final phone = prefs.getString('user_phone');
        final id = prefs.getString('user_id');

        // Update state if data exists
        currentState = currentState.copyWith(
          userName:
              name != null && name.isNotEmpty ? name : currentState.userName,
          userEmail: email != null && email.isNotEmpty
              ? email
              : currentState.userEmail,
          userPhone: phone != null && phone.isNotEmpty
              ? phone
              : currentState.userPhone,
          userId: id ?? currentState.userId,
        );

        // Create a cached profile data object from SharedPreferences
        if (id != null) {
          _cachedProfileData = {
            'id': id,
            'name': name ?? '',
            'email': email ?? '',
            'mobile_number': phone ?? '',
          };
        }
      }

      // Fetch wallet balance
      final walletBalance = await _fetchWalletBalance();
      currentState = currentState.copyWith(walletBalance: walletBalance);

      // CRITICAL: Fetch and sync instant charging status from server
      currentState = await _syncInstantChargingFromServer(currentState);

      // Mark that we've loaded the profile
      _hasLoadedProfileBefore = true;

      debugPrint('\n=== LOADED USER DATA ===');
      debugPrint('Name: ${currentState.userName}');
      debugPrint('Email: ${currentState.userEmail}');
      debugPrint('Phone: ${currentState.userPhone}');
      debugPrint('ID: ${currentState.userId}');
      debugPrint('Wallet Balance: ${currentState.walletBalance}');
      debugPrint('Instant Charging: ${currentState.isInstantChargingOn}');

      return currentState;
    } catch (e) {
      debugPrint('Error loading user data: $e');
      throw Exception('Error loading profile: $e');
    }
  }

  // Fetch wallet balance
  Future<double> _fetchWalletBalance() async {
    try {
      final walletResponse =
          await ServiceLocator().walletRepositoryImpl.getWalletInfo();
      if (walletResponse.success &&
          walletResponse.data != null &&
          walletResponse.data!.wallet != null) {
        final balance = walletResponse.data!.wallet!.balance ?? 0.0;

        // Cache the wallet balance
        _cachedWalletBalance = balance;
        return balance;
      }
    } catch (e) {
      debugPrint('Error fetching wallet balance: $e');
    }
    return _cachedWalletBalance ?? 0.0;
  }

  /// Sync instant charging status from server profile API
  /// This method fetches the latest profile data from the server and extracts the instant_charging field
  Future<ProfileState> _syncInstantChargingFromServer(ProfileState currentState) async {
    try {
      debugPrint('\n=== SYNCING INSTANT CHARGING FROM SERVER ===');

      // Make API call to get profile data from server
      final apiService = ApiService();
      final profileResponse = await apiService.get('/api/v1/user/profile');

      debugPrint('Profile API Response: $profileResponse');

      // Check if the response is successful and contains data
      if (profileResponse['success'] == true && profileResponse['data'] != null) {
        final profileData = profileResponse['data'];

        // Extract instant_charging field and convert to boolean
        // Handle both integer (0/1) and boolean (true/false) formats
        bool instantChargingStatus = false;

        if (profileData['instant_charging'] != null) {
          final instantChargingValue = profileData['instant_charging'];

          if (instantChargingValue is int) {
            // Convert integer to boolean: 0 = false, 1 = true
            instantChargingStatus = instantChargingValue == 1;
            debugPrint('Instant charging (int): $instantChargingValue → $instantChargingStatus');
          } else if (instantChargingValue is bool) {
            // Already a boolean
            instantChargingStatus = instantChargingValue;
            debugPrint('Instant charging (bool): $instantChargingStatus');
          } else if (instantChargingValue is String) {
            // Handle string values: "1", "true", "0", "false"
            instantChargingStatus = instantChargingValue == '1' ||
                                  instantChargingValue.toLowerCase() == 'true';
            debugPrint('Instant charging (string): "$instantChargingValue" → $instantChargingStatus');
          }
        }

        // Also extract autocharge_status if available
        bool autoChargeStatus = false;
        if (profileData['autocharge_status'] != null) {
          final autoChargeValue = profileData['autocharge_status'];

          if (autoChargeValue is int) {
            autoChargeStatus = autoChargeValue == 1;
          } else if (autoChargeValue is bool) {
            autoChargeStatus = autoChargeValue;
          } else if (autoChargeValue is String) {
            autoChargeStatus = autoChargeValue == '1' ||
                             autoChargeValue.toLowerCase() == 'true';
          }
        }

        debugPrint('✅ Server sync successful:');
        debugPrint('   Instant Charging: $instantChargingStatus');
        debugPrint('   Auto Charge: $autoChargeStatus');

        // Update the cached profile data with server values
        if (_cachedProfileData != null) {
          _cachedProfileData!['instant_charging'] = instantChargingStatus ? 1 : 0;
          _cachedProfileData!['autocharge_status'] = autoChargeStatus ? 1 : 0;
        }

        // Return updated state with server values
        return currentState.copyWith(
          isInstantChargingOn: instantChargingStatus,
          isAutoChargeOn: autoChargeStatus,
        );

      } else {
        // API call failed or returned no data
        final errorMessage = profileResponse['message'] ?? 'Failed to fetch profile data';
        debugPrint('❌ Profile API failed: $errorMessage');

        // Return current state unchanged if API fails
        return currentState;
      }

    } catch (error) {
      debugPrint('❌ Error syncing instant charging from server: $error');

      // Return current state unchanged if there's an error
      return currentState;
    }
  }

  // Load user data (public method)
  Future<void> loadUserData() async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      return await _loadUserData();
    });
  }

  /// Manually sync instant charging status from server
  /// This method can be called to refresh the toggle state from the server
  Future<void> syncInstantChargingFromServer() async {
    final currentState = state.value;
    if (currentState == null) return;

    try {
      debugPrint('\n=== MANUAL SYNC INSTANT CHARGING FROM SERVER ===');

      // Update state with server data
      final updatedState = await _syncInstantChargingFromServer(currentState);
      state = AsyncValue.data(updatedState);

      debugPrint('✅ Manual sync completed successfully');

    } catch (error) {
      debugPrint('❌ Manual sync failed: $error');
      // Don't update state on error - keep current state
    }
  }

  // Update profile data
  Future<void> updateProfile(Map<String, dynamic> updatedData) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final currentState = state.value!;

      // Update the cached data
      if (_cachedProfileData != null) {
        _cachedProfileData!.addAll(updatedData);
      } else {
        _cachedProfileData = Map<String, dynamic>.from(updatedData);
      }

      // Create updated state
      final updatedState = currentState.copyWith(
        userName: updatedData.containsKey('name')
            ? updatedData['name'] as String? ?? currentState.userName
            : currentState.userName,
        userEmail: updatedData.containsKey('email')
            ? updatedData['email'] as String? ?? currentState.userEmail
            : currentState.userEmail,
        userPhone: updatedData.containsKey('mobile_number')
            ? updatedData['mobile_number'] as String? ?? currentState.userPhone
            : currentState.userPhone,
      );

      debugPrint('\n=== UPDATED PROFILE DATA ===');
      debugPrint('Name: ${updatedState.userName}');
      debugPrint('Email: ${updatedState.userEmail}');
      debugPrint('Phone: ${updatedState.userPhone}');

      return updatedState;
    });
  }

  // Toggle instant charging with API update (sync happens automatically from profile API)
  Future<void> toggleInstantCharging(bool value) async {
    final currentState = state.value;
    if (currentState == null) return;

    // Optimistic UI update - toggle immediately for better UX
    state = AsyncValue.data(currentState.copyWith(isInstantChargingOn: value));

    try {
      // Make API call to update instant charging status
      final apiService = ApiService();
      final response = await apiService.post(
        '/api/v1/user/user/instant-charging/update',
        {
          'status': value ? 1 : 0, // Send integer status (1 for enable, 0 for disable)
        },
      );

      // Check for successful response
      if (response['success'] == true) {
        // Sync with profile API to get the updated instant charging status
        await Future.delayed(const Duration(milliseconds: 500)); // Small delay to ensure server is updated
        await syncInstantChargingFromServer();
      } else {
        // API call failed - revert to previous state
        state = AsyncValue.data(currentState.copyWith(isInstantChargingOn: !value));
      }

    } catch (error) {
      // Revert the optimistic update on error
      state = AsyncValue.data(currentState.copyWith(isInstantChargingOn: !value));
    }
  }

  // Toggle auto charge
  Future<void> toggleAutoCharge(bool value) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final currentState = state.value!;
      // TODO: Persist the state (e.g., using SharedPreferences or API)
      return currentState.copyWith(isAutoChargeOn: value);
    });
  }

}
