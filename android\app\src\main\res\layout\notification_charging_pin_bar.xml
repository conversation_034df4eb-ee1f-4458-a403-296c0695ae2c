<?xml version="1.0" encoding="utf-8"?>
<!-- Custom notification layout that replicates the charging pin bar design -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/notification_background"
    android:padding="16dp">

    <!-- Top Section: Status, Battery, Timer, Close -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="12dp">

        <!-- Active charging indicator -->
        <View
            android:id="@+id/charging_indicator"
            android:layout_width="8dp"
            android:layout_height="8dp"
            android:background="@drawable/charging_indicator_active"
            android:layout_marginEnd="8dp" />

        <!-- Status text -->
        <TextView
            android:id="@+id/status_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="ACTIVE CHARGING"
            android:textColor="@color/charging_green"
            android:textSize="12sp"
            android:textStyle="bold"
            android:letterSpacing="0.05" />

        <!-- Battery percentage container -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:background="@drawable/battery_percentage_background"
            android:paddingHorizontal="10dp"
            android:paddingVertical="4dp"
            android:gravity="center_vertical"
            android:layout_marginEnd="16dp">

            <!-- Battery icon with progress -->
            <FrameLayout
                android:layout_width="18dp"
                android:layout_height="18dp"
                android:layout_marginEnd="4dp">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:src="@drawable/ic_battery_outline"
                    android:tint="@color/charging_green_light" />

                <ImageView
                    android:id="@+id/battery_fill"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:src="@drawable/ic_battery_fill"
                    android:tint="@color/charging_green"
                    android:scaleType="fitXY" />
            </FrameLayout>

            <!-- Percentage text -->
            <TextView
                android:id="@+id/battery_percentage"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="68%"
                android:textColor="@color/charging_green"
                android:textSize="14sp"
                android:textStyle="bold" />
        </LinearLayout>

        <!-- Duration -->
        <TextView
            android:id="@+id/charging_timer"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="00:45:32"
            android:textColor="@color/text_secondary"
            android:textSize="14sp"
            android:textStyle="normal"
            android:layout_marginEnd="8dp" />

        <!-- Close button -->
        <ImageView
            android:id="@+id/close_button"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:src="@drawable/ic_close"
            android:tint="@color/text_light"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:padding="2dp" />
    </LinearLayout>

    <!-- Progress Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginBottom="12dp">

        <!-- Progress bar with car icon -->
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="20dp"
            android:layout_marginBottom="8dp">

            <!-- Background progress bar -->
            <View
                android:layout_width="match_parent"
                android:layout_height="8dp"
                android:layout_gravity="center_vertical"
                android:background="@drawable/progress_bar_background" />

            <!-- Filled progress bar -->
            <View
                android:id="@+id/progress_fill"
                android:layout_width="0dp"
                android:layout_height="8dp"
                android:layout_gravity="center_vertical"
                android:background="@drawable/progress_bar_fill" />

            <!-- Car icon positioned at progress -->
            <ImageView
                android:id="@+id/car_icon"
                android:layout_width="24dp"
                android:layout_height="20dp"
                android:src="@drawable/ic_car"
                android:background="@drawable/car_icon_background"
                android:padding="3dp"
                android:tint="@android:color/white"
                android:layout_gravity="center_vertical" />
        </FrameLayout>

        <!-- Progress labels -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/current_percentage"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="68%"
                android:textColor="@color/charging_green"
                android:textSize="12sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="100%"
                android:textColor="@color/text_light"
                android:textSize="12sp"
                android:textStyle="normal" />
        </LinearLayout>
    </LinearLayout>

    <!-- Metrics Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="12dp">

        <!-- Power -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:baselineAligned="true"
                android:gravity="bottom">

                <TextView
                    android:id="@+id/power_value"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="150"
                    android:textColor="@color/text_primary"
                    android:textSize="20sp"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="kW"
                    android:textColor="@color/text_secondary"
                    android:textSize="14sp"
                    android:textStyle="normal"
                    android:layout_marginStart="2dp" />
            </LinearLayout>

            <View
                android:layout_width="24dp"
                android:layout_height="3dp"
                android:background="@color/charging_green"
                android:layout_marginTop="4dp"
                android:layout_marginBottom="4dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Power"
                android:textColor="@color/text_light"
                android:textSize="12sp"
                android:textStyle="normal" />
        </LinearLayout>

        <!-- Energy -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:baselineAligned="true"
                android:gravity="bottom">

                <TextView
                    android:id="@+id/energy_value"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="32.4"
                    android:textColor="@color/text_primary"
                    android:textSize="20sp"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="kWh"
                    android:textColor="@color/text_secondary"
                    android:textSize="14sp"
                    android:textStyle="normal"
                    android:layout_marginStart="2dp" />
            </LinearLayout>

            <View
                android:layout_width="24dp"
                android:layout_height="3dp"
                android:background="@color/charging_green"
                android:layout_marginTop="4dp"
                android:layout_marginBottom="4dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Energy"
                android:textColor="@color/text_light"
                android:textSize="12sp"
                android:textStyle="normal" />
        </LinearLayout>

        <!-- Cost -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/cost_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="₹12.80"
                android:textColor="@color/text_primary"
                android:textSize="20sp"
                android:textStyle="bold"
                android:layout_marginBottom="8dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Cost"
                android:textColor="@color/text_light"
                android:textSize="12sp"
                android:textStyle="normal" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="$0.28/kWh"
                android:textColor="@color/text_lighter"
                android:textSize="10sp"
                android:textStyle="normal" />
        </LinearLayout>
    </LinearLayout>

    <!-- Environmental Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Environmental Impact"
            android:textColor="@color/text_secondary"
            android:textSize="14sp"
            android:textStyle="bold"
            android:letterSpacing="0.05"
            android:layout_marginBottom="8dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <!-- Carbon Savings -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:layout_marginEnd="16dp">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:baselineAligned="true"
                    android:gravity="bottom">

                    <TextView
                        android:id="@+id/co2_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="8.2"
                        android:textColor="@color/text_primary"
                        android:textSize="20sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="kg CO2"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp"
                        android:textStyle="normal"
                        android:layout_marginStart="2dp" />
                </LinearLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Carbon Savings"
                    android:textColor="@color/text_light"
                    android:textSize="12sp"
                    android:textStyle="normal"
                    android:layout_marginTop="4dp" />
            </LinearLayout>

            <!-- Renewable Energy -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:baselineAligned="true"
                    android:gravity="bottom">

                    <TextView
                        android:id="@+id/renewable_value"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="95"
                        android:textColor="@color/text_primary"
                        android:textSize="20sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="%"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp"
                        android:textStyle="normal"
                        android:layout_marginStart="2dp" />
                </LinearLayout>

                <View
                    android:layout_width="24dp"
                    android:layout_height="3dp"
                    android:background="@color/charging_green"
                    android:layout_marginTop="4dp"
                    android:layout_marginBottom="4dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Renewable Energy"
                    android:textColor="@color/text_light"
                    android:textSize="12sp"
                    android:textStyle="normal" />
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>
</LinearLayout>
